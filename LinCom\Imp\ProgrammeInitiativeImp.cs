﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
   

    public class ProgrammeInitiativeImp : IPorgrammeInitiative
    {
        int msg, n;

        ProgrammesEtInitiative p = new ProgrammesEtInitiative();
        public int add(ProgrammesEtInitiative_Class add)
        {
            using (Connection con = new Connection())
            {

                p.Nom = add.Nom;
                p.Description = add.Description;
                p.DateLancement = add.DateLancement;
                p.name = add.name;
                p.statut = add.statut;

                try
                {
                    con.ProgrammesEtInitiatives.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.ProgrammesEtInitiatives.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void afficherDetails(int code, ProgrammesEtInitiative_Class pr)
        {
            using (Connection con = new Connection())
            {
                p = con.ProgrammesEtInitiatives.Where(x => x.ProgrammeId == code).FirstOrDefault();

                if (p != null)
                {

                    pr.Nom = p.Nom;
                    pr.Description = p.Description;
                    pr.DateLancement = p.DateLancement;
                    pr.name = p.name;
                    pr.statut = p.statut;

                }

            }
        }

        public void afficherDetails(string code, ProgrammesEtInitiative_Class pr)
        {
            throw new NotImplementedException();
        }

        public void Chargement_GDV(GridView GV_apv)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.ProgrammesEtInitiatives

                           select new
                           {
                               nom = ep.Nom,
                               description = ep.Description,
                               dateLancemnet = ep.DateLancement,

                               name = ep.name,
                               statut = ep.statut,



                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public void chargerProgrammInitiative(DropDownList lst)
        {
            throw new NotImplementedException();
        }

        public int count()
        {

            using (Connection con = new Connection())
            {
                var b = (from l in con.ProgrammesEtInitiatives
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(ProgrammesEtInitiative_Class cl, int id)
        {
            using (Connection con = new Connection())
            {
                p = con.ProgrammesEtInitiatives.Where(x => x.ProgrammeId == id).FirstOrDefault();

                try
                {
                    p.Nom = cl.Nom;
                    p.Description = cl.Description;
                    p.DateLancement = cl.DateLancement;
                    p.name = cl.name;
                    p.statut = cl.statut;


                    if (con.SaveChanges() == 1)
                    {
                        con.ProgrammesEtInitiatives.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }

        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.ProgrammesEtInitiatives

                           select new
                           {
                               nom = ep.Nom,
                               description = ep.Description,
                               dateLancemnet = ep.DateLancement,

                               name = ep.name,
                               statut = ep.statut,

                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public int supprimer(int id)
        {
            using (Connection con = new Connection())
            {

                p = con.ProgrammesEtInitiatives.Where(x => x.ProgrammeId == id).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.ProgrammesEtInitiatives.Attach(p);

                con.ProgrammesEtInitiatives.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }
    }
}