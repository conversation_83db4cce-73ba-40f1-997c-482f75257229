﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="ressources.aspx.cs" Inherits="LinCom.ressources" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
            <!-- Page Title -->
<div class="page-title">
    <div class="heading">
        <div class="container">
            <div class="row d-flex justify-content-center text-center">
                <div class="col-lg-8">
                   <h2 class="display-5 fw-bold">Ressources et Publications</h2>
                    <p class="lead">Explorez nos guides, rapports et études qui soutiennent les initiatives des jeunes leaders.</p>
   </div>
            </div>
        </div>
    </div>
    <nav class="breadcrumbs">
        <div class="container">
            <ol>
                <li><a href="home.aspx">Home</a></li>
                <li><a href="#">Espace FNUAP</a></li>
                <li class="current"><a href="ressources.aspx"></a></li>
            </ol>
        </div>
    </nav>
</div>
<!-- End Page Title -->
   
     <main class="main">

<!-- Filters -->
<section class="filter-bar py-4 bg-white shadow-sm position-sticky top-0 z-2">
  <div class="container d-flex flex-wrap justify-content-center gap-3">
    <select class="form-select rounded-pill px-4" style="max-width: 220px;">
      <option value="">Type de ressource</option>
      <option>Guide</option>
      <option>Rapport</option>
      <option>Étude</option>
    </select>
    <input type="date" class="form-control rounded-pill px-4" style="max-width: 220px;">
    <button class="btn btn-primary rounded-pill px-4 fw-semibold">Filtrer</button>
  </div>
</section>

<!-- Resources Grid -->
<section class="resources py-5 bg-light">
  <div class="container">
    <div class="row g-4">
                 <!-- Carte avec lien PDF -->
<div class="col-md-4 col-lg-4">
  <div class="resource-card shadow rounded-4 overflow-hidden bg-white h-100 d-flex flex-column">
    <div class="resource-thumb position-relative">
      <img src="assets/img/blog/skills.png" class="w-100" style="height: 200px; object-fit: cover;" alt="Guide numérique">
      <span class="badge-type position-absolute top-0 end-0 m-3 bg-primary text-white px-3 py-1 rounded-pill">Guide</span>
    </div>
    <div class="p-4 d-flex flex-column flex-grow-1">
      <h5 class="fw-bold mb-2">Guide de l’Alphabétisation Numérique</h5>
      <p class="text-muted small mb-3">Un guide pratique pour renforcer les compétences numériques de base.</p>
      
                <!-- Lien de consultation -->
<a href="assets/pdf/Lincom.pdf" target="_blank" class="btn btn-outline-success mt-auto w-100 rounded-pill fw-semibold d-flex align-items-center justify-content-center gap-2">
  <i class="bi bi-file-earmark-pdf-fill fs-5"></i> Consulter le PDF
</a>
        
      <!-- Auteur -->
      <div class="d-flex align-items-center mt-4 pt-3 border-top">
        <img src="assets/img/blog/skills.png" class="rounded-circle me-3" width="48" height="48" alt="Auteur">
        <div>
          <p class="mb-0 fw-semibold">Sophie Martin</p>
          <small class="text-muted">10 Février 2024</small>
        </div>
      </div>
    </div>
  </div>
</div>

      <!-- Resource Card -->
      <div class="col-md-6 col-lg-4">
        <div class="resource-card shadow rounded-4 overflow-hidden bg-white h-100">
          <div class="resource-thumb position-relative">
            <img src="assets/img/blog/skills.png" class="w-100" style="height: 200px; object-fit: cover;" alt="Guide numérique">
            <span class="badge-type position-absolute top-0 end-0 m-3 bg-primary text-white px-3 py-1 rounded-pill">Guide</span>
          </div>
          <div class="p-4 d-flex flex-column">
            <h5 class="fw-bold mb-2">Guide de l’Alphabétisation Numérique</h5>
            <p class="text-muted small mb-3">Un guide pratique pour renforcer les compétences numériques de base.</p>
                            <!-- Lien de consultation -->
<a href="assets/pdf/Lincom.pdf" target="_blank" class="btn btn-outline-success mt-auto w-100 rounded-pill fw-semibold d-flex align-items-center justify-content-center gap-2">
  <i class="bi bi-file-earmark-pdf-fill fs-5"></i> Consulter le PDF
</a>
              <div class="d-flex align-items-center mt-auto pt-3 border-top">
              <img src="assets/img/blog/skills.png" class="rounded-circle me-3" width="48" height="48" alt="Auteur">
              <div>
                <p class="mb-0 fw-semibold">Sophie Martin</p>
                <small class="text-muted">10 Février 2024</small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Resource Card -->
      <div class="col-md-6 col-lg-4">
        <div class="resource-card shadow rounded-4 overflow-hidden bg-white h-100">
          <div class="resource-thumb position-relative">
            <img src="assets/img/blog/skills.png" class="w-100" style="height: 200px; object-fit: cover;" alt="Rapport zéro déchet">
            <span class="badge-type position-absolute top-0 end-0 m-3 bg-success text-white px-3 py-1 rounded-pill">Rapport</span>
          </div>
          <div class="p-4 d-flex flex-column">
            <h5 class="fw-bold mb-2">Rapport sur l’Initiative Zéro Déchet</h5>
            <p class="text-muted small mb-3">Analyse des résultats obtenus dans les quartiers urbains.</p>
                           <!-- Lien de consultation -->
<a href="assets/pdf/Lincom.pdf" target="_blank" class="btn btn-outline-success mt-auto w-100 rounded-pill fw-semibold d-flex align-items-center justify-content-center gap-2">
  <i class="bi bi-file-earmark-pdf-fill fs-5"></i> Consulter le PDF
</a>
              <div class="d-flex align-items-center mt-auto pt-3 border-top">
              <img src="assets/img/blog/skills.png" class="rounded-circle me-3" width="48" height="48" alt="Auteur">
              <div>
                <p class="mb-0 fw-semibold">Alex Dupont</p>
                <small class="text-muted">5 Novembre 2023</small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Resource Card -->
      <div class="col-md-6 col-lg-4">
        <div class="resource-card shadow rounded-4 overflow-hidden bg-white h-100">
          <div class="resource-thumb position-relative">
            <img src="assets/img/blog/skills.png" class="w-100" style="height: 200px; object-fit: cover;" alt="Étude santé mentale">
            <span class="badge-type position-absolute top-0 end-0 m-3 bg-warning text-dark px-3 py-1 rounded-pill">Étude</span>
          </div>
          <div class="p-4 d-flex flex-column">
            <h5 class="fw-bold mb-2">Étude sur la santé mentale des jeunes</h5>
            <p class="text-muted small mb-3">Données et recommandations à l’ère du numérique.</p>
                           <!-- Lien de consultation -->
<a href="assets/pdf/Lincom.pdf" target="_blank" class="btn btn-outline-success mt-auto w-100 rounded-pill fw-semibold d-flex align-items-center justify-content-center gap-2">
  <i class="bi bi-file-earmark-pdf-fill fs-5"></i> Consulter le PDF
</a>
              
              <div class="d-flex align-items-center mt-auto pt-3 border-top">
              <img src="assets/img/blog/skills.png" class="rounded-circle me-3" width="48" height="48" alt="Auteur">
              <div>
                <p class="mb-0 fw-semibold">Marie Leclerc</p>
                <small class="text-muted">28 Mars 2024</small>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</section>
         
<!-- Pagination -->
<section class="pagination-section py-4 bg-white">
  <div class="container d-flex justify-content-center">
    <nav>
      <ul class="pagination pagination-rounded gap-2 mb-0">
        <li class="page-item"><a class="page-link" href="#"><i class="bi bi-chevron-left"></i></a></li>
        <li class="page-item active"><a class="page-link" href="#">1</a></li>
        <li class="page-item"><a class="page-link" href="#">2</a></li>
        <li class="page-item"><a class="page-link" href="#">3</a></li>
        <li class="page-item disabled"><span class="page-link">...</span></li>
        <li class="page-item"><a class="page-link" href="#">5</a></li>
        <li class="page-item"><a class="page-link" href="#"><i class="bi bi-chevron-right"></i></a></li>
      </ul>
    </nav>
  </div>
</section>

</main>
<!-- Custom Styles -->
<style>
  .page-header {
    background: linear-gradient(135deg, #2e8b57, #1e4d2b);
  }

  .badge-type {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.03em;
  }

  .resource-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .resource-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  }

  .pagination .page-link {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
  }

  .pagination .active .page-link {
    background-color: #2e8b57;
    border-color: #2e8b57;
    color: #fff;
  }

  .filter-bar {
    z-index: 999;
  }
</style>

</asp:Content>
