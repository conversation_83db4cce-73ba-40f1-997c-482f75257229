﻿using LinCom.Class;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listfaq : System.Web.UI.Page
    {
        FAQ_Class faqObj = new FAQ_Class();
        FAQImp objFaq = new FAQImp();

        long ide;
        int rolid;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {
                    ide = Convert.ToInt64(idperso.Value);
                    rolid = Convert.ToInt32(role.Value);
                }
                else
                {
                    Response.Redirect("~/login.aspx");
                }

                InitialiserMessages();
                ChargerListeFAQ();
            }
        }

        private void InitialiserMessages()
        {
            div_msg_success.Visible = false;
            div_msg_error.Visible = false;
        }

        private void ChargerListeFAQ()
        {
            try
            {
                objFaq.ChargerGridView(gdv);
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors du chargement des FAQ : " + ex.Message;
            }
        }

        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                int faqId = Convert.ToInt32(e.CommandArgument);

                if (e.CommandName == "view")
                {
                    // Redirection vers la page de modification
                    Response.Redirect("faq.aspx?id=" + faqId);
                }
                else if (e.CommandName == "delete")
                {
                    // Suppression de la FAQ
                    SupprimerFAQ(faqId);
                }
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de l'opération : " + ex.Message;
            }
        }

        private void SupprimerFAQ(int faqId)
        {
            try
            {
                int resultat = objFaq.Supprimer(faqId);

                if (resultat == 1)
                {
                    div_msg_success.Visible = true;
                    msg_success.InnerText = "FAQ supprimée avec succès";
                    ChargerListeFAQ(); // Recharger la liste
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de la suppression de la FAQ";
                }
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de la suppression : " + ex.Message;
            }
        }
    }
}