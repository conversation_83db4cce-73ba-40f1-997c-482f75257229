<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="mentorat.aspx.cs" Inherits="LinCom.mentorat" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Titre de la page -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2 class="display-5 fw-bold">Programme de Mentorat</h2>
                        <p class="lead">Rejoignez notre communauté en tant que mentor ou mentoré et développez vos compétences et votre réseau.</p>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Accueil</a></li>
                    <li class="current">Mentorat</li>
                </ol>
            </div>
        </nav>
    </div>

    <main class="main">
        <!-- Section Hero Mentorat -->
        <section class="mentorat-hero section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="hero-content">
                            <h3>Développez votre potentiel</h3>
                            <p>Notre programme de mentorat connecte les jeunes leaders avec des mentors expérimentés pour un accompagnement personnalisé vers le succès.</p>
                            <div class="hero-stats">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="stat-item">
                                            <h4 id="nbMentors" runat="server">0</h4>
                                            <p>Mentors actifs</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-item">
                                            <h4 id="nbMentores" runat="server">0</h4>
                                            <p>Mentorés</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stat-item">
                                            <h4 id="nbSessions" runat="server">0</h4>
                                            <p>Sessions réalisées</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="hero-image">
                            <img src="assets/img/mentorat-hero.jpg" alt="Programme de Mentorat" class="img-fluid rounded">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section Programmes de Mentorat -->
        <section class="programmes-section section">
            <div class="container">
                <div class="section-title text-center">
                    <h2>Nos Programmes de Mentorat</h2>
                    <p>Découvrez nos différents programmes adaptés à vos besoins et objectifs</p>
                </div>
                
                <div class="row">
                    <asp:ListView ID="listProgrammes" runat="server">
                        <EmptyItemTemplate>
                            <div class="col-12">
                                <div class="alert alert-info text-center">
                                    <i class="bi bi-info-circle"></i>
                                    Aucun programme de mentorat disponible pour le moment.
                                </div>
                            </div>
                        </EmptyItemTemplate>
                        <ItemTemplate>
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="programme-card">
                                    <div class="card h-100 shadow-sm">
                                        <div class="card-body">
                                            <div class="programme-icon mb-3">
                                                <i class="bi bi-lightbulb display-4 text-primary"></i>
                                            </div>
                                            <h5 class="card-title"><%# HttpUtility.HtmlEncode(Eval("Nom")) %></h5>
                                            <p class="card-text"><%# HttpUtility.HtmlEncode(Eval("Description")) %></p>
                                            <div class="programme-meta">
                                                <small class="text-muted">
                                                    <i class="bi bi-calendar3"></i>
                                                    Durée: <%# HttpUtility.HtmlEncode(Eval("Duree")) %>
                                                </small>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-transparent">
                                            <a href="#" class="btn btn-outline-primary btn-sm">En savoir plus</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>
                </div>
            </div>
        </section>

        <!-- Section Mentors Disponibles -->
        <section class="mentors-section section bg-light">
            <div class="container">
                <div class="section-title text-center">
                    <h2>Nos Mentors Expérimentés</h2>
                    <p>Rencontrez nos mentors qui sont prêts à partager leur expertise avec vous</p>
                </div>
                
                <div class="row">
                    <asp:ListView ID="listMentors" runat="server">
                        <EmptyItemTemplate>
                            <div class="col-12">
                                <div class="alert alert-info text-center">
                                    <i class="bi bi-info-circle"></i>
                                    Aucun mentor disponible pour le moment.
                                </div>
                            </div>
                        </EmptyItemTemplate>
                        <ItemTemplate>
                            <div class="col-lg-3 col-md-6 mb-4">
                                <div class="mentor-card">
                                    <div class="card h-100 text-center">
                                        <div class="card-body">
                                            <div class="mentor-avatar mb-3">
                                                <img src='<%# !string.IsNullOrEmpty(Eval("photo").ToString()) ? "file/membre/" + Eval("photo") : "assets/img/default-avatar.png" %>' 
                                                     alt="<%# HttpUtility.HtmlEncode(Eval("NomComplet")) %>" 
                                                     class="rounded-circle" width="80" height="80">
                                            </div>
                                            <h6 class="card-title"><%# HttpUtility.HtmlEncode(Eval("NomComplet")) %></h6>
                                            <p class="card-text">
                                                <small class="text-primary"><%# HttpUtility.HtmlEncode(Eval("DomaineExpertise")) %></small>
                                            </p>
                                            <div class="mentor-status">
                                                <span class="badge bg-success">Disponible</span>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-transparent">
                                            <button class="btn btn-primary btn-sm" onclick="demanderMentorat(<%# Eval("MentorId") %>)">
                                                <i class="bi bi-person-plus"></i> Demander un mentorat
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ItemTemplate>
                    </asp:ListView>
                </div>
            </div>
        </section>

        <!-- Section Comment ça marche -->
        <section class="how-it-works section">
            <div class="container">
                <div class="section-title text-center">
                    <h2>Comment ça marche ?</h2>
                    <p>Suivez ces étapes simples pour commencer votre parcours de mentorat</p>
                </div>
                
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="step-card text-center">
                            <div class="step-number">1</div>
                            <div class="step-icon">
                                <i class="bi bi-person-plus display-4 text-primary"></i>
                            </div>
                            <h5>Inscription</h5>
                            <p>Créez votre profil et définissez vos objectifs de mentorat</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="step-card text-center">
                            <div class="step-number">2</div>
                            <div class="step-icon">
                                <i class="bi bi-search display-4 text-primary"></i>
                            </div>
                            <h5>Matching</h5>
                            <p>Nous vous mettons en relation avec le mentor idéal</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="step-card text-center">
                            <div class="step-number">3</div>
                            <div class="step-icon">
                                <i class="bi bi-calendar-check display-4 text-primary"></i>
                            </div>
                            <h5>Sessions</h5>
                            <p>Planifiez et participez à vos sessions de mentorat</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="step-card text-center">
                            <div class="step-number">4</div>
                            <div class="step-icon">
                                <i class="bi bi-trophy display-4 text-primary"></i>
                            </div>
                            <h5>Succès</h5>
                            <p>Atteignez vos objectifs et développez vos compétences</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section CTA -->
        <section class="cta-section section bg-primary text-white">
            <div class="container text-center">
                <h2>Prêt à commencer votre parcours de mentorat ?</h2>
                <p class="lead">Rejoignez notre communauté et bénéficiez d'un accompagnement personnalisé</p>
                <div class="cta-buttons">
                    <a href="register.aspx" class="btn btn-light btn-lg me-3">
                        <i class="bi bi-person-plus"></i> Devenir Mentoré
                    </a>
                    <a href="register.aspx" class="btn btn-outline-light btn-lg">
                        <i class="bi bi-person-check"></i> Devenir Mentor
                    </a>
                </div>
            </div>
        </section>
    </main>

    <style>
        .mentorat-hero {
            padding: 60px 0;
        }
        
        .hero-stats .stat-item {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .hero-stats h4 {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--accent-color);
            margin-bottom: 5px;
        }
        
        .programme-card .card {
            transition: transform 0.3s ease;
        }
        
        .programme-card .card:hover {
            transform: translateY(-5px);
        }
        
        .mentor-card .mentor-avatar img {
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .step-card {
            position: relative;
            padding: 30px 20px;
        }
        
        .step-number {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            background: var(--accent-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .cta-section {
            padding: 80px 0;
        }
        
        .cta-buttons .btn {
            margin: 10px;
        }
    </style>

    <script>
        function demanderMentorat(mentorId) {
            // Rediriger vers la page de contact ou d'inscription
            window.location.href = 'contact.aspx?mentor=' + mentorId;
        }
    </script>
</asp:Content>
