﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="communiquer.aspx.cs" Inherits="LinCom.communiquer" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
   
            <!-- Page Title -->
<div class="page-title">
    <div class="heading">
        <div class="container">
            <div class="row d-flex justify-content-center text-center">
                <div class="col-lg-8">
                  <h1 class="display-4 fw-bold">Communiqués Officiels</h1>
<p class="lead mt-3">Dernières annonces, mises à jour et informations importantes pour la communauté.</p>
  </div>
            </div>
        </div>
    </div>
    <nav class="breadcrumbs">
        <div class="container">
            <ol>
                <li><a href="home.aspx">Home</a></li>
                <li><a href="#">Espace FNUAP</a></li>
                <li class="current"><a href="ong.aspx">Communiqués</a></li>
            </ol>
        </div>
    </nav>
</div>
<!-- End Page Title -->

     <main class="main">
<!-- COMMUNIQUÉS EN GRILLE -->
<section class="communique-grid py-5 bg-white">
  <div class="container">
      <!-- FILTRES -->
<section class="filters bg-light py-4 sticky-top shadow-sm z-2">
  <div class="container d-flex flex-wrap gap-3 justify-content-center align-items-center">
    <div class="d-flex align-items-center gap-2">
      <i class="bi bi-funnel-fill text-primary"></i>
      <select class="form-select rounded-pill px-4" style="max-width: 220px;">
        <option>Toutes catégories</option>
        <option>Annonce</option>
        <option>Mise à jour</option>
        <option>Événement</option>
      </select>
    </div>
    <div class="d-flex align-items-center gap-2">
      <i class="bi bi-calendar-date text-primary"></i>
      <input type="date" class="form-control rounded-pill px-4" style="max-width: 220px;">
    </div>
    <button class="btn btn-outline-primary rounded-pill px-4 fw-bold"><i class="bi bi-search"></i> Filtrer</button>
  </div>
</section>

    <div class="row g-4">

      <!-- TEMPLATE DE CARTE DE COMMUNIQUÉ -->
      <div class="col-md-6 col-lg-4">
        <div class="card border-0 shadow-lg h-100 rounded-4 overflow-hidden">
          <img src="assets/img/blog/skills.png" class="card-img-top" style="height: 220px; object-fit: cover;" alt="Communiqué">
          <div class="card-body d-flex flex-column">
            <h5 class="card-title fw-bold">Mise à jour sur la programmation des ateliers</h5>
            <p class="card-text text-muted small">Découvrez les nouvelles dates et modalités pour les ateliers du trimestre à venir.</p>
            <div class="mt-auto d-flex justify-content-between align-items-center text-primary fw-semibold mb-3">
              <span><i class="bi bi-calendar-event"></i> 10 mai 2025</span>
              <span><i class="bi bi-tags-fill"></i> Mise à jour</span>
            </div>
            <a href="#" class="btn btn-primary w-100 rounded-pill fw-semibold"><i class="bi bi-arrow-right-circle me-1"></i> Lire plus</a>
          </div>
        </div>
      </div>

      <!-- Réplique ce bloc pour chaque communiqué -->

    </div>
  </div>
</section>

<!-- PAGINATION -->
<section class="pagination py-4 bg-light">
  <div class="container d-flex justify-content-center">
    <nav>
      <ul class="pagination gap-2">
        <li class="page-item"><a class="page-link shadow-sm" href="#"><i class="bi bi-chevron-left"></i></a></li>
        <li class="page-item active"><a class="page-link shadow-sm" href="#">1</a></li>
        <li class="page-item"><a class="page-link shadow-sm" href="#">2</a></li>
        <li class="page-item"><a class="page-link shadow-sm" href="#">3</a></li>
        <li class="page-item"><a class="page-link shadow-sm" href="#"><i class="bi bi-chevron-right"></i></a></li>
      </ul>
    </nav>
  </div>
</section>
</main>
<!-- STYLES SUPPLÉMENTAIRES -->
<style>
  .hero-section {
    min-height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .card:hover {
    transform: translateY(-6px);
    transition: all 0.3s ease;
  }

  .pagination .page-link {
    border-radius: 50%;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pagination .active .page-link {
    background-color: #0d6efd;
    color: white;
    border-color: transparent;
  }
</style>

</asp:Content>
