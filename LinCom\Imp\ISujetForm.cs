﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ISujetForm
    {
        // Méthodes CRUD de base (compatibilité LinCom)
        int add(SujetForum_Class add);
        void Chargement_GDV(GridView GV_apv);
        void search(GridView GV_apv, string code);
        void afficherDetails(int code, SujetForum_Class pr);
        void afficherDetails(string code, SujetForum_Class pr);
        int edit(SujetForum_Class cl, int id);
        int supprimer(int id);
        void chargerForum(DropDownList lst);
        int count();

        // Méthodes Stack Overflow style - Questions
        int CreerQuestion(SujetForum_Class question);
        int CreerReponse(SujetForum_Class reponse, long questionId);
        void ChargerQuestions(ListView lv, string filtre = "recent", long? categoryId = null);
        void ChargerQuestionsUtilisateur(ListView lv, long userId, string filtre = "recent");
        void ChargerQuestionAvecReponses(long questionId, SujetForum_Class question, ListView lvReponses);

        // Gestion des réponses
        void ChargerReponses(ListView lv, long questionId);
        void MarquerReponseAcceptee(long questionId, long reponseId, long userId);
        void SupprimerReponseAcceptee(long questionId, long userId);
        SujetForum_Class ObtenirMeilleureReponse(long questionId);
        int CompterReponses(long questionId);

        // Filtrage et tri Stack Overflow
        void ChargerQuestionsNonRepondues(ListView lv, long? categoryId = null, int limite = 20);
        void ChargerQuestionsPopulaires(ListView lv, string periode = "week", int limite = 20);
        void ChargerQuestionsTendance(ListView lv, int limite = 10);
        void ChargerQuestionsRecentes(ListView lv, long? categoryId = null, int limite = 20);
        void ChargerQuestionsAvecBounty(ListView lv, int limite = 20);

        // Recherche avancée
        void RechercherQuestions(ListView lv, string terme, long? categoryId = null, List<string> tags = null);
        void ChargerQuestionsSimilaires(ListView lv, long questionId, int limite = 5);
        void ChargerQuestionsLiees(ListView lv, long questionId, int limite = 5);
        void ChargerQuestionsParTags(ListView lv, List<string> tags, string operateur = "OR");

        // Gestion des vues
        void AjouterVue(long questionId, long? userId = null, string ipAddress = null);
        int ObtenirNombreVues(long questionId);
        void ChargerVuesRecentes(ListView lv, long questionId, int limite = 10);
        bool EstVueUnique(long questionId, long? userId, string ipAddress);

        // Statuts et états
        void FermerQuestion(long questionId, long userId, string raison);
        void ReouvriQuestion(long questionId, long userId);
        void EpinglerQuestion(long questionId, bool epingler = true);
        void MarquerCommeFeatured(long questionId, bool featured = true);
        void VerrouillerQuestion(long questionId, bool verrouiller = true);

        // Édition collaborative
        void ModifierQuestion(long questionId, SujetForum_Class modifications, long editeurId);
        void ModifierReponse(long reponseId, SujetForum_Class modifications, long editeurId);
        void ChargerHistoriqueModifications(ListView lv, long postId);
        void AnnulerModification(long postId, long revisionId);

        // Tags et catégorisation
        void MettreAJourTags(long questionId, List<string> tags);
        List<string> ObtenirTags(long questionId);
        void ChangerCategorie(long questionId, long nouvelleCategoryId);
        void ChargerQuestionsParCategorie(ListView lv, long categoryId, string filtre = "recent");

        // Statistiques et analytics
        void ChargerStatistiquesUtilisateur(long userId, out int questions, out int reponses, out int vues);
        void ChargerActiviteRecente(ListView lv, long userId, int limite = 10);
        void ChargerTopContributeurs(ListView lv, string periode = "month", int limite = 10);
        void ChargerStatistiquesGlobales(out int totalQuestions, out int totalReponses, out double tauxReponse);

        // Modération
        void SupprimerPost(long postId, long moderateurId, string raison);
        void RestaurerPost(long postId, long moderateurId);
        void ChargerPostsSupprimes(ListView lv, int limite = 20);
        void ChargerPostsSignales(ListView lv, int limite = 20);

        // Bounty et récompenses
        void DemarrerBounty(long questionId, long userId, int montant);
        void AttribuerBounty(long questionId, long reponseId, long userId);
        void ChargerBountiesActifs(ListView lv);
        bool PeutDemarrerBounty(long questionId, long userId);

        // Export et partage
        string GenererLienPartage(long postId);
        void ExporterQuestion(long questionId, string format = "html");
        void ChargerQuestionsExportees(ListView lv, long userId);
    }
}
