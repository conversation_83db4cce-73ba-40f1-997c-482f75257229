﻿using LinCom.Class;
using LinCom.file;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class AvisRessourceImp : IAvisRessource
    {
        int msg;
        private AvisRessource avis = new AvisRessource();

        public int Ajout(AvisRessource_Class add)
        {
            using (Connection con = new Connection())
            {
                avis.RessourceId = add.RessourceId;
                avis.MembreId = add.MembreId;
                avis.Note = add.Note;
                avis.Commentaire = add.Commentaire;
                avis.DateAvis = DateTime.Now;


                try
                {
                    con.AvisRessources.Add(avis);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void afficherDetails(int code, AvisRessource_Class pr)
        {
            using (Connection con = new Connection())
            {
                var a = con.AvisRessources.FirstOrDefault(x => x.AvisId == code);
                if (a != null)
                {
                    pr.AvisId = a.AvisId;
                    pr.RessourceId = a.RessourceId;
                    pr.MembreId = a.MembreId;
                    pr.Note = a.Note;
                    pr.Commentaire = a.Commentaire;
                    pr.DateAvis = a.DateAvis;

                }
            }
        }

        public void Chargement_GDV(GridView gv)
        {
            using (Connection con = new Connection())
            {

                var obj = (from avr in con.AvisRessources
                           select new
                           {
                               RessourceId =avr.RessourceId,
                               note = avr.Note,
                               comment=avr.Commentaire,
                               dateAvis=avr.DateAvis,

                           }).ToList();

                gv.DataSource = obj;
                gv.DataBind();

            }
        }

        public void Chargement_GDV_Membre(GridView GV_apv, int code)
        {
            using (Connection con = new Connection())
            {
                var query = from a in con.AvisRessources
                            join r in con.Ressources on a.RessourceId equals r.RessourceId
                            where a.MembreId == code
                            select new
                            {
                                a.AvisId,
                                r.Titre,
                                a.Note,
                                a.Commentaire,
                                a.DateAvis,

                            };

                GV_apv.DataSource = query
                    .OrderByDescending(x => x.DateAvis)
                    .ToList();
                GV_apv.DataBind();
            }
        }




        public void chargerAvis_Membre(DropDownList lst, int code)
        {
            using (Connection con = new Connection())
            {
                var query = from a in con.AvisRessources
                            join m in con.Membres on a.MembreId equals m.MembreId
                            where a.RessourceId == code
                            select new
                            {
                                a.AvisId,
                                Auteur = m.Nom + " " + m.Prenom,
                                a.Note,
                                a.Commentaire,
                                a.DateAvis,
                            };

                lst.DataSource = query
                    .OrderByDescending(x => x.DateAvis)
                    .ToList();
                lst.DataTextField = "Auteur";
                lst.DataValueField = "AvisId";
                lst.DataBind();
            }
        }

        public void chargerAvis(DropDownList ddw)
        {
            ddw.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.AvisRessources select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    ddw.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner l'Avis de Ressource";
                    ddw.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.RessourceId.ToString();
                        item.Text = data.Commentaire;
                        item.Text = data.Note.ToString();
                        ddw.Items.Add(item);
                    }

                }
                else
                {
                    ddw.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    ddw.Items.Add(item0);
                }

            }
        }


        public int count()
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                var b = (from l in con.AvisRessources
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(AvisRessource_Class cl)
        {
            using (Connection con = new Connection())
            {
                var a = con.AvisRessources.FirstOrDefault(x =>
                    x.AvisId == cl.AvisId &&
                    x.MembreId == cl.MembreId); // Vérifier que c'est bien l'auteur

                if (a != null)
                {
                    a.Note = cl.Note;
                    a.Commentaire = cl.Commentaire;
                    a.DateAvis = DateTime.Now;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.AvisRessources
                           where (ep.Commentaire.Contains(code))
                           select new
                           {
                               AvisRessourceId= ep.AvisId,
                               MembreId= ep.MembreId,
                               Note=ep.Note,
                               comment=ep.Commentaire,
                            

                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }

        }

        public int supprimer(int id)
        {
            using (Connection con = new Connection())
            {
                var a = con.AvisRessources.FirstOrDefault(x => x.AvisId == id);
                if (a != null)
                {
                    con.AvisRessources.Remove(a);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}