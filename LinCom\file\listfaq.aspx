﻿<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="listfaq.aspx.cs" Inherits="LinCom.file.listfaq" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">
                                    <div class="breadcomb-icon">
                                        <i class="notika-icon notika-form"></i>
                                    </div>
                                    <div class="breadcomb-ctn">
                                        <h2>Liste des FAQ</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">
                                    <a data-toggle="tooltip" data-placement="left" href="faq.aspx" title="Ajouter une nouvelle FAQ" class="btn">Nouvelle FAQ</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <div id="div_msg_success" runat="server" visible="false" class="alert alert-success alert-dismissible fade show" role="alert">
        <span id="msg_success" runat="server"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <div id="div_msg_error" runat="server" visible="false" class="alert alert-danger alert-dismissible fade show" role="alert">
        <span id="msg_error" runat="server"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
	  <!-- Data Table area Start-->
 <div class="normal-table-area">
      <div class="container">
         <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
               <div class="data-table-list">
                   <div class="table-responsive">
                       <asp:GridView ID="gdv" class="table table-striped" runat="server" CssClass="datatbemp"
                         AutoGenerateColumns="False" EmptyDataText="Aucune FAQ trouvée"
                         GridLines="None" width="100%" OnRowCommand="gdv_RowCommand">
                            <AlternatingRowStyle BackColor="#DCDCDC" />
                            <Columns>
                                <asp:TemplateField HeaderText="Question" FooterText="Question">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_question" runat="server" Text='<%# Eval("Question") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Réponse" FooterText="Réponse">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_reponse" runat="server" Text='<%# Eval("Reponse").ToString().Length > 100 ? Eval("Reponse").ToString().Substring(0, 100) + "..." : Eval("Reponse") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Statut" FooterText="Statut">
                                    <ItemTemplate>
                                        <span class="badge badge-<%# Eval("statut").ToString() == "publié" ? "success" : (Eval("statut").ToString() == "brouillon" ? "warning" : "secondary") %>">
                                            <%# Eval("statut") %>
                                        </span>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Date Publication" FooterText="Date Publication">
                                    <ItemTemplate>
                                        <asp:Label ID="lb_date" runat="server" Text='<%# Eval("DatePublication", "{0:dd/MM/yyyy}") %>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Action" FooterText="Action">
                                    <ItemTemplate>
                                        <asp:Button ID="btnEdit" Height="100px" BackColor="Green" class="btn btn-info btn-fix" CommandName="view" CommandArgument='<%# Eval("id") %>' runat="server" Text="Edit" />
                                        <asp:Button class="btn btn-danger notika-btn-danger" BackColor="Red" ID="btn_del" runat="server" Text="Delete" CommandName="delete" CommandArgument='<%# Eval("id") %>' OnClientClick=" return confirm('Voulez-Vous vraiment supprimer??')" />
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                            <EditRowStyle Font-Bold="True"></EditRowStyle>
                            <EmptyDataRowStyle HorizontalAlign="Center" Font-Names="century" Font-Size="X-Large"></EmptyDataRowStyle>
                       </asp:GridView>
                   </div>
               </div>
           </div>
         </div>
     </div>
 </div>
 <!-- Data Table area End-->

      <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.js"></script>
   <script>
       $(document).ready(function () {
           // Attendre que tous les autres scripts soient chargés
           setTimeout(function() {
               try {
                   // Vérifier si le tableau existe
                   if ($(".datatbemp").length > 0) {
                       // Créer l'en-tête si nécessaire
                       var $table = $(".datatbemp");
                       if ($table.find("thead").length === 0) {
                           $table.prepend($("<thead></thead>").append($table.find("tr:first").clone()));
                       }

                       // Initialiser DataTable avec configuration sécurisée
                       $table.DataTable({
                           "language": {
                               "url": "//cdn.datatables.net/plug-ins/1.11.3/i18n/French.json"
                           },
                           "pageLength": 10,
                           "responsive": true,
                           "destroy": true // Permet de réinitialiser si déjà initialisé
                       });
                   }
               } catch (e) {
                   console.log("DataTables initialization error:", e);
               }
           }, 500);
       });
   </script>
</asp:Content>
                                 <tr>
                                     <td>Jennifer Chang</td>
                                     <td>Regional Director</td>
                                     <td>Singapore</td>
                                     <td>28</td>
                                     <td>2010/11/14</td>
                                     <td>$357,650</td>
                                 </tr>
                                 <tr>
                                     <td>Brenden Wagner</td>
                                     <td>Software Engineer</td>
                                     <td>San Francisco</td>
                                     <td>28</td>
                                     <td>2011/06/07</td>
                                     <td>$206,850</td>
                                 </tr>
                                 <tr>
                                     <td>Fiona Green</td>
                                     <td>Chief Operating Officer (COO)</td>
                                     <td>San Francisco</td>
                                     <td>48</td>
                                     <td>2010/03/11</td>
                                     <td>$850,000</td>
                                 </tr>
                                 <tr>
                                     <td>Shou Itou</td>
                                     <td>Regional Marketing</td>
                                     <td>Tokyo</td>
                                     <td>20</td>
                                     <td>2011/08/14</td>
                                     <td>$163,000</td>
                                 </tr>
                                 <tr>
                                     <td>Michelle House</td>
                                     <td>Integration Specialist</td>
                                     <td>Sidney</td>
                                     <td>37</td>
                                     <td>2011/06/02</td>
                                     <td>$95,400</td>
                                 </tr>
                                 <tr>
                                     <td>Suki Burks</td>
                                     <td>Developer</td>
                                     <td>London</td>
                                     <td>53</td>
                                     <td>2009/10/22</td>
                                     <td>$114,500</td>
                                 </tr>
                                 <tr>
                                     <td>Prescott Bartlett</td>
                                     <td>Technical Author</td>
                                     <td>London</td>
                                     <td>27</td>
                                     <td>2011/05/07</td>
                                     <td>$145,000</td>
                                 </tr>
                                 <tr>
                                     <td>Gavin Cortez</td>
                                     <td>Team Leader</td>
                                     <td>San Francisco</td>
                                     <td>22</td>
                                     <td>2008/10/26</td>
                                     <td>$235,500</td>
                                 </tr>
                                 <tr>
                                     <td>Martena Mccray</td>
                                     <td>Questions/Réponses-Sales support</td>
                                     <td>Edinburgh</td>
                                     <td>46</td>
                                     <td>2011/03/09</td>
                                     <td>$324,050</td>
                                 </tr>
                                 <tr>
                                     <td>Unity Butler</td>
                                     <td>Marketing Designer</td>
                                     <td>San Francisco</td>
                                     <td>47</td>
                                     <td>2009/12/09</td>
                                     <td>$85,675</td>
                                 </tr>
                                 <tr>
                                     <td>Howard Hatfield</td>
                                     <td>Office Manager</td>
                                     <td>San Francisco</td>
                                     <td>51</td>
                                     <td>2008/12/16</td>
                                     <td>$164,500</td>
                                 </tr>
                                 <tr>
                                     <td>Hope Fuentes</td>
                                     <td>Secretary</td>
                                     <td>San Francisco</td>
                                     <td>41</td>
                                     <td>2010/02/12</td>
                                     <td>$109,850</td>
                                 </tr>
                                 <tr>
                                     <td>Vivian Harrell</td>
                                     <td>Financial Controller</td>
                                     <td>San Francisco</td>
                                     <td>62</td>
                                     <td>2009/02/14</td>
                                     <td>$452,500</td>
                                 </tr>
                                 <tr>
                                     <td>Timothy Mooney</td>
                                     <td>Office Manager</td>
                                     <td>London</td>
                                     <td>37</td>
                                     <td>2008/12/11</td>
                                     <td>$136,200</td>
                                 </tr>
                                 <tr>
                                     <td>Jackson Bradshaw</td>
                                     <td>Director</td>
                                     <td>New York</td>
                                     <td>65</td>
                                     <td>2008/09/26</td>
                                     <td>$645,750</td>
                                 </tr>
                                 <tr>
                                     <td>Olivia Liang</td>
                                     <td>Support Engineer</td>
                                     <td>Singapore</td>
                                     <td>64</td>
                                     <td>2011/02/03</td>
                                     <td>$234,500</td>
                                 </tr>
                                 <tr>
                                     <td>Bruno Nash</td>
                                     <td>Software Engineer</td>
                                     <td>London</td>
                                     <td>38</td>
                                     <td>2011/05/03</td>
                                     <td>$163,500</td>
                                 </tr>
                                 <tr>
                                     <td>Sakura Yamamoto</td>
                                     <td>Support Engineer</td>
                                     <td>Tokyo</td>
                                     <td>37</td>
                                     <td>2009/08/19</td>
                                     <td>$139,575</td>
                                 </tr>
                                 <tr>
                                     <td>Thor Walton</td>
                                     <td>Developer</td>
                                     <td>New York</td>
                                     <td>61</td>
                                     <td>2013/08/11</td>
                                     <td>$98,540</td>
                                 </tr>
                                 <tr>
                                     <td>Finn Camacho</td>
                                     <td>Support Engineer</td>
                                     <td>San Francisco</td>
                                     <td>47</td>
                                     <td>2009/07/07</td>
                                     <td>$87,500</td>
                                 </tr>
                                 <tr>
                                     <td>Serge Baldwin</td>
                                     <td>Data Coordinator</td>
                                     <td>Singapore</td>
                                     <td>64</td>
                                     <td>2012/04/09</td>
                                     <td>$138,575</td>
                                 </tr>
                                 <tr>
                                     <td>Zenaida Frank</td>
                                     <td>Software Engineer</td>
                                     <td>New York</td>
                                     <td>63</td>
                                     <td>2010/01/04</td>
                                     <td>$125,250</td>
                                 </tr>
                                 <tr>
                                     <td>Zorita Serrano</td>
                                     <td>Software Engineer</td>
                                     <td>San Francisco</td>
                                     <td>56</td>
                                     <td>2012/06/01</td>
                                     <td>$115,000</td>
                                 </tr>
                                 <tr>
                                     <td>Jennifer Acosta</td>
                                     <td>Junior Javascript Developer</td>
                                     <td>Edinburgh</td>
                                     <td>43</td>
                                     <td>2013/02/01</td>
                                     <td>$75,650</td>
                                 </tr>
                                 <tr>
                                     <td>Cara Stevens</td>
                                     <td>Sales Assistant</td>
                                     <td>New York</td>
                                     <td>46</td>
                                     <td>2011/12/06</td>
                                     <td>$145,600</td>
                                 </tr>
                                 <tr>
                                     <td>Hermione Butler</td>
                                     <td>Regional Director</td>
                                     <td>London</td>
                                     <td>47</td>
                                     <td>2011/03/21</td>
                                     <td>$356,250</td>
                                 </tr>
                                 <tr>
                                     <td>Lael Greer</td>
                                     <td>Systems Administrator</td>
                                     <td>London</td>
                                     <td>21</td>
                                     <td>2009/02/27</td>
                                     <td>$103,500</td>
                                 </tr>
                                 <tr>
                                     <td>Jonas Alexander</td>
                                     <td>Developer</td>
                                     <td>San Francisco</td>
                                     <td>30</td>
                                     <td>2010/07/14</td>
                                     <td>$86,500</td>
                                 </tr>
                                 <tr>
                                     <td>Shad Decker</td>
                                     <td>Regional Director</td>
                                     <td>Edinburgh</td>
                                     <td>51</td>
                                     <td>2008/11/13</td>
                                     <td>$183,000</td>
                                 </tr>
                                 <tr>
                                     <td>Michael Bruce</td>
                                     <td>Javascript Developer</td>
                                     <td>Singapore</td>
                                     <td>29</td>
                                     <td>2011/06/27</td>
                                     <td>$183,000</td>
                                 </tr>
                                 <tr>
                                     <td>Donna Snider</td>
                                     <td>Customer Support</td>
                                     <td>New York</td>
                                     <td>27</td>
                                     <td>2011/01/25</td>
                                     <td>$112,000</td>
                                 </tr>
                             </tbody>
                             <tfoot>
                                 <tr>
                                     <th>Name</th>
                                     <th>Position</th>
                                     <th>Office</th>
                                     <th>Age</th>
                                     <th>Start date</th>
                                     <th>Salary</th>
                                 </tr>
                             </tfoot>
                         </table>
                     </div>
                 </div>
             </div>
         </div>
     </div>
 </div>
 <!-- Data Table area End-->
</asp:Content>
