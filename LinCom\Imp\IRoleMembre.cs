﻿using LinCom.Class;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IRoleMembre
    {
        void AfficherDetails(int idRole, long idorg, string name, int cd, RoleMembre_Class roleClass);
        int Ajouter(RoleMembre_Class role);
        int Modifier(RoleMembre_Class roleClass, int id, long idorg);
        int Supprimer(int idRole, long idorg);
        void chargerRole(DropDownList lst, long id, long idorg, int cd);
        void ChargerGridviewRoles(GridView gdv, long idorg, int cd);
        void AfficherDetails(string code, RoleMembre_Class roleClass);
        void AfficherDetailRole(int id, RoleMembre_Class roleClass);

    }
}
