﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class SubscriptionImp : ISubscription
    {
        int msg;
        private Subscription subscription = new Subscription();

        public void AfficherDetails(long subscriptionId, Subscription_Class subscriptionClass)
        {
            using (Connection con = new Connection())
            {
                var s = con.Subscriptions.FirstOrDefault(x => x.SubscriptionId == subscriptionId);
                if (s != null)
                {
                    subscriptionClass.SubscriptionId = s.SubscriptionId;
                    subscriptionClass.MembreId = s.MembreId;
                    subscriptionClass.Email = s.Email;
                    subscriptionClass.DateAbonnement = s.DateAbonnement;
                }
            }
        }

        public int Ajouter(Subscription_Class subscriptionClass)
        {
            using (Connection con = new Connection())
            {
                subscription.MembreId = subscriptionClass.MembreId;
                subscription.Email = subscriptionClass.Email;
                subscription.DateAbonnement = DateTime.Now;

                try
                {
                    con.Subscriptions.Add(subscription);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerStatistiques(Repeater rpt)
        {
            throw new NotImplementedException();
        }

        public void chargerSubscription(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Subscriptions select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner la Subscription d'un Membre";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.SubscriptionId.ToString();
                        item.Text = data.MembreId.ToString();
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }



        public void ChargerSubscriptions(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var query = from s in con.Subscriptions
                            join m in con.Membres on s.MembreId equals m.MembreId into membreJoin
                            from m in membreJoin.DefaultIfEmpty()
                            select new
                            {
                                s.SubscriptionId,
                                Membre = m != null ? m.Nom + " " + m.Prenom : "Non associé",
                                s.Email,
                                s.DateAbonnement
                            };

                gdv.DataSource = query.OrderByDescending(x => x.DateAbonnement).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerSubscriptionsParMembre(GridView gdv, long membreId)
        {
            using (Connection con = new Connection())
            {
                var query = from s in con.Subscriptions
                            where s.MembreId == membreId
                            select new
                            {
                                s.SubscriptionId,
                                s.Email,
                                s.DateAbonnement
                            };

                gdv.DataSource = query.OrderByDescending(x => x.DateAbonnement).ToList();
                gdv.DataBind();
            }
        }

        public int Modifier(Subscription_Class subscriptionClass)
        {
            using (Connection con = new Connection())
            {
                var s = con.Subscriptions.FirstOrDefault(x => x.SubscriptionId == subscriptionClass.SubscriptionId);
                if (s != null)
                {
                    s.MembreId = subscriptionClass.MembreId;
                    s.Email = subscriptionClass.Email;
                    s.DateAbonnement = subscriptionClass.DateAbonnement;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }



        public int Supprimer(long subscriptionId)
        {
            using (Connection con = new Connection())
            {
                var s = con.Subscriptions.FirstOrDefault(x => x.SubscriptionId == subscriptionId);
                if (s != null)
                {
                    con.Subscriptions.Remove(s);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}