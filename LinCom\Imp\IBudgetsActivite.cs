﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IBudgetsActivite
    {
        void AfficherDetails(int idBudget, BudgetsActivite_Class budgetsActiviteClass);
        int Ajouter(BudgetsActivite_Class budgetsActiviteClass);
        void ChargerBudgets(GridView gdv, long activiteProjetId);
        int Modifier(BudgetsActivite_Class budgetsActiviteClass);
        void chargerCommentairePost(DropDownList lst);
        int Supprimer(int idBudget);
    }
}
