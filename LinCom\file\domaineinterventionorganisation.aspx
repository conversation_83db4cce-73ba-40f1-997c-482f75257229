﻿<%@ Page Title="" Language="C#" ValidateRequest="false" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="domaineinterventionorganisation.aspx.cs" Inherits="LinCom.file.domaineinterventionorganisation" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">

                                    <div class="breadcomb-ctn">
                                        <h2>Domaine d'Intervention</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">

                                    <a data-toggle="tooltip" data-placement="left" href="listdomaineinterventionorganisation.aspx" title="Clique sur ce button pour visualiser la liste des domaines d'intervention" class="btn">Liste des Domaines d'intervention</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->
    <!-- Form Element area Start-->
    <div class="form-element-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="cmp-tb-hd bcs-hd">
                            <p style="color: red">Remplissez les champs obligatoires avec asterix</p>
                        </div>
    <!-- Page Alert  -->
    <div class="alert-list">
        <div class="alert alert-success alert-dismissible" role="alert" runat="server" id="div_msg_succes">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
            <span runat="server" id="msg_succes">Enregistrement réussi</span>
        </div>
        <div class="alert alert-danger alert-dismissible" role="alert" runat="server" id="div_msg_error">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
            <span runat="server" id="msg_error">Enregistrement échoué</span>
        </div>
    </div>
<!--  Fin Page Alert -->
    <div class="row">
        <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
            <div class="form-group ic-cmp-int">
                <div class="form-ic-cmp">
                    <i class="notika-icon notika-edit"></i>
                </div>
                <div class="nk-int-st">
                    <asp:DropDownList class="form-control" ID="drpdorganisation" runat="server">
                        <asp:ListItem Value="-1">Selectionner l'organisation</asp:ListItem>
                    </asp:DropDownList>
                    <a href="organisation.aspx">Si l'organisation n'existe pas, je veux en créer</a>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
            <div class="form-group ic-cmp-int">
                <div class="form-ic-cmp">
                    <i class="notika-icon notika-edit"></i>
                </div>
                <div class="nk-int-st">
                    <asp:DropDownList class="form-control" ID="drpddom" runat="server">
                        <asp:ListItem Value="-1">Selectionner le domaine</asp:ListItem>
                    </asp:DropDownList>
                    <a href="domaineintervention.aspx">Si mon domaine n'existe pas, je veux en créer</a>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
            <div class="form-group ic-cmp-int">
                <div class="form-ic-cmp">
                    <i class="notika-icon notika-edit"></i>
                </div>
                <div class="nk-int-st">
                    <asp:DropDownList class="form-control" ID="drpdstatut" runat="server">
                        <asp:ListItem Value="-1">Selectionner le statut</asp:ListItem>
                        <asp:ListItem Value="actif">Activé</asp:ListItem>
                        <asp:ListItem Value="inactif">Désactivé</asp:ListItem>
                    </asp:DropDownList>
                </div>
            </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="form-group ic-cmp-int">
                <div class="form-ic-cmp">
                    <i class="notika-icon notika-form"></i>
                </div>
                <div class="nk-int-st">
                    <div class="html-editor">
                    <textarea runat="server" id="txtDescription" class="form-control" placeholder="Description du domaine  *" rows="4"></textarea>
                     </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
            <button type="button" runat="server" id="btn_ajouter" onserverclick="btn_ajouter_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
        </div>
    </div>
                        </div>

                    </div>
                </div>
            </div>

        </div>
    </div>
    <!-- Form Element area End-->
</asp:Content>
