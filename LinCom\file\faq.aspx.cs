﻿using LinCom.Class;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class faq : System.Web.UI.Page
    {
        FAQ_Class faqObj = new FAQ_Class();
        FAQImp objFaq = new FAQImp();
        CommonCode co = new CommonCode();

        string nsco;
        int faqId;
        long ide;
        int rolid;

        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            if (!string.IsNullOrEmpty(nsco))
            {
                faqId = Convert.ToInt32(nsco);
            }

            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {
                    ide = Convert.ToInt64(idperso.Value);
                    rolid = Convert.ToInt32(role.Value);
                }
                else
                {
                    Response.Redirect("~/login.aspx");
                }

                InitialiserMessages();

                // Si modification, charger les données
                if (faqId > 0)
                {
                    ChargerDonneesFAQ();
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_success.Visible = false;
            div_msg_error.Visible = false;
        }

        private void ChargerDonneesFAQ()
        {
            try
            {
                objFaq.AfficherDetails(faqId, faqObj);

                txtQuestion.Value = faqObj.Question;
                txtReponse.Value = faqObj.Reponse;
                drpdstatut.SelectedValue = faqObj.statut;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors du chargement des données : " + ex.Message;
            }
        }

        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            try
            {
                if (ValiderFormulaire())
                {
                    if (faqId > 0)
                    {
                        ModifierFAQ();
                    }
                    else
                    {
                        AjouterFAQ();
                    }
                }
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de l'enregistrement : " + ex.Message;
            }
        }

        private bool ValiderFormulaire()
        {
            if (string.IsNullOrEmpty(txtQuestion.Value) ||
                string.IsNullOrEmpty(txtReponse.Value) ||
                drpdstatut.SelectedValue == "-1")
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                return false;
            }
            return true;
        }

        private void AjouterFAQ()
        {
            faqObj.Question = txtQuestion.Value;
            faqObj.Reponse = txtReponse.Value;
            faqObj.statut = drpdstatut.SelectedValue;
            faqObj.name = co.GenerateSlug(txtQuestion.Value);

            int resultat = objFaq.Ajouter(faqObj);

            if (resultat == 1)
            {
                div_msg_success.Visible = true;
                msg_success.InnerText = "FAQ ajoutée avec succès";
                ViderFormulaire();
            }
            else
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de l'ajout de la FAQ";
            }
        }

        private void ModifierFAQ()
        {
            faqObj.Question = txtQuestion.Value;
            faqObj.Reponse = txtReponse.Value;
            faqObj.statut = drpdstatut.SelectedValue;
            faqObj.name = co.GenerateSlug(txtQuestion.Value);

            int resultat = objFaq.Modifier(faqObj, faqId);

            if (resultat == 1)
            {
                div_msg_success.Visible = true;
                msg_success.InnerText = "FAQ modifiée avec succès";
                Response.Redirect("listfaq.aspx");
            }
            else
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de la modification de la FAQ";
            }
        }

        private void ViderFormulaire()
        {
            txtQuestion.Value = "";
            txtReponse.Value = "";
            drpdstatut.SelectedValue = "-1";
        }
    }
}