﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class LangueImp
    {
        private Langue langue = new Langue();
        int msg;

        public void AfficherDetails(int idLangue, Langue_Class langueClass)
        {
            using (Connection con = new Connection())
            {
                var l = con.Langues.FirstOrDefault(x => x.LangueId == idLangue);
                if (l != null)
                {
                    langueClass.IdLangue = l.LangueId;
                    langueClass.Code = l.Code;
                    langueClass.Nom = l.Nom;

                }
            }
        }

        public int Ajouter(Langue_Class langueClass)
        {
            using (Connection con = new Connection())
            {
                langue.Code = langueClass.Code;
                langue.Nom = langueClass.Nom;


                try
                {
                    con.Langues.Add(langue);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerLangues(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var query = from l in con.Langues
                            select new
                            {
                                l.LangueId,
                                l.Code,
                                l.Nom,

                            };


                gdv.DataSource = query.OrderBy(x => x.Nom).ToList();
                gdv.DataBind();
            }
        }

        public void chargerLangues(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Langues select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner l'Activite";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.LangueId.ToString();
                        item.Text = data.Nom;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }


        public int Modifier(Langue_Class langueClass)
        {
            using (Connection con = new Connection())
            {
                var l = con.Langues.FirstOrDefault(x => x.LangueId == langueClass.IdLangue);
                if (l != null)
                {
                    l.Code = langueClass.Code;
                    l.Nom = langueClass.Nom;


                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

      

        public int Supprimer(int idLangue)
        {
            using (Connection con = new Connection())
            {
                var l = con.Langues.FirstOrDefault(x => x.LangueId == idLangue);
                if (l != null)
                {

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}