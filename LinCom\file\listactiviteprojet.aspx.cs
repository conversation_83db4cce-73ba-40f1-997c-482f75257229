﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listactiviteprojet : System.Web.UI.Page
    {
        private int info;
        int financementId;
        ActiviteProjet_Class actproj = new ActiviteProjet_Class();
        ActiviteProjet_Class financemen = new ActiviteProjet_Class();
        IActiviteProjet obj = new ActiviteProjetImp();
        ICommonCode co = new CommonCode();
        IPoste objpos = new PosteImp();
        Post_Class pos = new Post_Class();

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        DomaineRessource_Class actco = new DomaineRessource_Class();
        IDomaineRessource objactco = new DomaineRessourceImp();
        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        static string imge, imge1, pdfe, nameorg;
        static long ide, idorg;
        static int rolid;
        long index;
        static string nsco;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    ide = Convert.ToInt64(idperso.Value);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;


                getDataGDV();
            }
        }

        public void getDataGDV()
        {
            // Appeler la méthode avec le paramètre projetId
            obj.ChargerActiviteProjet(gdv, -1, idorg, "", 0);
        }

        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view")
            {
                Response.Redirect("~/file/activiteprojet.aspx?id=" + index);
            }
            else if (e.CommandName == "delete")
            {
                try
                {
                    // Utiliser ToInt64 au lieu de ToInt32 pour correspondre au type attendu par l'interface
                    long proactId = Convert.ToInt64(index);
                    obj.AfficherDetails(proactId, idorg,"",0, actproj);
                    info = obj.Supprimer(proactId,idorg);

                    if (info == 1)
                    {
                        Response.Redirect("~/file/listactiviteprojet.aspx");
                    }
                    else
                    {

                    }
                }
                catch (SqlException ex)
                {

                    // Journalisation de l'erreur
                    System.Diagnostics.Debug.WriteLine("Erreur SQL dans gdv_RowCommand: " + ex.ToString());
                }
                catch (Exception ex)
                {

                    // Journalisation de l'erreur
                    System.Diagnostics.Debug.WriteLine("Erreur dans gdv_RowCommand: " + ex.ToString());
                }
            }
        }
    }
}