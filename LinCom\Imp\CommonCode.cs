﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Text;
using System.Web;
using System.Security.Cryptography;


namespace LinCom.Imp
{
    public class CommonCode : ICommonCode
    {
        public string RemoveDiacritics(string text)
        {
            var normalized = text.Normalize(NormalizationForm.FormD);
            var sb = new StringBuilder();

            foreach (var c in normalized)
            {
                var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
                if (unicodeCategory != UnicodeCategory.NonSpacingMark)
                {
                    sb.Append(c);
                }
            }

            return sb.ToString().Normalize(NormalizationForm.FormC);
        }

        public string GenerateSlug(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return string.Empty;

            // Convertir en minuscules et supprimer les accents
            string str = RemoveDiacritics(input.ToLowerInvariant());

            // Supprimer tout sauf les lettres et les chiffres
            str = Regex.Replace(str, @"[^a-z0-9]", "");

            return str;
        }

        public string HasherMotDePasse(string motDePasse)
        {
            return BCrypt.Net.BCrypt.HashPassword(motDePasse);
        }

        public bool VerifierMotDePasse(string motDePasseEntree, string motDePasseHache)
        {
            return BCrypt.Net.BCrypt.Verify(motDePasseEntree, motDePasseHache);
        }
        public string GenererToken()
        {
            using (var rng = new RNGCryptoServiceProvider())
            {
                byte[] tokenData = new byte[32];
                rng.GetBytes(tokenData);
                return Convert.ToBase64String(tokenData).Replace("+", "").Replace("/", "").Replace("=", "");
            }
        }
    }
}