﻿<%@ Page Title="" Language="C#" ValidateRequest="false" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="politiqueconfidentialite.aspx.cs" Inherits="LinCom.file.politiqueconfidentialite" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
      <div class="breadcomb-area">
	<div class="container">
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
				<div class="breadcomb-list">
					<div class="row">
						<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
							<div class="breadcomb-wp">
								<div class="breadcomb-icon">
									<i class="notika-icon notika-form"></i>
								</div>
								<div class="breadcomb-ctn">
									<h2>Conditions d'utilisation</h2>
								</div>
							</div>
						</div>
						<div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
							<div class="breadcomb-report">
								<a runat="server" visible="false" data-toggle="tooltip" data-placement="left" href="listconditionutilisation.aspx" title="Cliquez sur ce bouton pour visualiser la liste des conditions d'utilisation" class="btn">Liste des Conditions d'utilisation</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!-- Breadcomb area End-->
<!-- Form Element area Start-->
<div class="form-element-area">
    <div class="container">
        <div class="row">
            <div class="alert-list">
                <div class="alert alert-success alert-dismissible" role="alert" runat="server" id="div_msg_succes">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button><span runat="server" id="msg_succes">Enregistrement réussi</span>
                </div>
                <div class="alert alert-danger alert-dismissible" role="alert" runat="server" id="div_msg_error">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button><span runat="server" id="msg_error">Enregistrement échoué</span>
                </div>
            </div>

            
                           


            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="form-element-list">
                    <div class="cmp-tb-hd bcs-hd">
                        <p style="color:red">Remplissez les champs obligatoires avec asterix</p>
                    </div>
                    <div class="row">
                      
                   <div class="form-group ic-cmp-int">
         <div class="form-ic-cmp">
             <i class="notika-icon notika-form"></i>
         </div>
         <div class="nk-int-st">
             <div >
             <textarea class="html-editor" runat="server" id="txtContenu" rows="10"></textarea>
           
</div>
         </div>
     </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <button type="button" runat="server" id="btn_ajouter" onserverclick="btn_ajouter_ServerClick" class="btn btn-success notika-gp-success">Mettre à jour</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Form Element area End-->
</asp:Content>
