using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IVotes
    {
        // Méthodes CRUD de base
        int Add(Votes_Class vote);
        void AfficherDetails(long voteId, Votes_Class vote);
        int Edit(Votes_Class vote, long voteId);
        int Supprimer(long voteId);
        void Chargement_GDV(GridView gdv);
        void Search(GridView gdv, string searchTerm);
        int Count();

        // Méthodes Stack Overflow style - Votes sur posts
        int VoterPost(long postId, long userId, int voteType, string ipAddress = null);
        int AnnulerVotePost(long postId, long userId);
        bool ADejaVote(long postId, long userId);
        int ObtenirTypeVoteUtilisateur(long postId, long userId);
        void ChargerVotesPost(long postId, out int upVotes, out int downVotes, out int score);

        // Votes sur commentaires
        int VoterCommentaire(long commentId, long userId, bool upVote, string ipAddress = null);
        int AnnulerVoteCommentaire(long commentId, long userId);
        bool ADejaVoteCommentaire(long commentId, long userId);
        int ObtenirScoreCommentaire(long commentId);

        // Gestion des favoris
        int AjouterAuxFavoris(long postId, long userId);
        int SupprimerDesFavoris(long postId, long userId);
        bool EstDansFavoris(long postId, long userId);
        void ChargerFavorisUtilisateur(ListView lv, long userId, int limite = 20);
        int CompterFavoris(long postId);

        // Votes spéciaux (Stack Overflow style)
        int VoterFermeture(long postId, long userId, string raison);
        int VoterReouverture(long postId, long userId);
        int VoterSuppression(long postId, long userId);
        int VoterRestoration(long postId, long userId);
        int SignalerSpam(long postId, long userId, string raison);
        int SignalerOffensant(long postId, long userId, string raison);

        // Bounty (récompenses)
        int DemarrerBounty(long postId, long userId, int montant);
        int AttribuerBounty(long postId, long reponseId, long userId, int montant);
        void ChargerBountiesActifs(ListView lv);
        void ChargerBountiesUtilisateur(ListView lv, long userId);
        bool PeutDemarrerBounty(long postId, long userId);

        // Statistiques et analytics
        void ChargerStatistiquesVotes(out int totalVotes, out int upVotes, out int downVotes);
        void ChargerVotesRecents(ListView lv, int limite = 20);
        void ChargerTopVoteurs(ListView lv, int limite = 10);
        void ChargerPostsLesPlusVotes(ListView lv, int limite = 10);
        void ChargerActiviteVotes(ListView lv, long userId, int limite = 20);

        // Validation et prévention d'abus
        bool PeutVoter(long userId, int voteType);
        bool AAtteintLimiteVotesJournaliere(long userId);
        int ObtenirNombreVotesJour(long userId);
        bool EstVoteValide(long postId, long userId, int voteType);
        void DetecterVotesAnormaux(long userId);

        // Calculs de réputation
        void CalculerReputationPost(long postId);
        void MettreAJourReputationUtilisateur(long userId);
        int CalculerPointsVote(int voteType, bool estAuteur = false);
        void AppliquerPointsReputation(long userId, long postId, int voteType);

        // Historique et audit
        void ChargerHistoriqueVotes(ListView lv, long postId);
        void ChargerVotesUtilisateur(ListView lv, long userId, int limite = 50);
        void LoggerVote(long postId, long userId, int voteType, string ipAddress);
        void ChargerVotesSuspects(ListView lv);

        // Administration
        void AnnulerVotesUtilisateur(long userId, string raison);
        void RecalculerTousLesScores();
        void NettoierVotesAnciens(DateTime dateLimit);
        void ChargerRapportVotes(DateTime dateDebut, DateTime dateFin);
    }
}
