﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IOrganisation
    {
        void AfficherDetails(long organisationId, Organisation_Class organisation);
        int Ajouter(Organisation_Class organisation);
       // int Modifier(Organisation_Class organisation);
        int Supprimer(long organisationId);
        void ChargerGridView(GridView gdv, string filtre = "", int typeOrg = 0);
        void AfficherDetails(string organisationId, Organisation_Class organisationClass);
        void chargerOrganisation(DropDownList ddw);
        int Modifier(Organisation_Class organisationClass, long id);
        void Chargement_GDVL(ListView listv, string code, int cd);
        int count(int cd, int ct, string publie, string code);
    }
}
