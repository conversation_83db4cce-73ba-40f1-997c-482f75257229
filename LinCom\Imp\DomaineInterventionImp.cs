﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class DomaineInterventionImp : IDomaineIntervention
    {
        int msg;
        private DomaineIntervention domaine = new DomaineIntervention();
       
        public void AfficherDetails(int idDomaine, DomaineIntervention_Class domaineClass)
        {
            using (Connection con = new Connection())
            {
                var d = con.DomaineInterventions.FirstOrDefault(x => x.DomaineInterventionId == idDomaine);
                if (d != null)
                {
                    domaineClass.DomaineInterventionId = d.DomaineInterventionId;
                    domaineClass.Libelle = d.Libelle;
                    domaineClass.name = d.name;
                }
            }
        }
        public void AfficherDetails(string code, DomaineIntervention_Class domaineClass,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var d = con.DomaineInterventions.FirstOrDefault(x => x.name == code);
                    if (d != null)
                    {
                        domaineClass.DomaineInterventionId = d.DomaineInterventionId;
                        domaineClass.Libelle = d.Libelle;
                        domaineClass.name = d.name;
                        
                    }
                }
                else if (cd==1)
                {
                    var d = con.DomaineInterventions.FirstOrDefault(x => x.Libelle == code);
                    if (d != null)
                    {
                        domaineClass.DomaineInterventionId = d.DomaineInterventionId;
                        domaineClass.Libelle = d.Libelle;
                        domaineClass.name = d.name;
                    }
                }
                  
            }
        }

        public int Ajouter(DomaineIntervention_Class domaineClass)
        {
            using (Connection con = new Connection())
            {
                domaine.Libelle = domaineClass.Libelle;
                domaine.name = domaineClass.name;

                try
                {
                    con.DomaineInterventions.Add(domaine);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }
        public void ChargerDomainedisponible(DropDownList ddw)
        {
          
                ddw.Items.Clear();
                using (Connection con = new Connection())
                {
                    var obj = (from p in con.DomaineInterventions select p).ToList();

                    if (obj != null && obj.Count() > 0)
                    {
                        ddw.Items.Clear();

                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = "--- Selectionner le domaine ---";
                        ddw.Items.Add(item0);

                        foreach (var data in obj)
                        {
                            ListItem item = new ListItem();
                            item.Value = data.DomaineInterventionId.ToString();
                            item.Text = data.Libelle;
                            ddw.Items.Add(item);
                        }

                }
            }
        }
        public void ChargerDomaines(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var query = from d in con.DomaineInterventions
                            select new
                            {
                                id=d.DomaineInterventionId,
                                libelle=d.Libelle,
                                name= d.name
                            };

                gdv.DataSource = query.OrderBy(x => x.libelle).ToList();
                gdv.DataBind();
            }
        }
        public void ChargerDomaines(ListView gdv)
        {
            using (Connection con = new Connection())
            {
                var query = from d in con.DomaineInterventions
                            join dp in con.DomainePosts on d.DomaineInterventionId equals dp.DomaineInterventionOrganisationId into dpGroup
                            from dp in dpGroup.DefaultIfEmpty()
                            join p in con.Posts on dp.PostId equals p.PostId into postGroup
                            from p in postGroup.DefaultIfEmpty()
                            group p by new { d.DomaineInterventionId, d.Libelle, d.name } into grp
                            select new
                            {
                                Id = grp.Key.DomaineInterventionId,
                                Libelle = grp.Key.Libelle,
                                Name = grp.Key.name,
                                NombrePosts = grp.Count(x => x != null)  // Ne compter que les posts existants
                            };


                gdv.DataSource = query.OrderBy(x => x.Libelle).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerOrganisationsParDomaine(GridView gdv, int idDomaine)
        {
            using (Connection con = new Connection())
            {
                var query = from dio in con.DomaineInterventionOrganisations
                            join o in con.Organisations on dio.OrganisationId equals o.OrganisationId
                            where dio.DomaineInterventionId == idDomaine
                            select new
                            {
                                o.OrganisationId,
                                o.Nom,
                                o.DateCreation
                            };

                gdv.DataSource = query.OrderBy(x => x.Nom).ToList();
                gdv.DataBind();
            }
        }

        
        

        public int Modifier(int id,DomaineIntervention_Class domaineClass)
        {
            using (Connection con = new Connection())
            {
                var d = con.DomaineInterventions.FirstOrDefault(x => x.DomaineInterventionId == id);
                if (d != null)
                {
                    d.Libelle = domaineClass.Libelle;
                    d.name = domaineClass.name;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(int idDomaine)
        {
            using (Connection con = new Connection())
            {
                var d = con.DomaineInterventions.FirstOrDefault(x => x.DomaineInterventionId == idDomaine);
                if (d != null)
                {
                    con.DomaineInterventions.Remove(d);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}