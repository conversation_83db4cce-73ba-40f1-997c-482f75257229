using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class FAQImp : IFAQ
    {
        int msg;
        private FAQ faq = new FAQ();

        public void AfficherDetails(int faqId, FAQ_Class faqClass)
        {
            using (Connection con = new Connection())
            {
                var f = con.FAQs.FirstOrDefault(x => x.FAQId == faqId);
                if (f != null)
                {
                    faqClass.FAQId = f.FAQId;
                    faqClass.Question = f.Question;
                    faqClass.Reponse = f.Reponse;
                    faqClass.name = f.name;
                    faqClass.statut = f.statut;
                    faqClass.DatePublication = f.DatePublication;
                }
            }
        }

        public void AfficherDetails(string name, FAQ_Class faqClass)
        {
            using (Connection con = new Connection())
            {
                var f = con.FAQs.FirstOrDefault(x => x.name == name);
                if (f != null)
                {
                    faqClass.FAQId = f.FAQId;
                    faqClass.Question = f.Question;
                    faqClass.Reponse = f.Reponse;
                    faqClass.name = f.name;
                    faqClass.statut = f.statut;
                    faqClass.DatePublication = f.DatePublication;
                }
            }
        }

        public int Ajouter(FAQ_Class faqClass)
        {
            using (Connection con = new Connection())
            {
                faq.Question = faqClass.Question;
                faq.Reponse = faqClass.Reponse;
                faq.name = faqClass.name;
                faq.statut = faqClass.statut;
                faq.DatePublication = DateTime.Now;

                try
                {
                    con.FAQs.Add(faq);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public int Modifier(FAQ_Class faqClass, int id)
        {
            using (Connection con = new Connection())
            {
                var f = con.FAQs.FirstOrDefault(x => x.FAQId == id);
                if (f != null)
                {
                    f.Question = faqClass.Question;
                    f.Reponse = faqClass.Reponse;
                    f.name = faqClass.name;
                    f.statut = faqClass.statut;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch (Exception e)
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(int faqId)
        {
            using (Connection con = new Connection())
            {
                var f = con.FAQs.FirstOrDefault(x => x.FAQId == faqId);
                if (f != null)
                {
                    try
                    {
                        con.FAQs.Remove(f);
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch (Exception e)
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public void ChargerGridView(GridView gdv, string filtre = "")
        {
            using (Connection con = new Connection())
            {
                var query = from f in con.FAQs
                           select new
                           {
                               id = f.FAQId,
                               Question = f.Question,
                               Reponse = f.Reponse,
                               name = f.name,
                               statut = f.statut,
                               DatePublication = f.DatePublication
                           };

                if (!string.IsNullOrEmpty(filtre))
                {
                    query = query.Where(x => x.Question.Contains(filtre) || x.Reponse.Contains(filtre));
                }

                gdv.DataSource = query.OrderByDescending(x => x.DatePublication).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerFAQPubliques(ListView lv, string statut = "publié")
        {
            using (Connection con = new Connection())
            {
                var query = from f in con.FAQs
                           where f.statut == statut
                           select new
                           {
                               id = f.FAQId,
                               Question = f.Question,
                               Reponse = f.Reponse,
                               name = f.name,
                               statut = f.statut,
                               DatePublication = f.DatePublication
                           };

                lv.DataSource = query.OrderByDescending(x => x.DatePublication).ToList();
                lv.DataBind();
            }
        }

        public int count(string statut = "")
        {
            using (Connection con = new Connection())
            {
                if (string.IsNullOrEmpty(statut))
                {
                    return con.FAQs.Count();
                }
                else
                {
                    return con.FAQs.Where(x => x.statut == statut).Count();
                }
            }
        }
    }
}
