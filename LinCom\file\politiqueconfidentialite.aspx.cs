﻿using LinCom.Class;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class politiqueconfidentialite : System.Web.UI.Page
    {

        private int info;
        string nscno;
        PolitiqueConfidentialite_Class condition = new PolitiqueConfidentialite_Class();
        PolitiqueConfidentialite_Class condTemp = new PolitiqueConfidentialite_Class();
        IPolitiqueConfidentialite objCondition = new PolitiqueConfidentialiteImp();
        ICommonCode co = new CommonCode();

        protected void Page_Load(object sender, EventArgs e)
        {
            nscno = Request.QueryString["name"];
            if (!IsPostBack)
            {
                initial_msg();
                afficher();

            }
        }

        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
        }

        public void AjouterCondition()
        {
            try
            {
                if (txtContenu.Value == "")
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir le contenu des conditions d'utilisation";
                    return;
                }
                else
                {
                    condition.name = ((objCondition.countID()) + 1).ToString();
                    condition.Contenu = txtContenu.Value;
                    condition.DatePublication = DateTime.Now;

                    info = objCondition.Ajouter(condition);

                    if (info == 1)
                    {
                        Response.Redirect("~/file/politiqueconfidentialite.aspx");
                    }
                    else
                    {
                        div_msg_succes.Visible = false;
                        div_msg_error.Visible = true;
                        msg_error.InnerText = "Erreur lors de l'enregistrement des conditions d'utilisation";
                    }
                }


            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false;
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + e.Message;
            }
            catch (Exception ex)
            {
                div_msg_succes.Visible = false;
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        public void ModifierCondition()
        {
            try
            {
                if (txtContenu.Value == "")
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir le contenu des conditions d'utilisation";
                    return;
                }

                // Récupération des détails de la condition existante
                objCondition.AfficherDetails(objCondition.ID(), condTemp);
                int conditionId = condTemp.PolitiqueId;

                // Préparation des données pour la modification
                condition.Contenu = txtContenu.Value;

                // Utilisation de la nouvelle méthode Modifier de l'interface
                info = objCondition.Modifier(condition, objCondition.ID());

                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    div_msg_error.Visible = false;
                    msg_succes.InnerText = "Les politiques d'utilisation ont été modifiées avec succès";
                    Response.Redirect("~/file/politiqueconfidentialite.aspx");
                }
                else
                {
                    div_msg_succes.Visible = false;
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de la modification des politique de confidentialité";
                }
            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false;
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + e.Message;
            }
            catch (Exception ex)
            {
                div_msg_succes.Visible = false;
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        protected void btn_ajouter_ServerClick(object sender, EventArgs e)
        {
            if (objCondition.ID() > 0)
            {
                ModifierCondition();

            }
            else AjouterCondition();



        }

        protected void afficher()
        {
            objCondition.AfficherDetails(objCondition.ID(), condition);
            txtContenu.Value = condition.Contenu;

        }
    }
}