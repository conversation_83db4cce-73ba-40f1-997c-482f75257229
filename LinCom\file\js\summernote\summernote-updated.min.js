!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):e(window.jQuery)}(function(e){Array.prototype.reduce||(Array.prototype.reduce=function(e){var t,n=Object(this),o=n.length>>>0,i=0;if(2===arguments.length)t=arguments[1];else{for(;o>i&&!(i in n);)i++;if(i>=o)throw new TypeError("Reduce of empty array with no initial value");t=n[i++]}for(;o>i;i++)i in n&&(t=e(t,n[i],i,n));return t}),"function"!=typeof Array.prototype.filter&&(Array.prototype.filter=function(e){for(var t=Object(this),n=t.length>>>0,o=[],i=arguments.length>=2?arguments[1]:void 0,r=0;n>r;r++)if(r in t){var a=t[r];e.call(i,a,r,t)&&o.push(a)}return o}),Array.prototype.map||(Array.prototype.map=function(e,t){var n,o,i;if(null===this)throw new TypeError(" this is null or not defined");var r=Object(this),a=r.length>>>0;if("function"!=typeof e)throw new TypeError(e+" is not a function");for(arguments.length>1&&(n=t),o=new Array(a),i=0;a>i;){var s,l;i in r&&(s=r[i],l=e.call(n,s,i,r),o[i]=l),i++}return o});var t,n="function"==typeof define&&define.amd,o=function(t){var n="Comic Sans MS"===t?"Courier New":"Comic Sans MS",o=e("<div>").css({position:"absolute",left:"-9999px",top:"-9999px",fontSize:"200px"}).text("mmmmmmmmmwwwwwww").appendTo(document.body),i=o.css("fontFamily",n).width(),r=o.css("fontFamily",t+","+n).width();return o.remove(),i!==r},i=navigator.userAgent,r=/MSIE|Trident/i.test(i);if(r){var a=/MSIE (\d+[.]\d+)/.exec(i);a&&(t=parseFloat(a[1])),a=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(i),a&&(t=parseFloat(a[1]))}var s,l={isMac:navigator.appVersion.indexOf("Mac")>-1,isMSIE:r,isFF:/firefox/i.test(i),isWebkit:/webkit/i.test(i),isSafari:/safari/i.test(i),browserVersion:t,jqueryVersion:parseFloat(e.fn.jquery),isSupportAmd:n,hasCodeMirror:n?require.specified("CodeMirror"):!!window.CodeMirror,isFontInstalled:o,isW3CRangeSupport:!!document.createRange},d=function(){var t=function(e){return function(t){return e===t}},n=function(e,t){return e===t},o=function(e){return function(t,n){return t[e]===n[e]}},i=function(){return!0},r=function(){return!1},a=function(e){return function(){return!e.apply(e,arguments)}},s=function(e,t){return function(n){return e(n)&&t(n)}},l=function(e){return e},d=0,c=function(e){var t=++d+"";return e?e+t:t},u=function(t){var n=e(document);return{top:t.top+n.scrollTop(),left:t.left+n.scrollLeft(),width:t.right-t.left,height:t.bottom-t.top}},f=function(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t},h=function(e,t){return t=t||"",t+e.split(".").map(function(e){return e.substring(0,1).toUpperCase()+e.substring(1)}).join("")};return{eq:t,eq2:n,peq2:o,ok:i,fail:r,self:l,not:a,and:s,uniqueId:c,rect2bnd:u,invertObject:f,namespaceToCamel:h}}(),c=function(){var t=function(e){return e[0]},n=function(e){return e[e.length-1]},o=function(e){return e.slice(0,e.length-1)},i=function(e){return e.slice(1)},r=function(e,t){for(var n=0,o=e.length;o>n;n++){var i=e[n];if(t(i))return i}},a=function(e,t){for(var n=0,o=e.length;o>n;n++)if(!t(e[n]))return!1;return!0},s=function(t,n){return e.inArray(n,t)},l=function(e,t){return-1!==s(e,t)},c=function(e,t){return t=t||d.self,e.reduce(function(e,n){return e+t(n)},0)},u=function(e){for(var t=[],n=-1,o=e.length;++n<o;)t[n]=e[n];return t},f=function(e,o){if(!e.length)return[];var r=i(e);return r.reduce(function(e,t){var i=n(e);return o(n(i),t)?i[i.length]=t:e[e.length]=[t],e},[[t(e)]])},h=function(e){for(var t=[],n=0,o=e.length;o>n;n++)e[n]&&t.push(e[n]);return t},p=function(e){for(var t=[],n=0,o=e.length;o>n;n++)l(t,e[n])||t.push(e[n]);return t},v=function(e,t){var n=s(e,t);return-1===n?null:e[n+1]},g=function(e,t){var n=s(e,t);return-1===n?null:e[n-1]};return{head:t,last:n,initial:o,tail:i,prev:g,next:v,find:r,contains:l,all:a,sum:c,from:u,clusterBy:f,compact:h,unique:p}}(),u=String.fromCharCode(160),f="﻿",h=function(){var t=function(t){return t&&e(t).hasClass("note-editable")},n=function(t){return t&&e(t).hasClass("note-control-sizing")},o=function(t){var n;if(t.hasClass("note-air-editor")){var o=c.last(t.attr("id").split("-"));return n=function(t){return function(){return e(t+o)}},{editor:function(){return t},holder:function(){return t.data("holder")},editable:function(){return t},popover:n("#note-popover-"),handle:n("#note-handle-"),dialog:n("#note-dialog-")}}n=function(e,n){return n=n||t,function(){return n.find(e)}};var i=t.data("options"),r=i&&i.dialogsInBody?e(document.body):null;return{editor:function(){return t},holder:function(){return t.data("holder")},dropzone:n(".note-dropzone"),toolbar:n(".note-toolbar"),editable:n(".note-editable"),codable:n(".note-codable"),statusbar:n(".note-statusbar"),popover:n(".note-popover"),handle:n(".note-handle"),dialog:n(".note-dialog",r)}},i=function(t){var n=e(t).closest(".note-editor, .note-air-editor, .note-air-layout");if(!n.length)return null;var i;return i=n.is(".note-editor, .note-air-editor")?n:e("#note-editor-"+c.last(n.attr("id").split("-"))),o(i)},r=function(e){return e=e.toUpperCase(),function(t){return t&&t.nodeName.toUpperCase()===e}},a=function(e){return e&&3===e.nodeType},s=function(e){return e&&/^BR|^IMG|^HR|^IFRAME|^BUTTON/.test(e.nodeName.toUpperCase())},p=function(e){return t(e)?!1:e&&/^DIV|^P|^LI|^H[1-7]/.test(e.nodeName.toUpperCase())},v=r("LI"),g=function(e){return p(e)&&!v(e)},m=r("TABLE"),b=function(e){return!(w(e)||y(e)||C(e)||p(e)||m(e)||x(e))},y=function(e){return e&&/^UL|^OL/.test(e.nodeName.toUpperCase())},C=r("HR"),k=function(e){return e&&/^TD|^TH/.test(e.nodeName.toUpperCase())},x=r("BLOCKQUOTE"),w=function(e){return k(e)||x(e)||t(e)},N=r("A"),T=function(e){return b(e)&&!!A(e,p)},S=function(e){return b(e)&&!A(e,p)},P=r("BODY"),L=function(e,t){return e.nextSibling===t||e.previousSibling===t},E=function(e,t){t=t||d.ok;var n=[];return e.previousSibling&&t(e.previousSibling)&&n.push(e.previousSibling),n.push(e),e.nextSibling&&t(e.nextSibling)&&n.push(e.nextSibling),n},F=l.isMSIE&&l.browserVersion<11?"&nbsp;":"<br>",I=function(e){return a(e)?e.nodeValue.length:e.childNodes.length},M=function(e){var t=I(e);return 0===t?!0:a(e)||1!==t||e.innerHTML!==F?c.all(e.childNodes,a)&&""===e.innerHTML?!0:!1:!0},R=function(e){s(e)||I(e)||(e.innerHTML=F)},A=function(e,n){for(;e;){if(n(e))return e;if(t(e))break;e=e.parentNode}return null},D=function(e,n){for(e=e.parentNode;e&&1===I(e);){if(n(e))return e;if(t(e))break;e=e.parentNode}return null},H=function(e,n){n=n||d.fail;var o=[];return A(e,function(e){return t(e)||o.push(e),n(e)}),o},B=function(e,t){var n=H(e);return c.last(n.filter(t))},z=function(t,n){for(var o=H(t),i=n;i;i=i.parentNode)if(e.inArray(i,o)>-1)return i;return null},U=function(e,t){t=t||d.fail;for(var n=[];e&&!t(e);)n.push(e),e=e.previousSibling;return n},O=function(e,t){t=t||d.fail;for(var n=[];e&&!t(e);)n.push(e),e=e.nextSibling;return n},j=function(e,t){var n=[];return t=t||d.ok,function o(i){e!==i&&t(i)&&n.push(i);for(var r=0,a=i.childNodes.length;a>r;r++)o(i.childNodes[r])}(e),n},K=function(t,n){var o=t.parentNode,i=e("<"+n+">")[0];return o.insertBefore(i,t),i.appendChild(t),i},V=function(e,t){var n=t.nextSibling,o=t.parentNode;return n?o.insertBefore(e,n):o.appendChild(e),e},q=function(t,n){return e.each(n,function(e,n){t.appendChild(n)}),t},W=function(e){return 0===e.offset},_=function(e){return e.offset===I(e.node)},Z=function(e){return W(e)||_(e)},G=function(e,t){for(;e&&e!==t;){if(0!==X(e))return!1;e=e.parentNode}return!0},J=function(e,t){for(;e&&e!==t;){if(X(e)!==I(e.parentNode)-1)return!1;e=e.parentNode}return!0},Y=function(e,t){return W(e)&&G(e.node,t)},Q=function(e,t){return _(e)&&J(e.node,t)},X=function(e){for(var t=0;e=e.previousSibling;)t+=1;return t},$=function(e){return!!(e&&e.childNodes&&e.childNodes.length)},et=function(e,n){var o,i;if(0===e.offset){if(t(e.node))return null;o=e.node.parentNode,i=X(e.node)}else $(e.node)?(o=e.node.childNodes[e.offset-1],i=I(o)):(o=e.node,i=n?0:e.offset-1);return{node:o,offset:i}},tt=function(e,n){var o,i;if(I(e.node)===e.offset){if(t(e.node))return null;o=e.node.parentNode,i=X(e.node)+1}else $(e.node)?(o=e.node.childNodes[e.offset],i=0):(o=e.node,i=n?I(e.node):e.offset+1);return{node:o,offset:i}},nt=function(e,t){return e.node===t.node&&e.offset===t.offset},ot=function(e){if(a(e.node)||!$(e.node)||M(e.node))return!0;var t=e.node.childNodes[e.offset-1],n=e.node.childNodes[e.offset];return t&&!s(t)||n&&!s(n)?!1:!0},it=function(e,t){for(;e;){if(t(e))return e;e=et(e)}return null},rt=function(e,t){for(;e;){if(t(e))return e;e=tt(e)}return null},at=function(e){if(!a(e.node))return!1;var t=e.node.nodeValue.charAt(e.offset-1);return t&&" "!==t&&t!==u},st=function(e,t,n,o){for(var i=e;i&&(n(i),!nt(i,t));){var r=o&&e.node!==i.node&&t.node!==i.node;i=tt(i,r)}},lt=function(e,t){var n=H(t,d.eq(e));return n.map(X).reverse()},dt=function(e,t){for(var n=e,o=0,i=t.length;i>o;o++)n=n.childNodes.length<=t[o]?n.childNodes[n.childNodes.length-1]:n.childNodes[t[o]];return n},ct=function(e,t){var n=t&&t.isSkipPaddingBlankHTML,o=t&&t.isNotSplitEdgePoint;if(Z(e)&&(a(e.node)||o)){if(W(e))return e.node;if(_(e))return e.node.nextSibling}if(a(e.node))return e.node.splitText(e.offset);var i=e.node.childNodes[e.offset],r=V(e.node.cloneNode(!1),e.node);return q(r,O(i)),n||(R(e.node),R(r)),r},ut=function(e,t,n){var o=H(t.node,d.eq(e));return o.length?1===o.length?ct(t,n):o.reduce(function(e,o){return e===t.node&&(e=ct(t,n)),ct({node:o,offset:e?h.position(e):I(o)},n)}):null},ft=function(e,t){var n,o,i=t?p:w,r=H(e.node,i),a=c.last(r)||e.node;i(a)?(n=r[r.length-2],o=a):(n=a,o=n.parentNode);var s=n&&ut(n,e,{isSkipPaddingBlankHTML:t,isNotSplitEdgePoint:t});return s||o!==e.node||(s=e.node.childNodes[e.offset]),{rightNode:s,container:o}},ht=function(e){return document.createElement(e)},pt=function(e){return document.createTextNode(e)},vt=function(e,t){if(e&&e.parentNode){if(e.removeNode)return e.removeNode(t);var n=e.parentNode;if(!t){var o,i,r=[];for(o=0,i=e.childNodes.length;i>o;o++)r.push(e.childNodes[o]);for(o=0,i=r.length;i>o;o++)n.insertBefore(r[o],e)}n.removeChild(e)}},gt=function(e,n){for(;e&&!t(e)&&n(e);){var o=e.parentNode;vt(e),e=o}},mt=function(e,t){if(e.nodeName.toUpperCase()===t.toUpperCase())return e;var n=ht(t);return e.style.cssText&&(n.style.cssText=e.style.cssText),q(n,c.from(e.childNodes)),V(n,e),vt(e),n},bt=r("TEXTAREA"),yt=function(e,t){var n=bt(e[0])?e.val():e.html();return t?n.replace(/[\n\r]/g,""):n},Ct=function(t,n){var o=yt(t);if(n){var i=/<(\/?)(\b(?!!)[^>\s]*)(.*?)(\s*\/?>)/g;o=o.replace(i,function(e,t,n){n=n.toUpperCase();var o=/^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(n)&&!!t,i=/^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(n);return e+(o||i?"\n":"")}),o=e.trim(o)}return o};return{NBSP_CHAR:u,ZERO_WIDTH_NBSP_CHAR:f,blank:F,emptyPara:"<p>"+F+"</p>",makePredByNodeName:r,isEditable:t,isControlSizing:n,buildLayoutInfo:o,makeLayoutInfo:i,isText:a,isVoid:s,isPara:p,isPurePara:g,isInline:b,isBlock:d.not(b),isBodyInline:S,isBody:P,isParaInline:T,isList:y,isTable:m,isCell:k,isBlockquote:x,isBodyContainer:w,isAnchor:N,isDiv:r("DIV"),isLi:v,isBR:r("BR"),isSpan:r("SPAN"),isB:r("B"),isU:r("U"),isS:r("S"),isI:r("I"),isImg:r("IMG"),isTextarea:bt,isEmpty:M,isEmptyAnchor:d.and(N,M),isClosestSibling:L,withClosestSiblings:E,nodeLength:I,isLeftEdgePoint:W,isRightEdgePoint:_,isEdgePoint:Z,isLeftEdgeOf:G,isRightEdgeOf:J,isLeftEdgePointOf:Y,isRightEdgePointOf:Q,prevPoint:et,nextPoint:tt,isSamePoint:nt,isVisiblePoint:ot,prevPointUntil:it,nextPointUntil:rt,isCharPoint:at,walkPoint:st,ancestor:A,singleChildAncestor:D,listAncestor:H,lastAncestor:B,listNext:O,listPrev:U,listDescendant:j,commonAncestor:z,wrap:K,insertAfter:V,appendChildNodes:q,position:X,hasChildren:$,makeOffsetPath:lt,fromOffsetPath:dt,splitTree:ut,splitPoint:ft,create:ht,createText:pt,remove:vt,removeWhile:gt,replace:mt,html:Ct,value:yt}}(),p=function(){var t=function(e,t){var n,o,i=e.parentElement(),r=document.body.createTextRange(),a=c.from(i.childNodes);for(n=0;n<a.length;n++)if(!h.isText(a[n])){if(r.moveToElementText(a[n]),r.compareEndPoints("StartToStart",e)>=0)break;o=a[n]}if(0!==n&&h.isText(a[n-1])){var s=document.body.createTextRange(),l=null;s.moveToElementText(o||i),s.collapse(!o),l=o?o.nextSibling:i.firstChild;var d=e.duplicate();d.setEndPoint("StartToStart",s);for(var u=d.text.replace(/[\r\n]/g,"").length;u>l.nodeValue.length&&l.nextSibling;)u-=l.nodeValue.length,l=l.nextSibling;{l.nodeValue}t&&l.nextSibling&&h.isText(l.nextSibling)&&u===l.nodeValue.length&&(u-=l.nodeValue.length,l=l.nextSibling),i=l,n=u}return{cont:i,offset:n}},n=function(e){var t=function(e,n){var o,i;if(h.isText(e)){var r=h.listPrev(e,d.not(h.isText)),a=c.last(r).previousSibling;o=a||e.parentNode,n+=c.sum(c.tail(r),h.nodeLength),i=!a}else{if(o=e.childNodes[n]||e,h.isText(o))return t(o,0);n=0,i=!1}return{node:o,collapseToStart:i,offset:n}},n=document.body.createTextRange(),o=t(e.node,e.offset);return n.moveToElementText(o.node),n.collapse(o.collapseToStart),n.moveStart("character",o.offset),n},o=function(t,i,r,a){this.sc=t,this.so=i,this.ec=r,this.eo=a;var s=function(){if(l.isW3CRangeSupport){var e=document.createRange();return e.setStart(t,i),e.setEnd(r,a),e}var o=n({node:t,offset:i});return o.setEndPoint("EndToEnd",n({node:r,offset:a})),o};this.getPoints=function(){return{sc:t,so:i,ec:r,eo:a}},this.getStartPoint=function(){return{node:t,offset:i}},this.getEndPoint=function(){return{node:r,offset:a}},this.select=function(){var e=s();if(l.isW3CRangeSupport){var t=document.getSelection();t.rangeCount>0&&t.removeAllRanges(),t.addRange(e)}else e.select();return this},this.normalize=function(){var e=function(e,t){if(h.isVisiblePoint(e)&&!h.isEdgePoint(e)||h.isVisiblePoint(e)&&h.isRightEdgePoint(e)&&!t||h.isVisiblePoint(e)&&h.isLeftEdgePoint(e)&&t||h.isVisiblePoint(e)&&h.isBlock(e.node)&&h.isEmpty(e.node))return e;var n=h.ancestor(e.node,h.isBlock);if((h.isLeftEdgePointOf(e,n)||h.isVoid(h.prevPoint(e).node))&&!t||(h.isRightEdgePointOf(e,n)||h.isVoid(h.nextPoint(e).node))&&t){if(h.isVisiblePoint(e))return e;t=!t}var o=t?h.nextPointUntil(h.nextPoint(e),h.isVisiblePoint):h.prevPointUntil(h.prevPoint(e),h.isVisiblePoint);return o||e},t=e(this.getEndPoint(),!1),n=this.isCollapsed()?t:e(this.getStartPoint(),!0);return new o(n.node,n.offset,t.node,t.offset)},this.nodes=function(e,t){e=e||d.ok;var n=t&&t.includeAncestor,o=t&&t.fullyContains,i=this.getStartPoint(),r=this.getEndPoint(),a=[],s=[];return h.walkPoint(i,r,function(t){if(!h.isEditable(t.node)){var i;o?(h.isLeftEdgePoint(t)&&s.push(t.node),h.isRightEdgePoint(t)&&c.contains(s,t.node)&&(i=t.node)):i=n?h.ancestor(t.node,e):t.node,i&&e(i)&&a.push(i)}},!0),c.unique(a)},this.commonAncestor=function(){return h.commonAncestor(t,r)},this.expand=function(e){var n=h.ancestor(t,e),s=h.ancestor(r,e);if(!n&&!s)return new o(t,i,r,a);var l=this.getPoints();return n&&(l.sc=n,l.so=0),s&&(l.ec=s,l.eo=h.nodeLength(s)),new o(l.sc,l.so,l.ec,l.eo)},this.collapse=function(e){return e?new o(t,i,t,i):new o(r,a,r,a)},this.splitText=function(){var e=t===r,n=this.getPoints();return h.isText(r)&&!h.isEdgePoint(this.getEndPoint())&&r.splitText(a),h.isText(t)&&!h.isEdgePoint(this.getStartPoint())&&(n.sc=t.splitText(i),n.so=0,e&&(n.ec=n.sc,n.eo=a-i)),new o(n.sc,n.so,n.ec,n.eo)},this.deleteContents=function(){if(this.isCollapsed())return this;var t=this.splitText(),n=t.nodes(null,{fullyContains:!0}),i=h.prevPointUntil(t.getStartPoint(),function(e){return!c.contains(n,e.node)}),r=[];return e.each(n,function(e,t){var n=t.parentNode;i.node!==n&&1===h.nodeLength(n)&&r.push(n),h.remove(t,!1)}),e.each(r,function(e,t){h.remove(t,!1)}),new o(i.node,i.offset,i.node,i.offset).normalize()};var u=function(e){return function(){var n=h.ancestor(t,e);return!!n&&n===h.ancestor(r,e)}};this.isOnEditable=u(h.isEditable),this.isOnList=u(h.isList),this.isOnAnchor=u(h.isAnchor),this.isOnCell=u(h.isCell),this.isLeftEdgeOf=function(e){if(!h.isLeftEdgePoint(this.getStartPoint()))return!1;var t=h.ancestor(this.sc,e);return t&&h.isLeftEdgeOf(this.sc,t)},this.isCollapsed=function(){return t===r&&i===a},this.wrapBodyInlineWithPara=function(){if(h.isBodyContainer(t)&&h.isEmpty(t))return t.innerHTML=h.emptyPara,new o(t.firstChild,0,t.firstChild,0);var e=this.normalize();if(h.isParaInline(t)||h.isPara(t))return e;var n;if(h.isInline(e.sc)){var i=h.listAncestor(e.sc,d.not(h.isInline));n=c.last(i),h.isInline(n)||(n=i[i.length-2]||e.sc.childNodes[e.so])}else n=e.sc.childNodes[e.so>0?e.so-1:0];var r=h.listPrev(n,h.isParaInline).reverse();if(r=r.concat(h.listNext(n.nextSibling,h.isParaInline)),r.length){var a=h.wrap(c.head(r),"p");h.appendChildNodes(a,c.tail(r))}return this.normalize()},this.insertNode=function(e){var t=this.wrapBodyInlineWithPara().deleteContents(),n=h.splitPoint(t.getStartPoint(),h.isInline(e));return n.rightNode?n.rightNode.parentNode.insertBefore(e,n.rightNode):n.container.appendChild(e),e},this.pasteHTML=function(t){var n=e("<div></div>").html(t)[0],o=c.from(n.childNodes),i=this.wrapBodyInlineWithPara().deleteContents();return o.reverse().map(function(e){return i.insertNode(e)}).reverse()},this.toString=function(){var e=s();return l.isW3CRangeSupport?e.toString():e.text},this.getWordRange=function(e){var t=this.getEndPoint();if(!h.isCharPoint(t))return this;var n=h.prevPointUntil(t,function(e){return!h.isCharPoint(e)});return e&&(t=h.nextPointUntil(t,function(e){return!h.isCharPoint(e)})),new o(n.node,n.offset,t.node,t.offset)},this.bookmark=function(e){return{s:{path:h.makeOffsetPath(e,t),offset:i},e:{path:h.makeOffsetPath(e,r),offset:a}}},this.paraBookmark=function(e){return{s:{path:c.tail(h.makeOffsetPath(c.head(e),t)),offset:i},e:{path:c.tail(h.makeOffsetPath(c.last(e),r)),offset:a}}},this.getClientRects=function(){var e=s();return e.getClientRects()}};return{create:function(e,n,i,r){if(arguments.length)2===arguments.length&&(i=e,r=n);else if(l.isW3CRangeSupport){var a=document.getSelection();if(!a||0===a.rangeCount)return null;if(h.isBody(a.anchorNode))return null;var s=a.getRangeAt(0);e=s.startContainer,n=s.startOffset,i=s.endContainer,r=s.endOffset}else{var d=document.selection.createRange(),c=d.duplicate();c.collapse(!1);var u=d;u.collapse(!0);var f=t(u,!0),p=t(c,!1);h.isText(f.node)&&h.isLeftEdgePoint(f)&&h.isTextNode(p.node)&&h.isRightEdgePoint(p)&&p.node.nextSibling===f.node&&(f=p),e=f.cont,n=f.offset,i=p.cont,r=p.offset}return new o(e,n,i,r)},createFromNode:function(e){var t=e,n=0,o=e,i=h.nodeLength(o);return h.isVoid(t)&&(n=h.listPrev(t).length-1,t=t.parentNode),h.isBR(o)?(i=h.listPrev(o).length-1,o=o.parentNode):h.isVoid(o)&&(i=h.listPrev(o).length,o=o.parentNode),this.create(t,n,o,i)},createFromNodeBefore:function(e){return this.createFromNode(e).collapse(!0)},createFromNodeAfter:function(e){return this.createFromNode(e).collapse()},createFromBookmark:function(e,t){var n=h.fromOffsetPath(e,t.s.path),i=t.s.offset,r=h.fromOffsetPath(e,t.e.path),a=t.e.offset;return new o(n,i,r,a)},createFromParaBookmark:function(e,t){var n=e.s.offset,i=e.e.offset,r=h.fromOffsetPath(c.head(t),e.s.path),a=h.fromOffsetPath(c.last(t),e.e.path);return new o(r,n,a,i)}}}(),v={version:"0.6.16",options:{width:null,height:null,minHeight:null,maxHeight:null,focus:!1,tabsize:4,styleWithSpan:!0,disableLinkTarget:!1,disableDragAndDrop:!1,disableResizeEditor:!1,disableResizeImage:!1,shortcuts:!0,textareaAutoSync:!0,placeholder:!1,prettifyHtml:!0,iconPrefix:"fa fa-",icons:{font:{bold:"bold",italic:"italic",underline:"underline",clear:"eraser",height:"text-height",strikethrough:"strikethrough",superscript:"superscript",subscript:"subscript"},image:{image:"picture-o",floatLeft:"align-left",floatRight:"align-right",floatNone:"align-justify",shapeRounded:"square",shapeCircle:"circle-o",shapeThumbnail:"picture-o",shapeNone:"times",remove:"trash-o"},link:{link:"link",unlink:"unlink",edit:"edit"},table:{table:"table"},hr:{insert:"minus"},style:{style:"magic"},lists:{unordered:"list-ul",ordered:"list-ol"},options:{help:"question",fullscreen:"arrows-alt",codeview:"code"},paragraph:{paragraph:"align-left",outdent:"outdent",indent:"indent",left:"align-left",center:"align-center",right:"align-right",justify:"align-justify"},color:{recent:"font"},history:{undo:"undo",redo:"repeat"},misc:{check:"check"}},dialogsInBody:!1,codemirror:{mode:"text/html",htmlMode:!0,lineNumbers:!0},lang:"en-US",direction:null,toolbar:[["style",["style"]],["font",["bold","italic","underline","clear"]],["fontname",["fontname"]],["fontsize",["fontsize"]],["color",["color"]],["para",["ul","ol","paragraph"]],["height",["height"]],["table",["table"]],["insert",["link","picture","hr"]],["view",["fullscreen","codeview"]],["help",["help"]]],plugin:{},airMode:!1,airPopover:[["color",["color"]],["font",["bold","underline","clear"]],["para",["ul","paragraph"]],["table",["table"]],["insert",["link","picture"]]],styleTags:["p","blockquote","pre","h1","h2","h3","h4","h5","h6"],defaultFontName:"Helvetica Neue",fontNames:["Arial","Arial Black","Comic Sans MS","Courier New","Helvetica Neue","Helvetica","Impact","Lucida Grande","Tahoma","Times New Roman","Verdana"],fontNamesIgnoreCheck:[],fontSizes:["8","9","10","11","12","14","18","24","36"],colors:[["#000000","#424242","#636363","#9C9C94","#CEC6CE","#EFEFEF","#F7F7F7","#FFFFFF"],["#FF0000","#FF9C00","#FFFF00","#00FF00","#00FFFF","#0000FF","#9C00FF","#FF00FF"],["#F7C6CE","#FFE7CE","#FFEFC6","#D6EFD6","#CEDEE7","#CEE7F7","#D6D6E7","#E7D6DE"],["#E79C9C","#FFC69C","#FFE79C","#B5D6A5","#A5C6CE","#9CC6EF","#B5A5D6","#D6A5BD"],["#E76363","#F7AD6B","#FFD663","#94BD7B","#73A5AD","#6BADDE","#8C7BC6","#C67BA5"],["#CE0000","#E79439","#EFC631","#6BA54A","#4A7B8C","#3984C6","#634AA5","#A54A7B"],["#9C0000","#B56308","#BD9400","#397B21","#104A5A","#085294","#311873","#731842"],["#630000","#7B3900","#846300","#295218","#083139","#003163","#21104A","#4A1031"]],lineHeights:["1.0","1.2","1.4","1.5","1.6","1.8","2.0","3.0"],insertTableMaxSize:{col:10,row:10},maximumImageFileSize:null,oninit:null,onfocus:null,onblur:null,onenter:null,onkeyup:null,onkeydown:null,onImageUpload:null,onImageUploadError:null,onMediaDelete:null,onToolbarClick:null,onsubmit:null,onCreateLink:function(e){return-1!==e.indexOf("@")&&-1===e.indexOf(":")&&(e="mailto:"+e),e},keyMap:{pc:{ENTER:"insertParagraph","CTRL+Z":"undo","CTRL+Y":"redo",TAB:"tab","SHIFT+TAB":"untab","CTRL+B":"bold","CTRL+I":"italic","CTRL+U":"underline","CTRL+SHIFT+S":"strikethrough","CTRL+BACKSLASH":"removeFormat","CTRL+SHIFT+L":"justifyLeft","CTRL+SHIFT+E":"justifyCenter","CTRL+SHIFT+R":"justifyRight","CTRL+SHIFT+J":"justifyFull","CTRL+SHIFT+NUM7":"insertUnorderedList","CTRL+SHIFT+NUM8":"insertOrderedList","CTRL+LEFTBRACKET":"outdent","CTRL+RIGHTBRACKET":"indent","CTRL+NUM0":"formatPara","CTRL+NUM1":"formatH1","CTRL+NUM2":"formatH2","CTRL+NUM3":"formatH3","CTRL+NUM4":"formatH4","CTRL+NUM5":"formatH5","CTRL+NUM6":"formatH6","CTRL+ENTER":"insertHorizontalRule","CTRL+K":"showLinkDialog"},mac:{ENTER:"insertParagraph","CMD+Z":"undo","CMD+SHIFT+Z":"redo",TAB:"tab","SHIFT+TAB":"untab","CMD+B":"bold","CMD+I":"italic","CMD+U":"underline","CMD+SHIFT+S":"strikethrough","CMD+BACKSLASH":"removeFormat","CMD+SHIFT+L":"justifyLeft","CMD+SHIFT+E":"justifyCenter","CMD+SHIFT+R":"justifyRight","CMD+SHIFT+J":"justifyFull","CMD+SHIFT+NUM7":"insertUnorderedList","CMD+SHIFT+NUM8":"insertOrderedList","CMD+LEFTBRACKET":"outdent","CMD+RIGHTBRACKET":"indent","CMD+NUM0":"formatPara","CMD+NUM1":"formatH1","CMD+NUM2":"formatH2","CMD+NUM3":"formatH3","CMD+NUM4":"formatH4","CMD+NUM5":"formatH5","CMD+NUM6":"formatH6","CMD+ENTER":"insertHorizontalRule","CMD+K":"showLinkDialog"}}},lang:{"en-US":{font:{bold:"Bold",italic:"Italic",underline:"Underline",clear:"Remove Font Style",height:"Line Height",name:"Font Family",strikethrough:"Strikethrough",subscript:"Subscript",superscript:"Superscript",size:"Font Size"},image:{image:"Picture",insert:"Insert Image",resizeFull:"Resize Full",resizeHalf:"Resize Half",resizeQuarter:"Resize Quarter",floatLeft:"Float Left",floatRight:"Float Right",floatNone:"Float None",shapeRounded:"Shape: Rounded",shapeCircle:"Shape: Circle",shapeThumbnail:"Shape: Thumbnail",shapeNone:"Shape: None",dragImageHere:"Drag image or text here",dropImage:"Drop image or Text",selectFromFiles:"Select from files",maximumFileSize:"Maximum file size",maximumFileSizeError:"Maximum file size exceeded.",url:"Image URL",remove:"Remove Image"},link:{link:"Link",insert:"Insert Link",unlink:"Unlink",edit:"Edit",textToDisplay:"Text to display",url:"To what URL should this link go?",openInNewWindow:"Open in new window"},table:{table:"Table"},hr:{insert:"Insert Horizontal Rule"},style:{style:"Style",normal:"Normal",blockquote:"Quote",pre:"Code",h1:"Header 1",h2:"Header 2",h3:"Header 3",h4:"Header 4",h5:"Header 5",h6:"Header 6"},lists:{unordered:"Unordered list",ordered:"Ordered list"},options:{help:"Help",fullscreen:"Full Screen",codeview:"Code View"},paragraph:{paragraph:"Paragraph",outdent:"Outdent",indent:"Indent",left:"Align left",center:"Align center",right:"Align right",justify:"Justify full"},color:{recent:"Recent Color",more:"More Color",background:"Background Color",foreground:"Foreground Color",transparent:"Transparent",setTransparent:"Set transparent",reset:"Reset",resetToDefault:"Reset to default"},shortcut:{shortcuts:"Keyboard shortcuts",close:"Close",textFormatting:"Text formatting",action:"Action",paragraphFormatting:"Paragraph formatting",documentStyle:"Document Style",extraKeys:"Extra keys"},history:{undo:"Undo",redo:"Redo"}}}},g=function(){var t=function(t){return e.Deferred(function(n){e.extend(new FileReader,{onload:function(e){var t=e.target.result;n.resolve(t)},onerror:function(){n.reject(this)}}).readAsDataURL(t)}).promise()},n=function(t,n){return e.Deferred(function(o){var i=e("<img>");i.one("load",function(){i.off("error abort"),o.resolve(i)}).one("error abort",function(){i.off("load").detach(),o.reject(i)}).css({display:"none"}).appendTo(document.body).attr({src:t,"data-filename":n})}).promise()};return{readFileAsDataURL:t,createImage:n}}(),m=function(){var e={BACKSPACE:8,TAB:9,ENTER:13,SPACE:32,NUM0:48,NUM1:49,NUM2:50,NUM3:51,NUM4:52,NUM5:53,NUM6:54,NUM7:55,NUM8:56,B:66,E:69,I:73,J:74,K:75,L:76,R:82,S:83,U:85,V:86,Y:89,Z:90,SLASH:191,LEFTBRACKET:219,BACKSLASH:220,RIGHTBRACKET:221};return{isEdit:function(e){return c.contains([8,9,13,32],e)},isMove:function(e){return c.contains([37,38,39,40],e)},nameFromCode:d.invertObject(e),code:e}}(),b=function(e){var t=[],n=-1,o=e[0],i=function(){var t=p.create(),n={s:{path:[],offset:0},e:{path:[],offset:0}};return{contents:e.html(),bookmark:t?t.bookmark(o):n}},r=function(t){null!==t.contents&&e.html(t.contents),null!==t.bookmark&&p.createFromBookmark(o,t.bookmark).select()};this.undo=function(){e.html()!==t[n].contents&&this.recordUndo(),n>0&&(n--,r(t[n]))},this.redo=function(){t.length-1>n&&(n++,r(t[n]))},this.recordUndo=function(){n++,t.length>n&&(t=t.slice(0,n)),t.push(i())},this.recordUndo()},y=function(){var t=function(t,n){if(l.jqueryVersion<1.9){var o={};return e.each(n,function(e,n){o[n]=t.css(n)}),o}return t.css.call(t,n)};this.fromNode=function(e){var n=["font-family","font-size","text-align","list-style-type","line-height"],o=t(e,n)||{};return o["font-size"]=parseInt(o["font-size"],10),o},this.stylePara=function(t,n){e.each(t.nodes(h.isPara,{includeAncestor:!0}),function(t,o){e(o).css(n)})},this.styleNodes=function(t,n){t=t.splitText();var o=n&&n.nodeName||"SPAN",i=!(!n||!n.expandClosestSibling),r=!(!n||!n.onlyPartialContains);if(t.isCollapsed())return[t.insertNode(h.create(o))];var a=h.makePredByNodeName(o),s=t.nodes(h.isText,{fullyContains:!0}).map(function(e){return h.singleChildAncestor(e,a)||h.wrap(e,o)});if(i){if(r){var l=t.nodes();a=d.and(a,function(e){return c.contains(l,e)})}return s.map(function(t){var n=h.withClosestSiblings(t,a),o=c.head(n),i=c.tail(n);return e.each(i,function(e,t){h.appendChildNodes(o,t.childNodes),h.remove(t)}),c.head(n)})}return s},this.current=function(t){var n=e(h.isText(t.sc)?t.sc.parentNode:t.sc),o=this.fromNode(n);if(o["font-bold"]=document.queryCommandState("bold")?"bold":"normal",o["font-italic"]=document.queryCommandState("italic")?"italic":"normal",o["font-underline"]=document.queryCommandState("underline")?"underline":"normal",o["font-strikethrough"]=document.queryCommandState("strikeThrough")?"strikethrough":"normal",o["font-superscript"]=document.queryCommandState("superscript")?"superscript":"normal",o["font-subscript"]=document.queryCommandState("subscript")?"subscript":"normal",t.isOnList()){var i=["circle","disc","disc-leading-zero","square"],r=e.inArray(o["list-style-type"],i)>-1;o["list-style"]=r?"unordered":"ordered"}else o["list-style"]="none";var a=h.ancestor(t.sc,h.isPara);if(a&&a.style["line-height"])o["line-height"]=a.style.lineHeight;else{var s=parseInt(o["line-height"],10)/parseInt(o["font-size"],10);o["line-height"]=s.toFixed(1)}return o.anchor=t.isOnAnchor()&&h.ancestor(t.sc,h.isAnchor),o.ancestors=h.listAncestor(t.sc,h.isEditable),o.range=t,o}},C=function(){this.insertOrderedList=function(){this.toggleList("OL")},this.insertUnorderedList=function(){this.toggleList("UL")},this.indent=function(){var t=this,n=p.create().wrapBodyInlineWithPara(),o=n.nodes(h.isPara,{includeAncestor:!0}),i=c.clusterBy(o,d.peq2("parentNode"));e.each(i,function(n,o){var i=c.head(o);h.isLi(i)?t.wrapList(o,i.parentNode.nodeName):e.each(o,function(t,n){e(n).css("marginLeft",function(e,t){return(parseInt(t,10)||0)+25})})}),n.select()},this.outdent=function(){var t=this,n=p.create().wrapBodyInlineWithPara(),o=n.nodes(h.isPara,{includeAncestor:!0}),i=c.clusterBy(o,d.peq2("parentNode"));e.each(i,function(n,o){var i=c.head(o);h.isLi(i)?t.releaseList([o]):e.each(o,function(t,n){e(n).css("marginLeft",function(e,t){return t=parseInt(t,10)||0,t>25?t-25:""})})}),n.select()},this.toggleList=function(t){var n=this,o=p.create().wrapBodyInlineWithPara(),i=o.nodes(h.isPara,{includeAncestor:!0}),r=o.paraBookmark(i),a=c.clusterBy(i,d.peq2("parentNode"));if(c.find(i,h.isPurePara)){var s=[];e.each(a,function(e,o){s=s.concat(n.wrapList(o,t))}),i=s}else{var l=o.nodes(h.isList,{includeAncestor:!0}).filter(function(n){return!e.nodeName(n,t)});l.length?e.each(l,function(e,n){h.replace(n,t)}):i=this.releaseList(a,!0)}p.createFromParaBookmark(r,i).select()},this.wrapList=function(e,t){var n=c.head(e),o=c.last(e),i=h.isList(n.previousSibling)&&n.previousSibling,r=h.isList(o.nextSibling)&&o.nextSibling,a=i||h.insertAfter(h.create(t||"UL"),o);return e=e.map(function(e){return h.isPurePara(e)?h.replace(e,"LI"):e}),h.appendChildNodes(a,e),r&&(h.appendChildNodes(a,c.from(r.childNodes)),h.remove(r)),e},this.releaseList=function(t,n){var o=[];return e.each(t,function(t,i){var r=c.head(i),a=c.last(i),s=n?h.lastAncestor(r,h.isList):r.parentNode,l=s.childNodes.length>1?h.splitTree(s,{node:a.parentNode,offset:h.position(a)+1},{isSkipPaddingBlankHTML:!0}):null,d=h.splitTree(s,{node:r.parentNode,offset:h.position(r)},{isSkipPaddingBlankHTML:!0});i=n?h.listDescendant(d,h.isLi):c.from(d.childNodes).filter(h.isLi),(n||!h.isList(s.parentNode))&&(i=i.map(function(e){return h.replace(e,"P")})),e.each(c.from(i).reverse(),function(e,t){h.insertAfter(t,s)});var u=c.compact([s,d,l]);e.each(u,function(t,n){var o=[n].concat(h.listDescendant(n,h.isList));e.each(o.reverse(),function(e,t){h.nodeLength(t)||h.remove(t,!0)})}),o=o.concat(i)}),o}},k=function(){var t=new C;this.insertTab=function(e,t,n){var o=h.createText(new Array(n+1).join(h.NBSP_CHAR));t=t.deleteContents(),t.insertNode(o,!0),t=p.create(o,n),t.select()},this.insertParagraph=function(){var n=p.create();n=n.deleteContents(),n=n.wrapBodyInlineWithPara();var o,i=h.ancestor(n.sc,h.isPara);if(i){if(h.isEmpty(i)&&h.isLi(i))return void t.toggleList(i.parentNode.nodeName);o=h.splitTree(i,n.getStartPoint());var r=h.listDescendant(i,h.isEmptyAnchor);r=r.concat(h.listDescendant(o,h.isEmptyAnchor)),e.each(r,function(e,t){h.remove(t)})}else{var a=n.sc.childNodes[n.so];o=e(h.emptyPara)[0],a?n.sc.insertBefore(o,a):n.sc.appendChild(o)}p.create(o,0).normalize().select()}},x=function(){this.tab=function(e,t){var n=h.ancestor(e.commonAncestor(),h.isCell),o=h.ancestor(n,h.isTable),i=h.listDescendant(o,h.isCell),r=c[t?"prev":"next"](i,n);
    r&&p.create(r,0).select()},this.createTable=function(t,n){for(var o,i=[],r=0;t>r;r++)i.push("<td>"+h.blank+"</td>");o=i.join("");for(var a,s=[],l=0;n>l;l++)s.push("<tr>"+o+"</tr>");return a=s.join(""),e('<table class="table table-bordered">'+a+"</table>")[0]}},w="bogus",N=function(t){var n=this,o=new y,i=new x,r=new k,a=new C;this.createRange=function(e){return this.focus(e),p.create()},this.saveRange=function(e,t){this.focus(e),e.data("range",p.create()),t&&p.create().collapse().select()},this.saveNode=function(e){for(var t=[],n=0,o=e[0].childNodes.length;o>n;n++)t.push(e[0].childNodes[n]);e.data("childNodes",t)},this.restoreRange=function(e){var t=e.data("range");t&&(t.select(),this.focus(e))},this.restoreNode=function(e){e.html("");for(var t=e.data("childNodes"),n=0,o=t.length;o>n;n++)e[0].appendChild(t[n])},this.currentStyle=function(e){var t=p.create(),n=t&&t.isOnEditable()?o.current(t.normalize()):{};return h.isImg(e)&&(n.image=e),n},this.styleFromNode=function(e){return o.fromNode(e)};var s=function(e){var n=h.makeLayoutInfo(e).holder();t.bindCustomEvent(n,e.data("callbacks"),"before.command")(e.html(),e)},d=function(e){var n=h.makeLayoutInfo(e).holder();t.bindCustomEvent(n,e.data("callbacks"),"change")(e.html(),e)};this.undo=function(e){s(e),e.data("NoteHistory").undo(),d(e)},this.redo=function(e){s(e),e.data("NoteHistory").redo(),d(e)};for(var u=this.beforeCommand=function(e){s(e),n.focus(e)},f=this.afterCommand=function(e,t){e.data("NoteHistory").recordUndo(),t||d(e)},v=["bold","italic","underline","strikethrough","superscript","subscript","justifyLeft","justifyCenter","justifyRight","justifyFull","formatBlock","removeFormat","backColor","foreColor","fontName"],m=0,b=v.length;b>m;m++)this[v[m]]=function(e){return function(t,n){u(t),document.execCommand(e,!1,n),f(t,!0)}}(v[m]);this.tab=function(e,t){var n=this.createRange(e);n.isCollapsed()&&n.isOnCell()?i.tab(n):(u(e),r.insertTab(e,n,t.tabsize),f(e))},this.untab=function(e){var t=this.createRange(e);t.isCollapsed()&&t.isOnCell()&&i.tab(t,!0)},this.insertParagraph=function(e){u(e),r.insertParagraph(e),f(e)},this.insertOrderedList=function(e){u(e),a.insertOrderedList(e),f(e)},this.insertUnorderedList=function(e){u(e),a.insertUnorderedList(e),f(e)},this.indent=function(e){u(e),a.indent(e),f(e)},this.outdent=function(e){u(e),a.outdent(e),f(e)},this.insertImage=function(e,n,o){g.createImage(n,o).then(function(t){u(e),t.css({display:"",width:Math.min(e.width(),t.width())}),p.create().insertNode(t[0]),p.createFromNodeAfter(t[0]).select(),f(e)}).fail(function(){var n=h.makeLayoutInfo(e).holder();t.bindCustomEvent(n,e.data("callbacks"),"image.upload.error")()})},this.insertNode=function(e,t){u(e),p.create().insertNode(t),p.createFromNodeAfter(t).select(),f(e)},this.insertText=function(e,t){u(e);var n=p.create().insertNode(h.createText(t));p.create(n,h.nodeLength(n)).select(),f(e)},this.pasteHTML=function(e,t){u(e);var n=p.create().pasteHTML(t);p.createFromNodeAfter(c.last(n)).select(),f(e)},this.formatBlock=function(e,t){u(e),t=l.isMSIE?"<"+t+">":t,document.execCommand("FormatBlock",!1,t),f(e)},this.formatPara=function(e){u(e),this.formatBlock(e,"P"),f(e)};for(var m=1;6>=m;m++)this["formatH"+m]=function(e){return function(t){this.formatBlock(t,"H"+e)}}(m);this.fontSize=function(t,n){var i=p.create();if(i.isCollapsed()){var r=o.styleNodes(i),a=c.head(r);e(r).css({"font-size":n+"px"}),a&&!h.nodeLength(a)&&(a.innerHTML=h.ZERO_WIDTH_NBSP_CHAR,p.createFromNodeAfter(a.firstChild).select(),t.data(w,a))}else u(t),e(o.styleNodes(i)).css({"font-size":n+"px"}),f(t)},this.insertHorizontalRule=function(t){u(t);var n=p.create(),o=n.insertNode(e("<HR/>")[0]);o.nextSibling&&p.create(o.nextSibling,0).normalize().select(),f(t)},this.removeBogus=function(e){var t=e.data(w);if(t){var n=c.find(c.from(t.childNodes),h.isText),o=n.nodeValue.indexOf(h.ZERO_WIDTH_NBSP_CHAR);-1!==o&&n.deleteData(o,1),h.isEmpty(t)&&h.remove(t),e.removeData(w)}},this.lineHeight=function(e,t){u(e),o.stylePara(p.create(),{lineHeight:t}),f(e)},this.unlink=function(e){var t=this.createRange(e);if(t.isOnAnchor()){var n=h.ancestor(t.sc,h.isAnchor);t=p.createFromNode(n),t.select(),u(e),document.execCommand("unlink"),f(e)}},this.createLink=function(t,n,i){var r=n.url,a=n.text,s=n.isNewWindow,l=n.range||this.createRange(t),d=l.toString()!==a;i=i||h.makeLayoutInfo(t).editor().data("options"),u(t),i.onCreateLink&&(r=i.onCreateLink(r));var v=[];if(d){var g=l.insertNode(e("<A>"+a+"</A>")[0]);v.push(g)}else v=o.styleNodes(l,{nodeName:"A",expandClosestSibling:!0,onlyPartialContains:!0});e.each(v,function(t,n){e(n).attr("href",r),s?e(n).attr("target","_blank"):e(n).removeAttr("target")});var m=p.createFromNodeBefore(c.head(v)),b=m.getStartPoint(),y=p.createFromNodeAfter(c.last(v)),C=y.getEndPoint();p.create(b.node,b.offset,C.node,C.offset).select(),f(t)},this.getLinkInfo=function(t){this.focus(t);var n=p.create().expand(h.isAnchor),o=e(c.head(n.nodes(h.isAnchor)));return{range:n,text:n.toString(),isNewWindow:o.length?"_blank"===o.attr("target"):!1,url:o.length?o.attr("href"):""}},this.color=function(e,t){var n=JSON.parse(t),o=n.foreColor,i=n.backColor;u(e),o&&document.execCommand("foreColor",!1,o),i&&document.execCommand("backColor",!1,i),f(e)},this.insertTable=function(e,t){var n=t.split("x");u(e);var o=p.create().deleteContents();o.insertNode(i.createTable(n[0],n[1])),f(e)},this.floatMe=function(e,t,n){u(e),n.removeClass("pull-left pull-right"),t&&"none"!==t&&n.addClass("pull-"+t),n.css("float",t),f(e)},this.imageShape=function(e,t,n){u(e),n.removeClass("img-rounded img-circle img-thumbnail"),t&&n.addClass(t),f(e)},this.resize=function(e,t,n){u(e),n.css({width:100*t+"%",height:""}),f(e)},this.resizeTo=function(e,t,n){var o;if(n){var i=e.y/e.x,r=t.data("ratio");o={width:r>i?e.x:e.y/r,height:r>i?e.x*r:e.y}}else o={width:e.x,height:e.y};t.css(o)},this.removeMedia=function(n,o,i){u(n),i.detach(),t.bindCustomEvent(e(),n.data("callbacks"),"media.delete")(i,n),f(n)},this.focus=function(e){e.focus(),l.isFF&&!p.create().isOnEditable()&&p.createFromNode(e[0]).normalize().collapse().select()},this.isEmpty=function(e){return h.isEmpty(e[0])||h.emptyPara===e.html()}},T=function(){this.update=function(t,n){var o=function(t,n){t.find(".dropdown-menu li a").each(function(){var t=e(this).data("value")+""==n+"";this.className=t?"checked":""})},i=function(e,n){var o=t.find(e);o.toggleClass("active",n())};if(n.image){var r=e(n.image);i('button[data-event="imageShape"][data-value="img-rounded"]',function(){return r.hasClass("img-rounded")}),i('button[data-event="imageShape"][data-value="img-circle"]',function(){return r.hasClass("img-circle")}),i('button[data-event="imageShape"][data-value="img-thumbnail"]',function(){return r.hasClass("img-thumbnail")}),i('button[data-event="imageShape"]:not([data-value])',function(){return!r.is(".img-rounded, .img-circle, .img-thumbnail")});var a=r.css("float");i('button[data-event="floatMe"][data-value="left"]',function(){return"left"===a}),i('button[data-event="floatMe"][data-value="right"]',function(){return"right"===a}),i('button[data-event="floatMe"][data-value="none"]',function(){return"left"!==a&&"right"!==a});var s=r.attr("style");return i('button[data-event="resize"][data-value="1"]',function(){return!!/(^|\s)(max-)?width\s*:\s*100%/.test(s)}),i('button[data-event="resize"][data-value="0.5"]',function(){return!!/(^|\s)(max-)?width\s*:\s*50%/.test(s)}),void i('button[data-event="resize"][data-value="0.25"]',function(){return!!/(^|\s)(max-)?width\s*:\s*25%/.test(s)})}var d=t.find(".note-fontname");if(d.length){var c=n["font-family"];if(c){for(var u=c.split(","),f=0,h=u.length;h>f&&(c=u[f].replace(/[\'\"]/g,"").replace(/\s+$/,"").replace(/^\s+/,""),!l.isFontInstalled(c));f++);d.find(".note-current-fontname").text(c),o(d,c)}}var p=t.find(".note-fontsize");p.find(".note-current-fontsize").text(n["font-size"]),o(p,parseFloat(n["font-size"]));var v=t.find(".note-height");o(v,parseFloat(n["line-height"])),i('button[data-event="bold"]',function(){return"bold"===n["font-bold"]}),i('button[data-event="italic"]',function(){return"italic"===n["font-italic"]}),i('button[data-event="underline"]',function(){return"underline"===n["font-underline"]}),i('button[data-event="strikethrough"]',function(){return"strikethrough"===n["font-strikethrough"]}),i('button[data-event="superscript"]',function(){return"superscript"===n["font-superscript"]}),i('button[data-event="subscript"]',function(){return"subscript"===n["font-subscript"]}),i('button[data-event="justifyLeft"]',function(){return"left"===n["text-align"]||"start"===n["text-align"]}),i('button[data-event="justifyCenter"]',function(){return"center"===n["text-align"]}),i('button[data-event="justifyRight"]',function(){return"right"===n["text-align"]}),i('button[data-event="justifyFull"]',function(){return"justify"===n["text-align"]}),i('button[data-event="insertUnorderedList"]',function(){return"unordered"===n["list-style"]}),i('button[data-event="insertOrderedList"]',function(){return"ordered"===n["list-style"]})},this.updateRecentColor=function(t,n,o){var i=e(t).closest(".note-color"),r=i.find(".note-recent-color"),a=JSON.parse(r.attr("data-value"));a[n]=o,r.attr("data-value",JSON.stringify(a));var s="backColor"===n?"background-color":"color";r.find("i").css(s,o)}},S=function(){var e=new T;this.update=function(t,n){e.update(t,n)},this.updateRecentColor=function(t,n,o){e.updateRecentColor(t,n,o)},this.activate=function(e){e.find("button").not('button[data-event="codeview"]').removeClass("disabled")},this.deactivate=function(e){e.find("button").not('button[data-event="codeview"]').addClass("disabled")},this.updateFullscreen=function(e,t){var n=e.find('button[data-event="fullscreen"]');n.toggleClass("active",t)},this.updateCodeview=function(e,t){var n=e.find('button[data-event="codeview"]');n.toggleClass("active",t),t?this.deactivate(e):this.activate(e)},this.get=function(e,t){var n=h.makeLayoutInfo(e).toolbar();return n.find("[data-name="+t+"]")},this.setButtonState=function(e,t,n){n=n===!1?!1:!0;var o=this.get(e,t);o.toggleClass("active",n)}},P=24,L=function(){var t=e(document);this.attach=function(e,t){t.disableResizeEditor||e.statusbar().on("mousedown",n)};var n=function(e){e.preventDefault(),e.stopPropagation();var n=h.makeLayoutInfo(e.target).editable(),o=n.offset().top-t.scrollTop(),i=h.makeLayoutInfo(e.currentTarget||e.target),r=i.editor().data("options");t.on("mousemove",function(e){var t=e.clientY-(o+P);t=r.minHeight>0?Math.max(t,r.minHeight):t,t=r.maxHeight>0?Math.min(t,r.maxHeight):t,n.height(t)}).one("mouseup",function(){t.off("mousemove")})}},E=function(){var t=new T,n=function(t,n){var o=n&&n.isAirMode,i=n&&n.isLeftTop,r=e(t),a=o?r.offset():r.position(),s=i?0:r.outerHeight(!0);return{left:a.left,top:a.top+s}},o=function(e,t){e.css({display:"block",left:t.left,top:t.top})},i=20;this.update=function(r,a,s){t.update(r,a);var l=r.find(".note-link-popover");if(a.anchor){var u=l.find("a"),f=e(a.anchor).attr("href"),h=e(a.anchor).attr("target");u.attr("href",f).html(f),h?u.attr("target","_blank"):u.removeAttr("target"),o(l,n(a.anchor,{isAirMode:s}))}else l.hide();var p=r.find(".note-image-popover");a.image?o(p,n(a.image,{isAirMode:s,isLeftTop:!0})):p.hide();var v=r.find(".note-air-popover");if(s&&a.range&&!a.range.isCollapsed()){var g=c.last(a.range.getClientRects());if(g){var m=d.rect2bnd(g);o(v,{left:Math.max(m.left+m.width/2-i,0),top:m.top+m.height})}}else v.hide()},this.updateRecentColor=function(e,t,n){e.updateRecentColor(e,t,n)},this.hide=function(e){e.children().hide()}},F=function(t){var n=e(document),o=function(o){if(h.isControlSizing(o.target)){o.preventDefault(),o.stopPropagation();var i=h.makeLayoutInfo(o.target),r=i.handle(),a=i.popover(),s=i.editable(),l=i.editor(),d=r.find(".note-control-selection").data("target"),c=e(d),u=c.offset(),f=n.scrollTop(),p=l.data("options").airMode;n.on("mousemove",function(e){t.invoke("editor.resizeTo",{x:e.clientX-u.left,y:e.clientY-(u.top-f)},c,!e.shiftKey),t.invoke("handle.update",r,{image:d},p),t.invoke("popover.update",a,{image:d},p)}).one("mouseup",function(){n.off("mousemove"),t.invoke("editor.afterCommand",s)}),c.data("ratio")||c.data("ratio",c.height()/c.width())}};this.attach=function(e){e.handle().on("mousedown",o)},this.update=function(t,n,o){var i=t.find(".note-control-selection");if(n.image){var r=e(n.image),a=o?r.offset():r.position(),s={w:r.outerWidth(!0),h:r.outerHeight(!0)};i.css({display:"block",left:a.left,top:a.top,width:s.w,height:s.h}).data("target",n.image);var l=s.w+"x"+s.h;i.find(".note-control-selection-info").text(l)}else i.hide()},this.hide=function(e){e.children().hide()}},I=function(t){var n=e(window),o=e("html, body");this.toggle=function(e){var i=e.editor(),r=e.toolbar(),a=e.editable(),s=e.codable(),l=function(e){a.css("height",e.h),s.css("height",e.h),s.data("cmeditor")&&s.data("cmeditor").setsize(null,e.h)};i.toggleClass("fullscreen");var d=i.hasClass("fullscreen");d?(a.data("orgheight",a.css("height")),n.on("resize",function(){l({h:n.height()-r.outerHeight()})}).trigger("resize"),o.css("overflow","hidden")):(n.off("resize"),l({h:a.data("orgheight")}),o.css("overflow","visible")),t.invoke("toolbar.updateFullscreen",r,d)}};l.hasCodeMirror&&(l.isSupportAmd?require(["CodeMirror"],function(e){s=e}):s=window.CodeMirror);var M=function(e){this.sync=function(t){var n=e.invoke("codeview.isActivated",t);n&&l.hasCodeMirror&&t.codable().data("cmEditor").save()},this.isActivated=function(e){var t=e.editor();return t.hasClass("codeview")},this.toggle=function(e){this.isActivated(e)?this.deactivate(e):this.activate(e)},this.activate=function(t){var n=t.editor(),o=t.toolbar(),i=t.editable(),r=t.codable(),a=t.popover(),d=t.handle(),c=n.data("options");if(r.val(h.html(i,c.prettifyHtml)),r.height(i.height()),e.invoke("toolbar.updateCodeview",o,!0),e.invoke("popover.hide",a),e.invoke("handle.hide",d),n.addClass("codeview"),r.focus(),l.hasCodeMirror){var u=s.fromTextArea(r[0],c.codemirror);if(c.codemirror.tern){var f=new s.TernServer(c.codemirror.tern);u.ternServer=f,u.on("cursorActivity",function(e){f.updateArgHints(e)})}u.setSize(null,i.outerHeight()),r.data("cmEditor",u)}},this.deactivate=function(t){var n=t.holder(),o=t.editor(),i=t.toolbar(),r=t.editable(),a=t.codable(),s=o.data("options");if(l.hasCodeMirror){var d=a.data("cmEditor");a.val(d.getValue()),d.toTextArea()}var c=h.value(a,s.prettifyHtml)||h.emptyPara,u=r.html()!==c;r.html(c),r.height(s.height?a.height():"auto"),o.removeClass("codeview"),u&&e.bindCustomEvent(n,r.data("callbacks"),"change")(r.html(),r),r.focus(),e.invoke("toolbar.updateCodeview",i,!1)}},R=function(t){var n=e(document);this.attach=function(e,t){t.airMode||t.disableDragAndDrop?n.on("drop",function(e){e.preventDefault()}):this.attachDragAndDropEvent(e,t)},this.attachDragAndDropEvent=function(o,i){var r=e(),a=o.editor(),s=o.dropzone(),l=s.find(".note-dropzone-message");n.on("dragenter",function(e){var n=t.invoke("codeview.isActivated",o),d=a.width()>0&&a.height()>0;n||r.length||!d||(a.addClass("dragover"),s.width(a.width()),s.height(a.height()),l.text(i.langInfo.image.dragImageHere)),r=r.add(e.target)}).on("dragleave",function(e){r=r.not(e.target),r.length||a.removeClass("dragover")}).on("drop",function(){r=e(),a.removeClass("dragover")}),s.on("dragenter",function(){s.addClass("hover"),l.text(i.langInfo.image.dropImage)}).on("dragleave",function(){s.removeClass("hover"),l.text(i.langInfo.image.dragImageHere)}),s.on("drop",function(n){var o=n.originalEvent.dataTransfer,i=h.makeLayoutInfo(n.currentTarget||n.target);if(o&&o.files&&o.files.length)n.preventDefault(),i.editable().focus(),t.insertImages(i,o.files);else for(var r=function(){i.holder().summernote("insertNode",this)},a=0,s=o.types.length;s>a;a++){var l=o.types[a],d=o.getData(l);l.toLowerCase().indexOf("text")>-1?i.holder().summernote("pasteHTML",d):e(d).each(r)}}).on("dragover",!1)}},A=function(t){var n;this.attach=function(r){l.isMSIE&&l.browserVersion>10||l.isFF?(n=e("<div />").attr("contenteditable",!0).css({position:"absolute",left:-1e5,opacity:0}),r.editable().on("keydown",function(e){e.ctrlKey&&e.keyCode===m.code.V&&(t.invoke("saveRange",r.editable()),n.focus(),setTimeout(function(){o(r)},0))}),r.editable().before(n)):r.editable().on("paste",i)};var o=function(o){var i=o.editable(),r=n[0].firstChild;if(h.isImg(r)){for(var a=r.src,s=atob(a.split(",")[1]),l=new Uint8Array(s.length),d=0;d<s.length;d++)l[d]=s.charCodeAt(d);var c=new Blob([l],{type:"image/png"});c.name="clipboard.png",t.invoke("restoreRange",i),t.invoke("focus",i),t.insertImages(o,[c])}else{var u=e("<div />").html(n.html()).html();t.invoke("restoreRange",i),t.invoke("focus",i),u&&t.invoke("pasteHTML",i,u)}n.empty()},i=function(e){var n=e.originalEvent.clipboardData,o=h.makeLayoutInfo(e.currentTarget||e.target),i=o.editable();if(n&&n.items&&n.items.length){var r=c.head(n.items);"file"===r.kind&&-1!==r.type.indexOf("image/")&&t.insertImages(o,[r.getAsFile()]),t.invoke("editor.afterCommand",i)}}},D=function(t){var n=function(e,t){e.toggleClass("disabled",!t),e.attr("disabled",!t)},o=function(e,t){e.on("keypress",function(e){e.keyCode===m.code.ENTER&&t.trigger("click")})};this.showLinkDialog=function(t,i,r){return e.Deferred(function(e){var t=i.find(".note-link-dialog"),a=t.find(".note-link-text"),s=t.find(".note-link-url"),l=t.find(".note-link-btn"),d=t.find("input[type=checkbox]");t.one("shown.bs.modal",function(){a.val(r.text),a.on("input",function(){n(l,a.val()&&s.val()),r.text=a.val()}),r.url||(r.url=r.text||"http://",n(l,r.text)),s.on("input",function(){n(l,a.val()&&s.val()),r.text||a.val(s.val())}).val(r.url).trigger("focus").trigger("select"),o(s,l),o(a,l),d.prop("checked",r.isNewWindow),l.one("click",function(n){n.preventDefault(),e.resolve({range:r.range,url:s.val(),text:a.val(),isNewWindow:d.is(":checked")}),t.modal("hide")})}).one("hidden.bs.modal",function(){a.off("input keypress"),s.off("input keypress"),l.off("click"),"pending"===e.state()&&e.reject()}).modal("show")}).promise()},this.show=function(e){var n=e.editor(),o=e.dialog(),i=e.editable(),r=e.popover(),a=t.invoke("editor.getLinkInfo",i),s=n.data("options");t.invoke("editor.saveRange",i),this.showLinkDialog(i,o,a).then(function(e){t.invoke("editor.restoreRange",i),t.invoke("editor.createLink",i,e,s),t.invoke("popover.hide",r)}).fail(function(){t.invoke("editor.restoreRange",i)})}},H=function(t){var n=function(e,t){e.toggleClass("disabled",!t),e.attr("disabled",!t)},o=function(e,t){e.on("keypress",function(e){e.keyCode===m.code.ENTER&&t.trigger("click")})};this.show=function(e){var n=e.dialog(),o=e.editable();t.invoke("editor.saveRange",o),this.showImageDialog(o,n).then(function(n){t.invoke("editor.restoreRange",o),"string"==typeof n?t.invoke("editor.insertImage",o,n):t.insertImages(e,n)}).fail(function(){t.invoke("editor.restoreRange",o)})},this.showImageDialog=function(t,i){return e.Deferred(function(e){var t=i.find(".note-image-dialog"),r=i.find(".note-image-input"),a=i.find(".note-image-url"),s=i.find(".note-image-btn");t.one("shown.bs.modal",function(){r.replaceWith(r.clone().on("change",function(){e.resolve(this.files||this.value),t.modal("hide")}).val("")),s.click(function(n){n.preventDefault(),e.resolve(a.val()),t.modal("hide")}),a.on("keyup paste",function(e){var t;t="paste"===e.type?e.originalEvent.clipboardData.getData("text"):a.val(),n(s,t)}).val("").trigger("focus"),o(a,s)}).one("hidden.bs.modal",function(){r.off("change"),a.off("keyup paste keypress"),s.off("click"),"pending"===e.state()&&e.reject()}).modal("show")})}},B=function(t){this.showHelpDialog=function(t,n){return e.Deferred(function(e){var t=n.find(".note-help-dialog");t.one("hidden.bs.modal",function(){e.resolve()}).modal("show")}).promise()},this.show=function(e){var n=e.dialog(),o=e.editable();t.invoke("editor.saveRange",o,!0),this.showHelpDialog(o,n).then(function(){t.invoke("editor.restoreRange",o)})}},z=function(){var t=this,n=this.modules={editor:new N(this),toolbar:new S(this),statusbar:new L(this),popover:new E(this),handle:new F(this),fullscreen:new I(this),codeview:new M(this),dragAndDrop:new R(this),clipboard:new A(this),linkDialog:new D(this),imageDialog:new H(this),helpDialog:new B(this)};this.invoke=function(){var e=c.head(c.from(arguments)),t=c.tail(c.from(arguments)),n=e.split("."),o=n.length>1,i=o&&c.head(n),r=o?c.last(n):c.head(n),a=this.getModule(i),s=a[r];return s&&s.apply(a,t)},this.getModule=function(e){return this.modules[e]||this.modules.editor};var o=this.bindCustomEvent=function(e,t,n){return function(){var o=t[d.namespaceToCamel(n,"on")];return o&&o.apply(e[0],arguments),e.trigger("summernote."+n,arguments)}};this.insertImages=function(t,i){var r=t.editor(),a=t.editable(),s=t.holder(),l=a.data("callbacks"),d=r.data("options");l.onImageUpload?o(s,l,"image.upload")(i):e.each(i,function(e,t){var i=t.name;d.maximumImageFileSize&&d.maximumImageFileSize<t.size?o(s,l,"image.upload.error")(d.langInfo.image.maximumFileSizeError):g.readFileAsDataURL(t).then(function(e){n.editor.insertImage(a,e,i)}).fail(function(){o(s,l,"image.upload.error")(d.langInfo.image.maximumFileSizeError)})})};var i={showLinkDialog:function(e){n.linkDialog.show(e)},showImageDialog:function(e){n.imageDialog.show(e)},showHelpDialog:function(e){n.helpDialog.show(e)},fullscreen:function(e){n.fullscreen.toggle(e)},codeview:function(e){n.codeview.toggle(e)}},r=function(e){h.isImg(e.target)&&e.preventDefault()},a=function(e){var t=h.makeLayoutInfo(e.currentTarget||e.target);n.editor.removeBogus(t.editable()),s(e)};this.updateStyleInfo=function(e,t){if(e){var o=t.editor().data("options").airMode;o||n.toolbar.update(t.toolbar(),e),n.popover.update(t.popover(),e,o),n.handle.update(t.handle(),e,o)}};var s=function(e){var o=e.target;setTimeout(function(){var e=h.makeLayoutInfo(o),i=n.editor.currentStyle(o);t.updateStyleInfo(i,e)},0)},u=function(e){var t=h.makeLayoutInfo(e.currentTarget||e.target);n.popover.hide(t.popover()),n.handle.hide(t.handle())},f=function(t){var n=e(t.target).closest("[data-event]");n.length&&t.preventDefault()},p=function(t){var o=e(t.target).closest("[data-event]");if(o.length){var r,a=o.attr("data-event"),l=o.attr("data-value"),d=o.attr("data-hide"),u=h.makeLayoutInfo(t.target);if(-1!==e.inArray(a,["resize","floatMe","removeMedia","imageShape"])){var f=u.handle().find(".note-control-selection");r=e(f.data("target"))}if(d&&o.parents(".popover").hide(),e.isFunction(e.summernote.pluginEvents[a]))e.summernote.pluginEvents[a](t,n.editor,u,l);else if(n.editor[a]){var p=u.editable();p.focus(),n.editor[a](p,l,r),t.preventDefault()}else i[a]&&(i[a].call(this,u),t.preventDefault());if(-1!==e.inArray(a,["backColor","foreColor"])){var v=u.editor().data("options",v),g=v.airMode?n.popover:n.toolbar;g.updateRecentColor(c.head(o),a,l)}s(t)}},v=18,y=function(t,n){var o,i=e(t.target.parentNode),r=i.next(),a=i.find(".note-dimension-picker-mousecatcher"),s=i.find(".note-dimension-picker-highlighted"),l=i.find(".note-dimension-picker-unhighlighted");if(void 0===t.offsetX){var d=e(t.target).offset();o={x:t.pageX-d.left,y:t.pageY-d.top}}else o={x:t.offsetX,y:t.offsetY};var c={c:Math.ceil(o.x/v)||1,r:Math.ceil(o.y/v)||1};s.css({width:c.c+"em",height:c.r+"em"}),a.attr("data-value",c.c+"x"+c.r),3<c.c&&c.c<n.insertTableMaxSize.col&&l.css({width:c.c+1+"em"}),3<c.r&&c.r<n.insertTableMaxSize.row&&l.css({height:c.r+1+"em"}),r.html(c.c+" x "+c.r)};this.bindKeyMap=function(t,o){var r=t.editor(),a=t.editable();a.on("keydown",function(s){var l=[];s.metaKey&&l.push("CMD"),s.ctrlKey&&!s.altKey&&l.push("CTRL"),s.shiftKey&&l.push("SHIFT");var d=m.nameFromCode[s.keyCode];d&&l.push(d);var c,u=l.join("+"),f=o[u];if(f){if(c=e.summernote.pluginEvents[u],e.isFunction(c)&&c(s,n.editor,t))return!1;c=e.summernote.pluginEvents[f],e.isFunction(c)?c(s,n.editor,t):n.editor[f]?(n.editor[f](a,r.data("options")),s.preventDefault()):i[f]&&(i[f].call(this,t),s.preventDefault())}else m.isEdit(s.keyCode)&&n.editor.afterCommand(a)})},this.attach=function(e,t){t.shortcuts&&this.bindKeyMap(e,t.keyMap[l.isMac?"mac":"pc"]),e.editable().on("mousedown",r),e.editable().on("keyup mouseup",a),e.editable().on("scroll",u),n.clipboard.attach(e,t),n.handle.attach(e,t),e.popover().on("click",p),e.popover().on("mousedown",f),n.dragAndDrop.attach(e,t),t.airMode||(e.toolbar().on("click",p),e.toolbar().on("mousedown",f),n.statusbar.attach(e,t));var o=t.airMode?e.popover():e.toolbar(),i=o.find(".note-dimension-picker-mousecatcher");i.css({width:t.insertTableMaxSize.col+"em",height:t.insertTableMaxSize.row+"em"}).on("mousemove",function(e){y(e,t)}),e.editor().data("options",t),l.isMSIE||setTimeout(function(){document.execCommand("styleWithCSS",0,t.styleWithSpan)},0);var s=new b(e.editable());e.editable().data("NoteHistory",s),e.editable().data("callbacks",{onInit:t.onInit,onFocus:t.onFocus,onBlur:t.onBlur,onKeydown:t.onKeydown,onKeyup:t.onKeyup,onMousedown:t.onMousedown,onEnter:t.onEnter,onPaste:t.onPaste,onBeforeCommand:t.onBeforeCommand,onChange:t.onChange,onImageUpload:t.onImageUpload,onImageUploadError:t.onImageUploadError,onMediaDelete:t.onMediaDelete,onToolbarClick:t.onToolbarClick});var d=n.editor.styleFromNode(e.editable());this.updateStyleInfo(d,e)},this.attachCustomEvent=function(t,n){var i=t.holder(),r=t.editable(),a=r.data("callbacks");r.focus(o(i,a,"focus")),r.blur(o(i,a,"blur")),r.keydown(function(e){e.keyCode===m.code.ENTER&&o(i,a,"enter").call(this,e),o(i,a,"keydown").call(this,e)}),r.keyup(o(i,a,"keyup")),r.on("mousedown",o(i,a,"mousedown")),r.on("mouseup",o(i,a,"mouseup")),r.on("scroll",o(i,a,"scroll")),r.on("paste",o(i,a,"paste"));var s=l.isMSIE?"DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted":"input";r.on(s,function(){o(i,a,"change")(r.html(),r)}),n.airMode||(t.toolbar().click(o(i,a,"toolbar.click")),t.popover().click(o(i,a,"popover.click"))),h.isTextarea(c.head(i))&&i.closest("form").submit(function(e){t.holder().val(t.holder().code()),o(i,a,"submit").call(this,e,i.code())}),h.isTextarea(c.head(i))&&n.textareaAutoSync&&i.on("summernote.change",function(){t.holder().val(t.holder().code())}),o(i,a,"init")(t);for(var d=0,u=e.summernote.plugins.length;u>d;d++)e.isFunction(e.summernote.plugins[d].init)&&e.summernote.plugins[d].init(t)},this.detach=function(e,t){e.holder().off(),e.editable().off(),e.popover().off(),e.handle().off(),e.dialog().off(),t.airMode||(e.dropzone().off(),e.toolbar().off(),e.statusbar().off())}},U=function(){var t=function(e,t){var n=t.event,o=t.value,i=t.title,r=t.className,a=t.dropdown,s=t.hide;return(a?'<div class="btn-group'+(r?" "+r:"")+'">':"")+'<button type="button" class="btn btn-default btn-sm'+(!a&&r?" "+r:"")+(a?" dropdown-toggle":"")+'"'+(a?' data-toggle="dropdown"':"")+(i?' title="'+i+'"':"")+(n?' data-event="'+n+'"':"")+(o?" data-value='"+o+"'":"")+(s?" data-hide='"+s+"'":"")+' tabindex="-1">'+e+(a?' <span class="caret"></span>':"")+"</button>"+(a||"")+(a?"</div>":"")},n=function(e,n){var o='<i class="'+e+'"></i>';return t(o,n)},o=function(t,n){var o=e('<div class="'+t+' popover bottom in" style="display: none;"><div class="arrow"></div><div class="popover-content"></div></div>');return o.find(".popover-content").append(n),o},i=function(e,t,n,o){return'<div class="'+e+' modal" aria-hidden="false"><div class="modal-dialog"><div class="modal-content">'+(t?'<div class="modal-header"><button type="button" class="close" aria-hidden="true" tabindex="-1">&times;</button><h4 class="modal-title">'+t+"</h4></div>":"")+'<div class="modal-body">'+n+"</div>"+(o?'<div class="modal-footer">'+o+"</div>":"")+"</div></div></div>"},r=function(e,t,n){var o="dropdown-menu"+(t?" "+t:"");return n=n||"ul",e instanceof Array&&(e=e.join("")),"<"+n+' class="'+o+'">'+e+"</"+n+">"},a={picture:function(e,t){return n(t.iconPrefix+t.icons.image.image,{event:"showImageDialog",title:e.image.image,hide:!0})},link:function(e,t){return n(t.iconPrefix+t.icons.link.link,{event:"showLinkDialog",title:e.link.link,hide:!0})},table:function(e,t){var o=['<div class="note-dimension-picker">','<div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"></div>','<div class="note-dimension-picker-highlighted"></div>','<div class="note-dimension-picker-unhighlighted"></div>',"</div>",'<div class="note-dimension-display"> 1 x 1 </div>'];return n(t.iconPrefix+t.icons.table.table,{title:e.table.table,dropdown:r(o,"note-table")})},style:function(e,t){var o=t.styleTags.reduce(function(t,n){var o=e.style["p"===n?"normal":n];return t+'<li><a data-event="formatBlock" href="#" data-value="'+n+'">'+("p"===n||"pre"===n?o:"<"+n+">"+o+"</"+n+">")+"</a></li>"},"");return n(t.iconPrefix+t.icons.style.style,{title:e.style.style,dropdown:r(o)})},fontname:function(e,n){var o=[],i=n.fontNames.reduce(function(e,t){return l.isFontInstalled(t)||c.contains(n.fontNamesIgnoreCheck,t)?(o.push(t),e+'<li><a data-event="fontName" href="#" data-value="'+t+'" style="font-family:\''+t+'\'"><i class="'+n.iconPrefix+n.icons.misc.check+'"></i> '+t+"</a></li>"):e},""),a=l.isFontInstalled(n.defaultFontName),s=a?n.defaultFontName:o[0],d='<span class="note-current-fontname">'+s+"</span>";return t(d,{title:e.font.name,className:"note-fontname",dropdown:r(i,"note-check")})},fontsize:function(e,n){var o=n.fontSizes.reduce(function(e,t){return e+'<li><a data-event="fontSize" href="#" data-value="'+t+'"><i class="'+n.iconPrefix+n.icons.misc.check+'"></i> '+t+"</a></li>"},""),i='<span class="note-current-fontsize">11</span>';return t(i,{title:e.font.size,className:"note-fontsize",dropdown:r(o,"note-check")})},color:function(e,n){var o='<i class="'+n.iconPrefix+n.icons.color.recent+'" style="color:black;background-color:yellow;"></i>',i=t(o,{className:"note-recent-color",title:e.color.recent,event:"color",value:'{"backColor":"yellow"}'}),a=['<li><div class="btn-group">','<div class="note-palette-title">'+e.color.background+"</div>",'<div class="note-color-reset" data-event="backColor"',' data-value="inherit" title="'+e.color.transparent+'">'+e.color.setTransparent+"</div>",'<div class="note-color-palette" data-target-event="backColor"></div>','</div><div class="btn-group">','<div class="note-palette-title">'+e.color.foreground+"</div>",'<div class="note-color-reset" data-event="foreColor" data-value="inherit" title="'+e.color.reset+'">',e.color.resetToDefault,"</div>",'<div class="note-color-palette" data-target-event="foreColor"></div>',"</div></li>"],s=t("",{title:e.color.more,dropdown:r(a)});return i+s},bold:function(e,t){return n(t.iconPrefix+t.icons.font.bold,{event:"bold",title:e.font.bold})},italic:function(e,t){return n(t.iconPrefix+t.icons.font.italic,{event:"italic",title:e.font.italic})},underline:function(e,t){return n(t.iconPrefix+t.icons.font.underline,{event:"underline",title:e.font.underline})},strikethrough:function(e,t){return n(t.iconPrefix+t.icons.font.strikethrough,{event:"strikethrough",title:e.font.strikethrough})},superscript:function(e,t){return n(t.iconPrefix+t.icons.font.superscript,{event:"superscript",title:e.font.superscript})},subscript:function(e,t){return n(t.iconPrefix+t.icons.font.subscript,{event:"subscript",title:e.font.subscript})},clear:function(e,t){return n(t.iconPrefix+t.icons.font.clear,{event:"removeFormat",title:e.font.clear})},ul:function(e,t){return n(t.iconPrefix+t.icons.lists.unordered,{event:"insertUnorderedList",title:e.lists.unordered})},ol:function(e,t){return n(t.iconPrefix+t.icons.lists.ordered,{event:"insertOrderedList",title:e.lists.ordered})},paragraph:function(e,t){var o=n(t.iconPrefix+t.icons.paragraph.left,{title:e.paragraph.left,event:"justifyLeft"}),i=n(t.iconPrefix+t.icons.paragraph.center,{title:e.paragraph.center,event:"justifyCenter"}),a=n(t.iconPrefix+t.icons.paragraph.right,{title:e.paragraph.right,event:"justifyRight"}),s=n(t.iconPrefix+t.icons.paragraph.justify,{title:e.paragraph.justify,event:"justifyFull"}),l=n(t.iconPrefix+t.icons.paragraph.outdent,{title:e.paragraph.outdent,event:"outdent"}),d=n(t.iconPrefix+t.icons.paragraph.indent,{title:e.paragraph.indent,event:"indent"}),c=['<div class="note-align btn-group">',o+i+a+s,'</div><div class="note-list btn-group">',d+l,"</div>"];
    return n(t.iconPrefix+t.icons.paragraph.paragraph,{title:e.paragraph.paragraph,dropdown:r(c,"","div")})},height:function(e,t){var o=t.lineHeights.reduce(function(e,n){return e+'<li><a data-event="lineHeight" href="#" data-value="'+parseFloat(n)+'"><i class="'+t.iconPrefix+t.icons.misc.check+'"></i> '+n+"</a></li>"},"");return n(t.iconPrefix+t.icons.font.height,{title:e.font.height,dropdown:r(o,"note-check")})},help:function(e,t){return n(t.iconPrefix+t.icons.options.help,{event:"showHelpDialog",title:e.options.help,hide:!0})},fullscreen:function(e,t){return n(t.iconPrefix+t.icons.options.fullscreen,{event:"fullscreen",title:e.options.fullscreen})},codeview:function(e,t){return n(t.iconPrefix+t.icons.options.codeview,{event:"codeview",title:e.options.codeview})},undo:function(e,t){return n(t.iconPrefix+t.icons.history.undo,{event:"undo",title:e.history.undo})},redo:function(e,t){return n(t.iconPrefix+t.icons.history.redo,{event:"redo",title:e.history.redo})},hr:function(e,t){return n(t.iconPrefix+t.icons.hr.insert,{event:"insertHorizontalRule",title:e.hr.insert})}},s=function(i,r){var s=function(){var e=n(r.iconPrefix+r.icons.link.edit,{title:i.link.edit,event:"showLinkDialog",hide:!0}),t=n(r.iconPrefix+r.icons.link.unlink,{title:i.link.unlink,event:"unlink"}),a='<a href="http://www.google.com" target="_blank">www.google.com</a>&nbsp;&nbsp;<div class="note-insert btn-group">'+e+t+"</div>";return o("note-link-popover",a)},l=function(){var e=t('<span class="note-fontsize-10">100%</span>',{title:i.image.resizeFull,event:"resize",value:"1"}),a=t('<span class="note-fontsize-10">50%</span>',{title:i.image.resizeHalf,event:"resize",value:"0.5"}),s=t('<span class="note-fontsize-10">25%</span>',{title:i.image.resizeQuarter,event:"resize",value:"0.25"}),l=n(r.iconPrefix+r.icons.image.floatLeft,{title:i.image.floatLeft,event:"floatMe",value:"left"}),d=n(r.iconPrefix+r.icons.image.floatRight,{title:i.image.floatRight,event:"floatMe",value:"right"}),c=n(r.iconPrefix+r.icons.image.floatNone,{title:i.image.floatNone,event:"floatMe",value:"none"}),u=n(r.iconPrefix+r.icons.image.shapeRounded,{title:i.image.shapeRounded,event:"imageShape",value:"img-rounded"}),f=n(r.iconPrefix+r.icons.image.shapeCircle,{title:i.image.shapeCircle,event:"imageShape",value:"img-circle"}),h=n(r.iconPrefix+r.icons.image.shapeThumbnail,{title:i.image.shapeThumbnail,event:"imageShape",value:"img-thumbnail"}),p=n(r.iconPrefix+r.icons.image.shapeNone,{title:i.image.shapeNone,event:"imageShape",value:""}),v=n(r.iconPrefix+r.icons.image.remove,{title:i.image.remove,event:"removeMedia",value:"none"}),g=(r.disableResizeImage?"":'<div class="btn-group">'+e+a+s+"</div>")+'<div class="btn-group">'+l+d+c+'</div><br><div class="btn-group">'+u+f+h+p+'</div><div class="btn-group">'+v+"</div>";return o("note-image-popover",g)},d=function(){for(var t=e("<div />"),n=0,s=r.airPopover.length;s>n;n++){for(var l=r.airPopover[n],d=e('<div class="note-'+l[0]+' btn-group">'),c=0,u=l[1].length;u>c;c++){var f=e(a[l[1][c]](i,r));f.attr("data-name",l[1][c]),d.append(f)}t.append(d)}return o("note-air-popover",t.children())},c=e('<div class="note-popover" />');return c.append(s()),c.append(l()),r.airMode&&c.append(d()),c},u=function(e){return'<div class="note-handle"><div class="note-control-selection"><div class="note-control-selection-bg"></div><div class="note-control-holder note-control-nw"></div><div class="note-control-holder note-control-ne"></div><div class="note-control-holder note-control-sw"></div><div class="'+(e.disableResizeImage?"note-control-holder":"note-control-sizing")+' note-control-se"></div>'+(e.disableResizeImage?"":'<div class="note-control-selection-info"></div>')+"</div></div>"},f=function(e,t){var n="note-shortcut-col col-xs-6 note-shortcut-",o=[];for(var i in t)t.hasOwnProperty(i)&&o.push('<div class="'+n+'key">'+t[i].kbd+'</div><div class="'+n+'name">'+t[i].text+"</div>");return'<div class="note-shortcut-row row"><div class="'+n+'title col-xs-offset-6">'+e+'</div></div><div class="note-shortcut-row row">'+o.join('</div><div class="note-shortcut-row row">')+"</div>"},p=function(e){var t=[{kbd:"⌘ + B",text:e.font.bold},{kbd:"⌘ + I",text:e.font.italic},{kbd:"⌘ + U",text:e.font.underline},{kbd:"⌘ + \\",text:e.font.clear}];return f(e.shortcut.textFormatting,t)},v=function(e){var t=[{kbd:"⌘ + Z",text:e.history.undo},{kbd:"⌘ + ⇧ + Z",text:e.history.redo},{kbd:"⌘ + ]",text:e.paragraph.indent},{kbd:"⌘ + [",text:e.paragraph.outdent},{kbd:"⌘ + ENTER",text:e.hr.insert}];return f(e.shortcut.action,t)},g=function(e){var t=[{kbd:"⌘ + ⇧ + L",text:e.paragraph.left},{kbd:"⌘ + ⇧ + E",text:e.paragraph.center},{kbd:"⌘ + ⇧ + R",text:e.paragraph.right},{kbd:"⌘ + ⇧ + J",text:e.paragraph.justify},{kbd:"⌘ + ⇧ + NUM7",text:e.lists.ordered},{kbd:"⌘ + ⇧ + NUM8",text:e.lists.unordered}];return f(e.shortcut.paragraphFormatting,t)},m=function(e){var t=[{kbd:"⌘ + NUM0",text:e.style.normal},{kbd:"⌘ + NUM1",text:e.style.h1},{kbd:"⌘ + NUM2",text:e.style.h2},{kbd:"⌘ + NUM3",text:e.style.h3},{kbd:"⌘ + NUM4",text:e.style.h4},{kbd:"⌘ + NUM5",text:e.style.h5},{kbd:"⌘ + NUM6",text:e.style.h6}];return f(e.shortcut.documentStyle,t)},b=function(e,t){var n=t.extraKeys,o=[];for(var i in n)n.hasOwnProperty(i)&&o.push({kbd:i,text:n[i]});return f(e.shortcut.extraKeys,o)},y=function(e,t){var n='class="note-shortcut note-shortcut-col col-sm-6 col-xs-12"',o=["<div "+n+">"+v(e,t)+"</div><div "+n+">"+p(e,t)+"</div>","<div "+n+">"+m(e,t)+"</div><div "+n+">"+g(e,t)+"</div>"];return t.extraKeys&&o.push("<div "+n+">"+b(e,t)+"</div>"),'<div class="note-shortcut-row row">'+o.join('</div><div class="note-shortcut-row row">')+"</div>"},C=function(e){return e.replace(/⌘/g,"Ctrl").replace(/⇧/g,"Shift")},k={image:function(e,t){var n="";if(t.maximumImageFileSize){var o=Math.floor(Math.log(t.maximumImageFileSize)/Math.log(1024)),r=1*(t.maximumImageFileSize/Math.pow(1024,o)).toFixed(2)+" "+" KMGTP"[o]+"B";n="<small>"+e.image.maximumFileSize+" : "+r+"</small>"}var a='<div class="form-group row note-group-select-from-files"><label>'+e.image.selectFromFiles+'</label><input class="note-image-input form-control" type="file" name="files" accept="image/*" multiple="multiple" />'+n+'</div><div class="form-group row"><label>'+e.image.url+'</label><input class="note-image-url form-control col-md-12" type="text" /></div>',s='<button href="#" class="btn btn-primary note-image-btn disabled" disabled>'+e.image.insert+"</button>";return i("note-image-dialog",e.image.insert,a,s)},link:function(e,t){var n='<div class="form-group row"><label>'+e.link.textToDisplay+'</label><div class="nk-int-st"><input class="note-link-text form-control" type="text" /></div></div><div class="form-group row"><label>'+e.link.url+'</label><div class="nk-int-st"><input class="note-link-url form-control col-md-12" type="text" value="http://" /></div></div>'+(t.disableLinkTarget?"":''),o='<button href="#" class="btn btn-primary note-link-btn disabled" disabled>'+e.link.insert+"</button>";return i("note-link-dialog",e.link.insert,n,o)},help:function(e,t){var n='<a class="modal-close pull-right" aria-hidden="true" tabindex="-1">'+e.shortcut.close+'</a><div class="title">'+e.shortcut.shortcuts+"</div>"+(l.isMac?y(e,t):C(y(e,t)))+'<p class="text-center"><a href="//summernote.org/" target="_blank">Summernote 0.6.16</a> · <a href="//github.com/summernote/summernote" target="_blank">Project</a> · <a href="//github.com/summernote/summernote/issues" target="_blank">Issues</a></p>';return i("note-help-dialog","",n,"")}},x=function(t,n){var o="";return e.each(k,function(e,i){o+=i(t,n)}),'<div class="note-dialog">'+o+"</div>"},w=function(){return'<div class="note-resizebar"><div class="note-icon-bar"></div><div class="note-icon-bar"></div><div class="note-icon-bar"></div></div>'},N=function(e){return l.isMac&&(e=e.replace("CMD","⌘").replace("SHIFT","⇧")),e.replace("BACKSLASH","\\").replace("SLASH","/").replace("LEFTBRACKET","[").replace("RIGHTBRACKET","]")},T=function(t,n,o){var i=d.invertObject(n),r=t.find("button");r.each(function(t,n){var o=e(n),r=i[o.data("event")];r&&o.attr("title",function(e,t){return t+" ("+N(r)+")"})}).tooltip({container:"body",trigger:"hover",placement:o||"top"}).on("click",function(){e(this).tooltip("hide")})},S=function(t,n){var o=n.colors;t.find(".note-color-palette").each(function(){for(var t=e(this),n=t.attr("data-target-event"),i=[],r=0,a=o.length;a>r;r++){for(var s=o[r],l=[],d=0,c=s.length;c>d;d++){var u=s[d];l.push(['<button type="button" class="note-color-btn" style="background-color:',u,';" data-event="',n,'" data-value="',u,'" title="',u,'" data-toggle="button" tabindex="-1"></button>'].join(""))}i.push('<div class="note-color-row">'+l.join("")+"</div>")}t.html(i.join(""))})};this.createLayoutByAirMode=function(t,n){var o=n.langInfo,i=n.keyMap[l.isMac?"mac":"pc"],r=d.uniqueId();t.addClass("note-air-editor note-editable panel-body"),t.attr({id:"note-editor-"+r,contentEditable:!0});var a=document.body,c=e(s(o,n));c.addClass("note-air-layout"),c.attr("id","note-popover-"+r),c.appendTo(a),T(c,i),S(c,n);var f=e(u(n));f.addClass("note-air-layout"),f.attr("id","note-handle-"+r),f.appendTo(a);var h=e(x(o,n));h.addClass("note-air-layout"),h.attr("id","note-dialog-"+r),h.find("button.close, a.modal-close").click(function(){e(this).closest(".modal").modal("hide")}),h.appendTo(a)},this.createLayoutByFrame=function(t,n){var o=n.langInfo,i=e('<div class="note-editor panel panel-default" />');n.width&&i.width(n.width),n.height>0&&e('<div class="note-statusbar">'+(n.disableResizeEditor?"":w())+"</div>").prependTo(i);var r=e('<div class="note-editing-area" />'),d=!t.is(":disabled"),c=e('<div class="note-editable panel-body" contentEditable="'+d+'"></div>').prependTo(r);n.height&&c.height(n.height),n.direction&&c.attr("dir",n.direction);var f=t.attr("placeholder")||n.placeholder;f&&c.attr("data-placeholder",f),c.html(h.html(t)||h.emptyPara),e('<textarea class="note-codable"></textarea>').prependTo(r);var p=e(s(o,n)).prependTo(r);S(p,n),T(p,E),e(u(n)).prependTo(r),r.prependTo(i);for(var v=e('<div class="note-toolbar panel-heading" />'),g=0,m=n.toolbar.length;m>g;g++){for(var b=n.toolbar[g][0],y=n.toolbar[g][1],C=e('<div class="note-'+b+' btn-group" />'),k=0,N=y.length;N>k;k++){var P=a[y[k]];if(e.isFunction(P)){var L=e(P(o,n));L.attr("data-name",y[k]),C.append(L)}}v.append(C)}var E=n.keyMap[l.isMac?"mac":"pc"];S(v,n),T(v,E,"bottom"),v.prependTo(i),e('<div class="note-dropzone"><div class="note-dropzone-message"></div></div>').prependTo(i);var F=n.dialogsInBody?e(document.body):i,I=e(x(o,n)).prependTo(F);I.find("button.close, a.modal-close").click(function(){e(this).closest(".modal").modal("hide")}),i.insertAfter(t),t.hide()},this.hasNoteEditor=function(e){return this.noteEditorFromHolder(e).length>0},this.noteEditorFromHolder=function(t){return t.hasClass("note-air-editor")?t:t.next().hasClass("note-editor")?t.next():e()},this.createLayout=function(e,t){t.airMode?this.createLayoutByAirMode(e,t):this.createLayoutByFrame(e,t)},this.layoutInfoFromHolder=function(e){var t=this.noteEditorFromHolder(e);if(t.length)return t.data("holder",e),h.buildLayoutInfo(t)},this.removeLayout=function(e,t,n){n.airMode?(e.removeClass("note-air-editor note-editable").removeAttr("id contentEditable"),t.popover().remove(),t.handle().remove(),t.dialog().remove()):(e.html(t.editable().html()),n.dialogsInBody&&t.dialog().remove(),t.editor().remove(),e.show())},this.getTemplate=function(){return{button:t,iconButton:n,dialog:i}},this.addButtonInfo=function(e,t){a[e]=t},this.addDialogInfo=function(e,t){k[e]=t}};e.summernote=e.summernote||{},e.extend(e.summernote,v);var O=new U,j=new z;e.extend(e.summernote,{renderer:O,eventHandler:j,core:{agent:l,list:c,dom:h,range:p},pluginEvents:{},plugins:[]}),e.summernote.addPlugin=function(t){e.summernote.plugins.push(t),t.buttons&&e.each(t.buttons,function(e,t){O.addButtonInfo(e,t)}),t.dialogs&&e.each(t.dialogs,function(e,t){O.addDialogInfo(e,t)}),t.events&&e.each(t.events,function(t,n){e.summernote.pluginEvents[t]=n}),t.langs&&e.each(t.langs,function(t,n){e.summernote.lang[t]&&e.extend(e.summernote.lang[t],n)}),t.options&&e.extend(e.summernote.options,t.options)},e.fn.extend({summernote:function(){var t=e.type(c.head(arguments)),n="string"===t,o="object"===t,i=o?c.head(arguments):{};if(i=e.extend({},e.summernote.options,i),i.icons=e.extend({},e.summernote.options.icons,i.icons),i.langInfo=e.extend(!0,{},e.summernote.lang["en-US"],e.summernote.lang[i.lang]),!n&&o)for(var r=0,a=e.summernote.plugins.length;a>r;r++){var s=e.summernote.plugins[r];i.plugin[s.name]&&(e.summernote.plugins[r]=e.extend(!0,s,i.plugin[s.name]))}this.each(function(t,n){var o=e(n);if(!O.hasNoteEditor(o)){O.createLayout(o,i);var r=O.layoutInfoFromHolder(o);o.data("layoutInfo",r),j.attach(r,i),j.attachCustomEvent(r,i)}});var l=this.first();if(l.length){var d=O.layoutInfoFromHolder(l);if(n){var u=c.head(c.from(arguments)),f=c.tail(c.from(arguments)),h=[u,d.editable()].concat(f);return j.invoke.apply(j,h)}i.focus&&d.editable().focus()}return this},code:function(t){if(void 0===t){var n=this.first();if(!n.length)return;var o=O.layoutInfoFromHolder(n),i=o&&o.editable();if(i&&i.length){var r=j.invoke("codeview.isActivated",o);return j.invoke("codeview.sync",o),r?o.codable().val():o.editable().html()}return h.value(n)}return this.each(function(n,o){var i=O.layoutInfoFromHolder(e(o)),r=i&&i.editable();r&&r.html(t)}),this},destroy:function(){return this.each(function(t,n){var o=e(n);if(O.hasNoteEditor(o)){var i=O.layoutInfoFromHolder(o),r=i.editor().data("options");j.detach(i,r),O.removeLayout(o,i,r)}}),this}})});