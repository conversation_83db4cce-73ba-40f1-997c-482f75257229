﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IMentore
    {
        void AfficherDetails(int mentoreId, Mentore_Class mentore);
        int Ajouter(Mentore_Class mentore);
        int Modifier(Mentore_Class mentore);
        int Supprimer(int mentoreId);
        void ChargerMentores(GridView gdv, string status = "");
        void ChargerMentoresParProgramme(GridView gdv, int programmeMentoratId);
        void chargerMentore(DropDownList lst);
        void ChargerMentoresParMembre(GridView gdv, long membreId);
        int count(string status = "");
    }
}
