﻿using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listcommune : System.Web.UI.Page
    {
        Province_Class cat = new Province_Class();
        CommuneClass pc = new CommuneClass();
        ICommune obj = new CommuneImp();
        int info;
        static string typenm; static int id, idpers, rol;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
               
                getDataGDV();

            }
        }
        public void getDataGDV()
        {
            obj.Chargement_GDV(gdv);
           // nbr.Text = obj.count().ToString();

        }
        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view")
            {
                Response.Redirect("~/file/commune.aspx?name=" + index);

            }
            if (e.CommandName == "delete")
            {
                try
                {
                    obj.afficherDetails(index,pc);
                    info = obj.supprimer(Convert.ToInt32(pc.CommuneId));
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listcommune.aspx");
                    }
                    else
                    {
                        //  msg.Text = "Modification echoue";
                        //msg.Text = id.ToString();
                    }


                }
                catch (SqlException ex)
                {
                    // msg.Text = "Cette Province existe deja";
                }
            }
        }

        protected void btn_srch_Click(object sender, EventArgs e)
        {
            //if (txt_srch.Value == "")
            //    getDataGDV();
            //else obj.search(gdv, txt_srch.Value);
        }
    }
}