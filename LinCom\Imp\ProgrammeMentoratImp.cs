﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class ProgrammeMentoratImp : IProgrammeMentorat
    {

        int msg;
        private Programmementorat programme = new Programmementorat();

        public void AfficherDetails(int programmeMentoratId, ProgrammeMentorat_Class programmeClass)
        {
            using (Connection con = new Connection())
            {
                var p = con.Programmementorats.FirstOrDefault(x => x.ProgrammeMentoratId == programmeMentoratId);
                if (p != null)
                {
                    programmeClass.ProgrammeMentoratId = p.ProgrammeMentoratId;
                    programmeClass.Titre = p.Titre;
                    programmeClass.Description = p.Description;
                    programmeClass.DateDebut = p.DateDebut;
                    programmeClass.DateFin = p.DateFin;
                    programmeClass.name = p.name;
                    programmeClass.auteur = p.auteur;
                    programmeClass.Dateenreg = p.Dateenreg;
                    programmeClass.MembreId = p.MembreId;
                    programmeClass.status = p.status;
                }
            }
        }

        public int Ajouter(ProgrammeMentorat_Class programmeClass)
        {
            using (Connection con = new Connection())
            {
                programme.Titre = programmeClass.Titre;
                programme.Description = programmeClass.Description;
                programme.DateDebut = programmeClass.DateDebut;
                programme.DateFin = programmeClass.DateFin;
                programme.name = programmeClass.name;
                programme.auteur = programmeClass.auteur;
                programme.Dateenreg = DateTime.Now;
                programme.MembreId = programmeClass.MembreId;
                programme.status = programmeClass.status ?? "actif";

                try
                {
                    con.Programmementorats.Add(programme);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

       

        public void ChargerProgrammes(GridView gdv, string status = "")
        {
            using (Connection con = new Connection())
            {
                var query = from p in con.Programmementorats
                            join m in con.Membres on p.MembreId equals m.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            where string.IsNullOrEmpty(status) || p.status == status
                            select new
                            {
                                p.ProgrammeMentoratId,
                                p.Titre,
                                p.Description,
                                p.DateDebut,
                                p.DateFin,
                                p.name,
                                p.auteur,
                                p.Dateenreg,
                                p.MembreId,
                                Membre = membre != null ? membre.Nom : "Inconnu",
                                p.status
                            };

                gdv.DataSource = query.OrderByDescending(x => x.Dateenreg).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerProgrammesParMembre(GridView gdv, long membreId)
        {
            using (Connection con = new Connection())
            {
                var query = from p in con.Programmementorats
                            where p.MembreId == membreId
                            select new
                            {
                                p.ProgrammeMentoratId,
                                p.Titre,
                                p.Description,
                                p.DateDebut,
                                p.DateFin,
                                p.name,
                                p.auteur,
                                p.Dateenreg,
                                p.status
                            };

                gdv.DataSource = query.OrderByDescending(x => x.Dateenreg).ToList();
                gdv.DataBind();
            }
        }

        // NOUVELLES MÉTHODES POUR RÉCUPÉRATION DEPUIS POST
        public void chargerProgrammeMentoratFromPost(DropDownList lst, long idorg)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Posts
                           join c in con.CategoriePosts on p.CategoriePostId equals c.CategoriePostId
                           where p.OrganisationId == idorg && p.statut == "programmementorat"
                           select new
                           {
                               PostId = p.PostId,
                               Titre = p.Titre
                           }).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Sélectionner le Programme du Mentorat";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.PostId.ToString();
                        item.Text = data.Titre;
                        lst.Items.Add(item);
                    }
                }
            }
        }

        public void ChargerProgrammesFromPost(GridView gdv, long idorg, string status = "programmementorat")
        {
            using (Connection con = new Connection())
            {
                var query = from p in con.Posts
                            join c in con.CategoriePosts on p.CategoriePostId equals c.CategoriePostId
                            join m in con.Membres on p.MembreId equals m.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            where p.OrganisationId == idorg && p.statut == status
                            select new
                            {
                                PostId = p.PostId,
                                Titre = p.Titre,
                                Description = p.Contenu,
                                DateDebut = p.DatePublication,
                                DateLimiteInscription = p.DateModification,
                                Auteur = p.author,
                                DateCreation = p.DateCreation,
                                MembreId = p.MembreId,
                                Membre = membre != null ? membre.Nom : "Inconnu",
                                Status = p.EstPublie,
                                Categorie = c.Libelle
                            };

                gdv.DataSource = query.OrderByDescending(x => x.DateCreation).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerProgrammesPublicsFromPost(ListView lv, string status = "programmementorat")
        {
            using (Connection con = new Connection())
            {
                var query = from p in con.Posts
                            join c in con.CategoriePosts on p.CategoriePostId equals c.CategoriePostId
                            where p.statut == status && p.EstPublie == "publié"
                            select new
                            {
                                PostId = p.PostId,
                                Nom = p.Titre,
                                Description = p.summery,
                                Duree = p.eventduration,
                                DatePublication = p.DateCreation
                            };

                lv.DataSource = query.OrderByDescending(x => x.DatePublication).ToList();
                lv.DataBind();
            }
        }

        public int countFromPost(long idorg, string status = "programmementorat")
        {
            using (Connection con = new Connection())
            {
                return con.Posts.Count(p => p.OrganisationId == idorg && p.statut == status);
            }
        }

        public void chargerProgrammeMentorat(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Programmementorats select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le Programme du Mentorat";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.ProgrammeMentoratId.ToString();
                        item.Text = data.name;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }

        public int Modifier(ProgrammeMentorat_Class programmeClass)
        {
            using (Connection con = new Connection())
            {
                var p = con.Programmementorats.FirstOrDefault(x => x.ProgrammeMentoratId == programmeClass.ProgrammeMentoratId);
                if (p != null)
                {
                    p.Titre = programmeClass.Titre;
                    p.Description = programmeClass.Description;
                    p.DateDebut = programmeClass.DateDebut;
                    p.DateFin = programmeClass.DateFin;
                    p.name = programmeClass.name;
                    p.auteur = programmeClass.auteur;
                    p.status = programmeClass.status;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(int programmeMentoratId)
        {
            using (Connection con = new Connection())
            {
                var p = con.Programmementorats.FirstOrDefault(x => x.ProgrammeMentoratId == programmeMentoratId);
                if (p != null)
                {
                    con.Programmementorats.Remove(p);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}