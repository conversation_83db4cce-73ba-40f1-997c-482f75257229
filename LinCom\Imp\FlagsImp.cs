using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class FlagsImp
    {
        int msg;
        Flags p = new Flags();

        public int Add(Flags_Class flag)
        {
            using (Connection con = new Connection())
            {
                p.PostId = flag.PostId;
                p.CommentId = flag.CommentId;
                p.FlaggedBy = flag.FlaggedBy;
                p.FlagType = flag.FlagType;
                p.Reason = flag.Reason;
                p.Status = "Pending";
                p.CreatedDate = DateTime.Now;

                try
                {
                    con.Flags.Add(p);
                    if (con.SaveChanges() == 1)
                    {
                        // Notifier les modérateurs
                        NotifierModerateurSignalement(flag.PostId, flag.CommentId, flag.FlagType);
                        
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public int SignalerPost(long postId, long userId, string flagType, string raison)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si l'utilisateur a déjà signalé ce post
                var signalementExistant = con.Flags.Where(f => f.PostId == postId && f.FlaggedBy == userId).FirstOrDefault();
                
                if (signalementExistant == null)
                {
                    var flag = new Flags_Class
                    {
                        PostId = postId,
                        FlaggedBy = userId,
                        FlagType = flagType,
                        Reason = raison
                    };

                    return Add(flag);
                }
                return 0; // Déjà signalé
            }
        }

        public int SignalerCommentaire(long commentId, long userId, string flagType, string raison)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si l'utilisateur a déjà signalé ce commentaire
                var signalementExistant = con.Flags.Where(f => f.CommentId == commentId && f.FlaggedBy == userId).FirstOrDefault();
                
                if (signalementExistant == null)
                {
                    var flag = new Flags_Class
                    {
                        CommentId = commentId,
                        FlaggedBy = userId,
                        FlagType = flagType,
                        Reason = raison
                    };

                    return Add(flag);
                }
                return 0; // Déjà signalé
            }
        }

        public void ChargerSignalementsPendants(ListView lv, int limite = 20)
        {
            using (Connection con = new Connection())
            {
                var signalements = from f in con.Flags
                                  join u in con.Membres on f.FlaggedBy equals u.MembreId
                                  join p in con.SujetForums on f.PostId equals p.SujetForumId into postJoin
                                  from p in postJoin.DefaultIfEmpty()
                                  join c in con.Comments on f.CommentId equals c.CommentId into commentJoin
                                  from c in commentJoin.DefaultIfEmpty()
                                  where f.Status == "Pending"
                                  orderby f.CreatedDate descending
                                  select new
                                  {
                                      f.FlagId,
                                      f.FlagType,
                                      f.Reason,
                                      f.CreatedDate,
                                      FlaggedByName = u.Nom + " " + u.Prenom,
                                      PostTitle = p != null ? p.Title : "",
                                      CommentText = c != null ? c.Text : "",
                                      TargetType = f.PostId.HasValue ? "Post" : "Comment",
                                      TargetId = f.PostId ?? f.CommentId ?? 0,
                                      Severity = GetFlagSeverity(f.FlagType),
                                      FlagIcon = GetFlagIcon(f.FlagType),
                                      StatusColor = "#ffc107" // Pending = jaune
                                  };

                lv.DataSource = signalements.Take(limite).ToList();
                lv.DataBind();
            }
        }

        public void ChargerTousSignalements(ListView lv, string statut = "all")
        {
            using (Connection con = new Connection())
            {
                var query = from f in con.Flags
                           join u in con.Membres on f.FlaggedBy equals u.MembreId
                           join r in con.Membres on f.ReviewedBy equals r.MembreId into reviewJoin
                           from r in reviewJoin.DefaultIfEmpty()
                           join p in con.SujetForums on f.PostId equals p.SujetForumId into postJoin
                           from p in postJoin.DefaultIfEmpty()
                           join c in con.Comments on f.CommentId equals c.CommentId into commentJoin
                           from c in commentJoin.DefaultIfEmpty()
                           select new
                           {
                               f.FlagId,
                               f.FlagType,
                               f.Reason,
                               f.Status,
                               f.CreatedDate,
                               f.ReviewedDate,
                               FlaggedByName = u.Nom + " " + u.Prenom,
                               ReviewedByName = r != null ? r.Nom + " " + r.Prenom : "",
                               PostTitle = p != null ? p.Title : "",
                               CommentText = c != null ? c.Text : "",
                               TargetType = f.PostId.HasValue ? "Post" : "Comment",
                               StatusDisplay = GetStatusDisplay(f.Status),
                               StatusColor = GetStatusColor(f.Status)
                           };

                if (statut != "all")
                {
                    query = query.Where(f => f.Status == statut);
                }

                lv.DataSource = query.OrderByDescending(f => f.CreatedDate).ToList();
                lv.DataBind();
            }
        }

        public int TraiterSignalement(long flagId, long moderateurId, string decision, string commentaire = null)
        {
            using (Connection con = new Connection())
            {
                var flag = con.Flags.Where(f => f.FlagId == flagId).FirstOrDefault();
                
                if (flag != null && flag.Status == "Pending")
                {
                    flag.Status = decision; // "Approved" ou "Rejected"
                    flag.ReviewedBy = moderateurId;
                    flag.ReviewedDate = DateTime.Now;
                    
                    con.SaveChanges();

                    // Si approuvé, appliquer l'action correspondante
                    if (decision == "Approved")
                    {
                        AppliquerActionSignalement(flag);
                    }

                    // Logger l'action de modération
                    LoggerActionModeration(moderateurId, flag, decision, commentaire);

                    return 1;
                }
                return 0;
            }
        }

        private void AppliquerActionSignalement(Flags flag)
        {
            using (Connection con = new Connection())
            {
                switch (flag.FlagType.ToLower())
                {
                    case "spam":
                    case "offensive":
                        // Supprimer le contenu
                        if (flag.PostId.HasValue)
                        {
                            var post = con.SujetForums.Where(p => p.SujetForumId == flag.PostId.Value).FirstOrDefault();
                            if (post != null)
                            {
                                post.DeletionDate = DateTime.Now;
                                con.SaveChanges();
                            }
                        }
                        else if (flag.CommentId.HasValue)
                        {
                            var comment = con.Comments.Where(c => c.CommentId == flag.CommentId.Value).FirstOrDefault();
                            if (comment != null)
                            {
                                comment.DeletionDate = DateTime.Now;
                                con.SaveChanges();
                            }
                        }
                        break;

                    case "lowquality":
                        // Marquer pour révision
                        if (flag.PostId.HasValue)
                        {
                            // Ajouter à la queue de révision
                        }
                        break;

                    case "duplicate":
                        // Marquer comme doublon
                        if (flag.PostId.HasValue)
                        {
                            var post = con.SujetForums.Where(p => p.SujetForumId == flag.PostId.Value).FirstOrDefault();
                            if (post != null)
                            {
                                post.ClosedDate = DateTime.Now;
                                con.SaveChanges();
                            }
                        }
                        break;
                }
            }
        }

        private void LoggerActionModeration(long moderateurId, Flags flag, string decision, string commentaire)
        {
            using (Connection con = new Connection())
            {
                var action = new ModerationActions
                {
                    ModeratorId = moderateurId,
                    TargetType = flag.PostId.HasValue ? "Post" : "Comment",
                    TargetId = flag.PostId ?? flag.CommentId ?? 0,
                    ActionType = $"Flag{decision}",
                    Reason = $"Signalement {flag.FlagType}: {decision}. {commentaire}",
                    ActionDate = DateTime.Now
                };

                con.ModerationActions.Add(action);
                con.SaveChanges();
            }
        }

        private void NotifierModerateurSignalement(long? postId, long? commentId, string flagType)
        {
            using (Connection con = new Connection())
            {
                // Obtenir tous les modérateurs
                var moderateurs = con.Membres.Where(m => m.IsModerator == true).ToList();

                foreach (var moderateur in moderateurs)
                {
                    var notification = new Notification
                    {
                        UserId = moderateur.MembreId,
                        Type = "NewFlag",
                        Title = "Nouveau signalement",
                        Message = $"Un nouveau signalement de type '{flagType}' nécessite votre attention.",
                        RelatedPostId = postId,
                        IsRead = false,
                        CreatedDate = DateTime.Now
                    };

                    con.Notifications.Add(notification);
                }

                con.SaveChanges();
            }
        }

        public void ChargerStatistiquesSignalements(out int total, out int pendants, out int approuves, out int rejetes)
        {
            using (Connection con = new Connection())
            {
                total = con.Flags.Count();
                pendants = con.Flags.Count(f => f.Status == "Pending");
                approuves = con.Flags.Count(f => f.Status == "Approved");
                rejetes = con.Flags.Count(f => f.Status == "Rejected");
            }
        }

        public void ChargerSignalementsParType(ListView lv)
        {
            using (Connection con = new Connection())
            {
                var stats = from f in con.Flags
                           group f by f.FlagType into g
                           orderby g.Count() descending
                           select new
                           {
                               FlagType = g.Key,
                               Total = g.Count(),
                               Pending = g.Count(f => f.Status == "Pending"),
                               Approved = g.Count(f => f.Status == "Approved"),
                               Rejected = g.Count(f => f.Status == "Rejected"),
                               ApprovalRate = g.Count() > 0 ? (double)g.Count(f => f.Status == "Approved") / g.Count() * 100 : 0
                           };

                lv.DataSource = stats.ToList();
                lv.DataBind();
            }
        }

        private int GetFlagSeverity(string flagType)
        {
            switch (flagType.ToLower())
            {
                case "spam":
                case "offensive":
                    return 3; // Haute
                case "lowquality":
                    return 2; // Moyenne
                case "duplicate":
                case "offtopic":
                    return 1; // Basse
                default:
                    return 1;
            }
        }

        private string GetFlagIcon(string flagType)
        {
            switch (flagType.ToLower())
            {
                case "spam":
                    return "fa-ban";
                case "offensive":
                    return "fa-exclamation-triangle";
                case "lowquality":
                    return "fa-thumbs-down";
                case "duplicate":
                    return "fa-copy";
                case "offtopic":
                    return "fa-off";
                default:
                    return "fa-flag";
            }
        }

        private string GetStatusDisplay(string status)
        {
            switch (status)
            {
                case "Pending":
                    return "En attente";
                case "Approved":
                    return "Approuvé";
                case "Rejected":
                    return "Rejeté";
                default:
                    return status;
            }
        }

        private string GetStatusColor(string status)
        {
            switch (status)
            {
                case "Pending":
                    return "#ffc107"; // Jaune
                case "Approved":
                    return "#28a745"; // Vert
                case "Rejected":
                    return "#dc3545"; // Rouge
                default:
                    return "#6c757d"; // Gris
            }
        }

        public void Chargement_GDV(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var obj = (from f in con.Flags
                           join u in con.Membres on f.FlaggedBy equals u.MembreId
                           join p in con.SujetForums on f.PostId equals p.SujetForumId into postJoin
                           from p in postJoin.DefaultIfEmpty()
                           select new
                           {
                               FlagId = f.FlagId,
                               FlagType = f.FlagType,
                               PostTitle = p != null ? p.Title : "Commentaire",
                               FlaggedBy = u.Nom + " " + u.Prenom,
                               Status = f.Status,
                               CreatedDate = f.CreatedDate
                           }).OrderByDescending(f => f.CreatedDate).ToList();

                gdv.DataSource = obj;
                gdv.DataBind();
            }
        }

        public int Count()
        {
            using (Connection con = new Connection())
            {
                return con.Flags.Count();
            }
        }
    }
}
