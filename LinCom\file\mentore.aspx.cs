using LinCom.Class;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class mentore : System.Web.UI.Page
    {
        Mentore_Class mentoreObj = new Mentore_Class();
        IMentore objMentore = new MentoreImp();
        CommonCode co = new CommonCode();
        
        IMembre objMembre = new MembreImp();
        IProgrammeMentorat objProgramme = new ProgrammeMentoratImp();
        
        string nsco;
        int mentoreId;
        long ide;
        int rolid;

        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            if (!string.IsNullOrEmpty(nsco))
            {
                mentoreId = Convert.ToInt32(nsco);
            }

            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {
                    ide = Convert.ToInt64(idperso.Value);
                    rolid = Convert.ToInt32(role.Value);
                }
                else
                {
                    Response.Redirect("~/login.aspx");
                }

                InitialiserMessages();
                ChargerDropDownLists();

                // Si modification, charger les données
                if (mentoreId > 0)
                {
                    ChargerDonneesMentore();
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_success.Visible = false;
            div_msg_error.Visible = false;
        }

        private void ChargerDropDownLists()
        {
            try
            {
                // Charger les membres
                objMembre.chargerMembre(drpdMembre);

                // Charger les programmes de mentorat depuis la table Post
                IOrganisation objOrganisation = new OrganisationImp();
                Organisation_Class org = new Organisation_Class();
                MembresOrganisation_Class memorg = new MembresOrganisation_Class();
                IMembresOrganisation objmemorg = new MembresOrganisationImp();

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                long idorg = Convert.ToInt64(memorg.OrganisationId);

                objProgramme.chargerProgrammeMentoratFromPost(drpdProgramme, idorg);
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors du chargement des données : " + ex.Message;
            }
        }

        private void ChargerDonneesMentore()
        {
            try
            {
                objMentore.AfficherDetails(mentoreId, mentoreObj);
                
                drpdMembre.SelectedValue = mentoreObj.MembreId.ToString();
                drpdProgramme.SelectedValue = mentoreObj.ProgrammeMentoratId.ToString();
                drpdStatut.SelectedValue = mentoreObj.status;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors du chargement des données : " + ex.Message;
            }
        }

        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            try
            {
                if (ValiderFormulaire())
                {
                    if (mentoreId > 0)
                    {
                        ModifierMentore();
                    }
                    else
                    {
                        AjouterMentore();
                    }
                }
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de l'enregistrement : " + ex.Message;
            }
        }

        private bool ValiderFormulaire()
        {
            if (drpdMembre.SelectedValue == "-1" ||
                drpdProgramme.SelectedValue == "-1" ||
                drpdStatut.SelectedValue == "-1")
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                return false;
            }
            return true;
        }

        private void AjouterMentore()
        {
            mentoreObj.MembreId = Convert.ToInt64(drpdMembre.SelectedValue);
            mentoreObj.ProgrammeMentoratId = Convert.ToInt32(drpdProgramme.SelectedValue);
            mentoreObj.status = drpdStatut.SelectedValue;

            int resultat = objMentore.Ajouter(mentoreObj);

            if (resultat == 1)
            {
                div_msg_success.Visible = true;
                msg_success.InnerText = "Mentoré ajouté avec succès";
                ViderFormulaire();
            }
            else
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de l'ajout du mentoré";
            }
        }

        private void ModifierMentore()
        {
            mentoreObj.MembreId = Convert.ToInt64(drpdMembre.SelectedValue);
            mentoreObj.ProgrammeMentoratId = Convert.ToInt32(drpdProgramme.SelectedValue);
            mentoreObj.status = drpdStatut.SelectedValue;

            int resultat = objMentore.Modifier(mentoreObj);

            if (resultat == 1)
            {
                div_msg_success.Visible = true;
                msg_success.InnerText = "Mentoré modifié avec succès";
                Response.Redirect("listmentore.aspx");
            }
            else
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de la modification du mentoré";
            }
        }

        private void ViderFormulaire()
        {
            drpdMembre.SelectedValue = "-1";
            drpdProgramme.SelectedValue = "-1";
            drpdStatut.SelectedValue = "-1";
        }
    }
}
