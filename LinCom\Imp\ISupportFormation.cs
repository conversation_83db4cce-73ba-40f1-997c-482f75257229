﻿using LinCom.Class;
using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ISupportFormation
    {
        void AfficherDetails(int idSupport, SupportFormation_Class support);
        int Ajouter(SupportFormation_Class support);
        int Modifier(SupportFormation_Class support);
        int Supprimer(int idSupport);
        void ChargerSupport(GridView gdv);
        void chargerSupportFormation(DropDownList ddl);
    }
}
