<%@ Page Title="Modération - LinCom" Language="C#" MasterPageFile="~/MasterPage.Master" AutoEventWireup="true" CodeBehind="moderation-dashboard.aspx.cs" Inherits="LinCom.moderation_dashboard" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="Content/forum-lincom.css" rel="stylesheet" />
    <style>
        .moderation-header {
            background: var(--lincom-gradient-primary);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-bottom: 4px solid var(--lincom-accent);
        }
        
        .mod-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .mod-stat-card {
            background: var(--lincom-white);
            border: 1px solid var(--lincom-border);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            border-left: 4px solid var(--lincom-secondary);
            transition: all 0.3s ease;
        }
        
        .mod-stat-card.urgent {
            border-left-color: var(--lincom-accent);
            background: rgba(231, 76, 60, 0.02);
        }
        
        .mod-stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--lincom-shadow-hover);
        }
        
        .mod-stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--lincom-secondary);
            display: block;
        }
        
        .mod-stat-card.urgent .mod-stat-number {
            color: var(--lincom-accent);
        }
        
        .mod-stat-label {
            color: var(--lincom-text-muted);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .mod-queue-item {
            background: var(--lincom-white);
            border: 1px solid var(--lincom-border);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--lincom-warning);
        }
        
        .mod-queue-item.high-priority {
            border-left-color: var(--lincom-accent);
        }
        
        .mod-queue-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .mod-queue-title {
            font-weight: 600;
            color: var(--lincom-primary);
        }
        
        .mod-queue-meta {
            color: var(--lincom-text-muted);
            font-size: 0.9rem;
        }
        
        .mod-queue-content {
            background: var(--lincom-light);
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            border-left: 3px solid var(--lincom-border);
        }
        
        .mod-queue-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .mod-action-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .mod-action-approve {
            background: var(--lincom-success);
            color: white;
        }
        
        .mod-action-approve:hover {
            background: #1e7e34;
            transform: translateY(-1px);
        }
        
        .mod-action-reject {
            background: var(--lincom-accent);
            color: white;
        }
        
        .mod-action-reject:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
        
        .mod-action-edit {
            background: var(--lincom-secondary);
            color: white;
        }
        
        .mod-action-edit:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .mod-action-close {
            background: var(--lincom-warning);
            color: white;
        }
        
        .mod-action-close:hover {
            background: #e0a800;
            transform: translateY(-1px);
        }
        
        .mod-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid var(--lincom-border);
        }
        
        .mod-tab {
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 500;
            color: var(--lincom-text-muted);
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .mod-tab.active {
            color: var(--lincom-accent);
            border-bottom-color: var(--lincom-accent);
            background: rgba(231, 76, 60, 0.05);
        }
        
        .mod-tab:hover {
            color: var(--lincom-accent);
            background: rgba(231, 76, 60, 0.03);
        }
        
        .mod-tab .badge {
            background: var(--lincom-accent);
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-size: 0.7rem;
            margin-left: 0.5rem;
        }
        
        .mod-reason-select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--lincom-border);
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }
        
        .mod-comment-box {
            width: 100%;
            min-height: 80px;
            padding: 0.5rem;
            border: 1px solid var(--lincom-border);
            border-radius: 4px;
            resize: vertical;
        }
        
        .priority-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .priority-low { background: var(--lincom-success); }
        .priority-medium { background: var(--lincom-warning); }
        .priority-high { background: var(--lincom-accent); }
        
        .mod-user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .mod-user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--lincom-secondary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .mod-user-name {
            font-weight: 500;
            color: var(--lincom-primary);
        }
        
        .mod-user-rep {
            color: var(--lincom-text-muted);
            font-size: 0.8rem;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Header -->
    <div class="moderation-header">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h1><i class="fas fa-shield-alt"></i> Tableau de Modération</h1>
                    <p class="lead">Gérez la qualité et la sécurité du forum LinCom</p>
                </div>
                <div class="col-md-4 text-right">
                    <span class="badge badge-light">
                        Modérateur : <asp:Label ID="lblModeratorName" runat="server"></asp:Label>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistiques de Modération -->
        <div class="mod-stats">
            <div class="mod-stat-card urgent">
                <span class="mod-stat-number">
                    <asp:Label ID="lblPendingFlags" runat="server" Text="0"></asp:Label>
                </span>
                <div class="mod-stat-label">
                    <i class="fas fa-flag"></i> Signalements en attente
                </div>
            </div>
            <div class="mod-stat-card">
                <span class="mod-stat-number">
                    <asp:Label ID="lblReviewQueue" runat="server" Text="0"></asp:Label>
                </span>
                <div class="mod-stat-label">
                    <i class="fas fa-eye"></i> Posts à réviser
                </div>
            </div>
            <div class="mod-stat-card">
                <span class="mod-stat-number">
                    <asp:Label ID="lblTodayActions" runat="server" Text="0"></asp:Label>
                </span>
                <div class="mod-stat-label">
                    <i class="fas fa-check"></i> Actions aujourd'hui
                </div>
            </div>
            <div class="mod-stat-card">
                <span class="mod-stat-number">
                    <asp:Label ID="lblActiveUsers" runat="server" Text="0"></asp:Label>
                </span>
                <div class="mod-stat-label">
                    <i class="fas fa-users"></i> Utilisateurs actifs
                </div>
            </div>
        </div>

        <!-- Onglets de Modération -->
        <div class="mod-tabs">
            <asp:LinkButton ID="lnkFlags" runat="server" CssClass="mod-tab active" 
                OnClick="SwitchTab_Click" CommandArgument="flags">
                <i class="fas fa-flag"></i> Signalements
                <span class="badge">
                    <asp:Label ID="lblFlagsCount" runat="server" Text="0"></asp:Label>
                </span>
            </asp:LinkButton>
            <asp:LinkButton ID="lnkReview" runat="server" CssClass="mod-tab" 
                OnClick="SwitchTab_Click" CommandArgument="review">
                <i class="fas fa-eye"></i> Révision
                <span class="badge">
                    <asp:Label ID="lblReviewCount" runat="server" Text="0"></asp:Label>
                </span>
            </asp:LinkButton>
            <asp:LinkButton ID="lnkUsers" runat="server" CssClass="mod-tab" 
                OnClick="SwitchTab_Click" CommandArgument="users">
                <i class="fas fa-users"></i> Utilisateurs
            </asp:LinkButton>
            <asp:LinkButton ID="lnkHistory" runat="server" CssClass="mod-tab" 
                OnClick="SwitchTab_Click" CommandArgument="history">
                <i class="fas fa-history"></i> Historique
            </asp:LinkButton>
        </div>

        <!-- Contenu des Onglets -->
        <div id="flagsTab" runat="server">
            <h3>Signalements en Attente</h3>
            <asp:ListView ID="lvFlags" runat="server" OnItemCommand="lvFlags_ItemCommand">
                <LayoutTemplate>
                    <div id="itemPlaceholder" runat="server"></div>
                </LayoutTemplate>
                <ItemTemplate>
                    <div class="mod-queue-item <%# GetPriorityClass(Eval("Priority").ToString()) %>">
                        <div class="mod-queue-header">
                            <div class="mod-queue-title">
                                <span class="priority-indicator <%# GetPriorityClass(Eval("Priority").ToString()) %>"></span>
                                Signalement : <%# Eval("FlagType") %>
                            </div>
                            <div class="mod-queue-meta">
                                <%# GetTimeAgo(Convert.ToDateTime(Eval("CreatedDate"))) %>
                            </div>
                        </div>
                        
                        <div class="mod-user-info">
                            <div class="mod-user-avatar">
                                <%# GetUserInitials(Eval("FlaggedByName").ToString()) %>
                            </div>
                            <span class="mod-user-name"><%# Eval("FlaggedByName") %></span>
                            <span class="mod-user-rep">(<%# Eval("FlaggedByReputation") %> pts)</span>
                        </div>
                        
                        <div class="mod-queue-content">
                            <strong>Raison :</strong> <%# Eval("Reason") %><br>
                            <strong>Contenu signalé :</strong><br>
                            <%# TruncateText(Eval("PostContent").ToString(), 200) %>
                        </div>
                        
                        <div class="mod-queue-actions">
                            <asp:Button runat="server" Text="Approuver" CssClass="mod-action-btn mod-action-approve" 
                                CommandName="Approve" CommandArgument='<%# Eval("FlagId") %>' />
                            <asp:Button runat="server" Text="Rejeter" CssClass="mod-action-btn mod-action-reject" 
                                CommandName="Reject" CommandArgument='<%# Eval("FlagId") %>' />
                            <asp:Button runat="server" Text="Éditer Post" CssClass="mod-action-btn mod-action-edit" 
                                CommandName="Edit" CommandArgument='<%# Eval("PostId") %>' />
                            <asp:Button runat="server" Text="Fermer" CssClass="mod-action-btn mod-action-close" 
                                CommandName="Close" CommandArgument='<%# Eval("PostId") %>' />
                        </div>
                    </div>
                </ItemTemplate>
                <EmptyDataTemplate>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h4>Aucun signalement en attente</h4>
                        <p class="text-muted">Excellent travail ! Tous les signalements ont été traités.</p>
                    </div>
                </EmptyDataTemplate>
            </asp:ListView>
        </div>

        <div id="reviewTab" runat="server" style="display: none;">
            <h3>Posts à Réviser</h3>
            <asp:ListView ID="lvReview" runat="server" OnItemCommand="lvReview_ItemCommand">
                <LayoutTemplate>
                    <div id="itemPlaceholder" runat="server"></div>
                </LayoutTemplate>
                <ItemTemplate>
                    <div class="mod-queue-item">
                        <div class="mod-queue-header">
                            <div class="mod-queue-title">
                                <%# Eval("PostType") %> : <%# TruncateText(Eval("Title").ToString(), 60) %>
                            </div>
                            <div class="mod-queue-meta">
                                Score: <%# Eval("Score") %> • <%# GetTimeAgo(Convert.ToDateTime(Eval("CreationDate"))) %>
                            </div>
                        </div>
                        
                        <div class="mod-user-info">
                            <div class="mod-user-avatar">
                                <%# GetUserInitials(Eval("AuthorName").ToString()) %>
                            </div>
                            <span class="mod-user-name"><%# Eval("AuthorName") %></span>
                            <span class="mod-user-rep">(<%# Eval("AuthorReputation") %> pts)</span>
                        </div>
                        
                        <div class="mod-queue-content">
                            <%# TruncateText(Eval("Body").ToString(), 300) %>
                        </div>
                        
                        <div class="mod-queue-actions">
                            <asp:Button runat="server" Text="Approuver" CssClass="mod-action-btn mod-action-approve" 
                                CommandName="ApprovePost" CommandArgument='<%# Eval("PostId") %>' />
                            <asp:Button runat="server" Text="Éditer" CssClass="mod-action-btn mod-action-edit" 
                                CommandName="EditPost" CommandArgument='<%# Eval("PostId") %>' />
                            <asp:Button runat="server" Text="Supprimer" CssClass="mod-action-btn mod-action-reject" 
                                CommandName="DeletePost" CommandArgument='<%# Eval("PostId") %>' />
                        </div>
                    </div>
                </ItemTemplate>
                <EmptyDataTemplate>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h4>Aucun post à réviser</h4>
                        <p class="text-muted">Tous les posts ont été révisés.</p>
                    </div>
                </EmptyDataTemplate>
            </asp:ListView>
        </div>

        <div id="usersTab" runat="server" style="display: none;">
            <h3>Gestion des Utilisateurs</h3>
            <p class="text-muted">Fonctionnalité en développement...</p>
        </div>

        <div id="historyTab" runat="server" style="display: none;">
            <h3>Historique des Actions</h3>
            <p class="text-muted">Fonctionnalité en développement...</p>
        </div>
    </div>

    <script>
        // Auto-refresh toutes les 30 secondes
        setInterval(function() {
            // Vérifier s'il y a de nouveaux signalements
            fetch('/api/moderation/check-new')
                .then(response => response.json())
                .then(data => {
                    if (data.hasNew) {
                        // Mettre à jour les compteurs
                        document.getElementById('<%=lblPendingFlags.ClientID%>').textContent = data.pendingFlags;
                        document.getElementById('<%=lblFlagsCount.ClientID%>').textContent = data.pendingFlags;
                        
                        // Afficher une notification
                        toastr.info('Nouveaux signalements disponibles');
                    }
                })
                .catch(error => console.error('Erreur:', error));
        }, 30000);

        // Confirmation pour les actions destructives
        document.querySelectorAll('.mod-action-reject, .mod-action-close').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (!confirm('Êtes-vous sûr de vouloir effectuer cette action ?')) {
                    e.preventDefault();
                }
            });
        });
    </script>
</asp:Content>
