﻿using Antlr.Runtime.Misc;
using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class ressources : System.Web.UI.Page
    {
        private int info;
        int formationId;
        Ressources_Class res = new Ressources_Class();
        Ressources_Class ress = new Ressources_Class();
        DomaineRessource_Class domaineFormationClass = new DomaineRessource_Class();
       
        IRessource obj = new RessourceImp();
      
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        DomaineRessource_Class actco = new DomaineRessource_Class();
        IDomaineRessource objactco = new DomaineRessourceImp();
        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        static string imge, imge1, pdfe, nameorg;
        static long ide, idorg;
        static int rolid;
        long index;
        static string nsco;
        DataTable dat = new DataTable();


        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];

            if (!IsPostBack)
            {
                InitialiserMessages();
                ChargerDomaines();

                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    ide = Convert.ToInt64(idperso.Value);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                objdom.chargerDomaineInterventionOrganisation(drpddomai, idorg);

                // Vérifier si un ID est passé en paramètre pour l'édition
                if (ViewState["Record"] == null)
                {//depense
                    dat.Columns.Add("#Code");
                    dat.Columns.Add("Domaine d'Intervention");

                    ViewState["Record"] = dat;
                }

                if (Request.QueryString["id"] != null)
                {
                    formationId = Convert.ToInt32(Request.QueryString["id"]);
                    btn_ajouter.InnerText = "Modifier";
                    AfficherDetails();
                }
                else
                {
                    btn_ajouter.InnerText = "Ajouter";
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
        }

        private void ChargerDomaines()
        {
            // Utilisation de la méthode implémentée dans FormationImp
            //  objFormation.ChargerDomaines(drpddomain);
        }

        protected void btn_ajouter_ServerClick(object sender, EventArgs e)
        {
            if (nsco != null)
            {
                ModifierRessources();
            }
            else
            {
                AjouterRessources();
            }
        }

        protected void btn_ajouter_domaine_ServerClick(object sender, EventArgs e)
        {
            AjouterDomaineRessources();
        }
        private string Uploadoc(FileUpload fil)
        {
            if (fil.HasFile)
            {
                string fileName = Path.GetFileName(fil.FileName);
                string extension = Path.GetExtension(fileName).ToLower();

                string[] validExtensions = { ".pdf" };

                if (!validExtensions.Contains(extension))
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Le document doit avoir ces extensions : .pdf ";

                    return null;
                }

                if (fil.PostedFile.ContentLength > 104857600)
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Votre document est trop volumineux. Il doit faire moins de 100MB.";

                    return null;
                }

                string path = Server.MapPath("~/file/ressource/") + fileName;
                fil.SaveAs(path);
                imge = fileName;

            }
            else
            {
                if (nsco == null)
                {
                    imge = "No Doc";
                }
                else
                {
                    obj.AfficherDetails(Convert.ToInt64(nsco), idorg, 0, ress);

                    imge = ress.Fichier;


                }
            }
            return imge;

        }
        private string UploadImage(FileUpload fil)
        {
            if (fil.HasFile)
            {
                string fileName = Path.GetFileName(fil.FileName);
                string extension = Path.GetExtension(fileName).ToLower();

                string[] validExtensions = { ".png", ".jpeg", ".gif", ".jpg", ".jfif" };

                if (!validExtensions.Contains(extension))
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "La photo doit avoir ces extensions : .png, .jpg, .gif, .jpeg, .jfif. ";

                    return null;
                }

                if (fil.PostedFile.ContentLength > 104857600)
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Votre photo est trop volumineuse. Elle doit faire moins de 100MB.";

                    return null;
                }

                string path = Server.MapPath("~/file/ressource/") + fileName;
                fil.SaveAs(path);
                imge = fileName;

            }
            else
            {
                if (nsco == null)
                {
                    imge = "No Image";
                }
                else
                {

                    obj.AfficherDetails(Convert.ToInt64(nsco), idorg, 0, ress);

                     imge = ress.photocouverture; 
                   
                }
            }
            return imge;

        }
        void AjouterDomaineRessources()
        {
            try
            {
                int dc = GridView1.Rows.Count;
                if (dc > 0)
                {
                    foreach (GridViewRow row in this.GridView1.Rows)
                    {
                        if (row.RowType == DataControlRowType.DataRow)
                        {
                            obj.AfficherDetails(co.GenerateSlug(txtTitre.Value), idorg, 0, ress);
                            index = (long)ress.RessourceId;

                            actco.RessourceId = index;
                            objdom.AfficherDetails(Convert.ToInt32(row.Cells[0].Text), domai, idorg, 0);
                            actco.DomaineInterventionId = Convert.ToInt32(domai.DomaineInterventionOrganisationId);
                            actco.DateCreation = DateTime.Now;
                            actco.statut = "actif";
                            actco.MembreId = ide;
                            actco.OrganisationId = idorg;


                            objactco.Ajout(actco);

                        }
                    }

                }

            }
            catch (Exception ex)
            { // logguer ou afficher les erreurs pour le débogage
                System.Diagnostics.Debug.WriteLine("Erreur dans Domaines d'Intervention de l'organisation : " + ex.Message);

            }

        }
        protected void btnajoutdom_Click(object sender, EventArgs e)
        {
            try
            {
                if (drpddomai.SelectedValue == "-1" || drpddomai.SelectedValue == "0")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    dom.Visible = true;
                    int i = GridView1.Rows.Count;
                    dat = (DataTable)ViewState["Record"];
                    dat.Rows.Add(drpddomai.SelectedValue, drpddomai.SelectedItem);
                    GridView1.DataSource = dat;
                    GridView1.DataBind();

                }

            }
            catch (Exception ec)
            {

            }
        }

        protected void btnvider_Click(object sender, EventArgs e)
        {
            dom.Visible = true;
            dat = (DataTable)ViewState["Record"];
            dat.Rows.Clear();
            GridView1.DataSource = dat;
            GridView1.DataBind();
        }
        public void remplissagedomainepost()
        {
            try
            {
                if (drpddomai.SelectedValue == "-1" || drpddomai.SelectedValue == "0")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    dom.Visible = true;
                    int i = GridView1.Rows.Count;
                    dat = (DataTable)ViewState["Record"];
                    dat.Rows.Add(drpddomai.SelectedValue, drpddomai.SelectedItem);
                    GridView1.DataSource = dat;
                    GridView1.DataBind();

                }

            }
            catch (Exception ec)
            {

            }
        }
        protected void btnannuldom_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = true;
            dat = (DataTable)ViewState["Record"];
            dat.Rows.Clear();
            GridView1.DataSource = dat;
            GridView1.DataBind();
        }
        protected void ajoudom_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = false;
           // ajourdom.Visible = true;
        }

        protected void btnajoutnouvdom_ServerClick(object sender, EventArgs e)
        {
            //  ad();
        }

        protected void drpddomai_SelectedIndexChanged(object sender, EventArgs e)
        {
            dom.Visible = true;
            remplissagedomainepost();
        }
        protected void btnajourdom_ServerClick(object sender, EventArgs e)
        {
            remplissagedomainepost();
        }


        private void AjouterRessources()
        {
            try
            {
                if (string.IsNullOrEmpty(txtTitre.Value) || string.IsNullOrEmpty(txtDatePublication.Value) ||
                    string.IsNullOrEmpty(txtdescription.Value) || drpdstatutforma.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }


                res.Titre = txtTitre.Value;
                res.Description = txtdescription.Value;
                res.DateFormation = txtDatePublication.Value;
                res.Fichier = Uploadoc(fileupdoc);
                res.AuteurId = (int?)ide;
                res.name = co.GenerateSlug(txtTitre.Value);
                res.photocouverture = UploadImage(fileupd);
                res.nombrepage = Convert.ToInt32(txtnombrepage.Value);
                res.typeressources = drpdtyperessources.SelectedValue;
                res.OrganisationId = idorg;
                res.nbrevue =0;
                res.nbrelike = 0;
                res.DatePublication = Convert.ToDateTime(txtDatePublication.Value).ToString();
                res.DateCreation = DateTime.Now;
                res.statut = drpdstatutforma.SelectedValue;
                res.MembreId = ide;
                res.MOIS = Convert.ToDateTime(txtDatePublication.Value).Month.ToString();
                res.ANNEE = Convert.ToDateTime(txtDatePublication.Value).Year.ToString();

             
                info = obj.Ajout(res);

                if (info == 1)
                {
                        AjouterDomaineRessources();
                   
                    div_msg_succes.Visible = true;
                    msg_succes.InnerText = "La formation a été enregistrée avec succès";
                    ReinitialiserFormulaire();
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de l'enregistrement de la formation";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        private void ModifierRessources()
        {
            try
            {

                if (string.IsNullOrEmpty(txtTitre.Value) || string.IsNullOrEmpty(txtDatePublication.Value) ||
                    string.IsNullOrEmpty(txtdescription.Value) || drpdstatutforma.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }


                res.Titre = txtTitre.Value;
                res.Description = txtdescription.Value;
                res.DateFormation = txtDatePublication.Value;
                res.Fichier = Uploadoc(fileupdoc);
                res.AuteurId = (int?)ide;
                res.name = co.GenerateSlug(txtTitre.Value);
                res.photocouverture = UploadImage(fileupd);
                res.nombrepage = Convert.ToInt32(txtnombrepage.Value);
                res.typeressources = drpdtyperessources.SelectedValue;
           
                res.DatePublication = Convert.ToDateTime(txtDatePublication.Value).ToString();
                res.DateCreation = DateTime.Now;
                res.statut = drpdstatutforma.SelectedValue;
                res.MOIS = Convert.ToDateTime(txtDatePublication.Value).Month.ToString();
                res.ANNEE = Convert.ToDateTime(txtDatePublication.Value).Year.ToString();


                info = obj.edit(res, Convert.ToInt64(nsco), idorg);

                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    msg_succes.InnerText = "La Ressource a été enregistrée avec succès";
                    ReinitialiserFormulaire();
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de la modification de la Ressource";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        private void AfficherDetails()
        {
            if (formationId > 0)
            {
                obj.AfficherDetails(Convert.ToInt64(nsco), idorg, 0, ress);

                if (ress.RessourceId > 0)
                {
                    txtTitre.Value = res.Titre;
                    txtdescription.Value = res.Description;
                    txtDatePublication.Value = res.DateFormation;

                    txtnombrepage.Value = res.nombrepage.ToString();
                    drpdtyperessources.SelectedValue = res.typeressources;

                    drpdstatutforma.SelectedValue = res.statut;

                }
            }
        }

        private void ReinitialiserFormulaire()
        {
            txtTitre.Value = "";
            txtdescription.Value = "";
            txtDatePublication.Value = "";

            txtnombrepage.Value ="";
            drpdtyperessources.SelectedValue = "-1";

            drpdstatutforma.SelectedValue = "-1";
        }
    }
}