﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{

    public class SujetForumImp : ISujetForm
    {
           int msg;
   SujetForum p = new SujetForum();
   public int add(SujetForum_Class add)
   {
       using (Connection con = new Connection())
       {
           p.Titre = add.Titre;
           p.ForumId = add.ForumId;
           p.MembreId = add.MembreId;
           p.DateCreation = add.DateCreation;
           p.name = add.name;



           try
           {
               con.SujetForums.Add(p);

               if (con.SaveChanges() == 1)
               {
                   con.SujetForums.Add(p);
                   return msg = 1;
               }
               else
                   return msg = 0;
           }
           catch (Exception e)
           {

           }


       }
       return msg;
   }

   public void afficherDetails(int code, SujetForum_Class pr)
   {
       using (Connection con = new Connection())
       {
           p = con.SujetForums.Where(x => x.SujetForumId == code).FirstOrDefault();

           if (p != null)
           {


               pr.Titre = p.Titre;
               pr.ForumId = p.ForumId;
               pr.MembreId = p.MembreId;
               pr.DateCreation = p.DateCreation;
               pr.name = p.name;

           }

       }
   }

   public void afficherDetails(string code, SujetForum_Class pr)
   {
       throw new NotImplementedException();
   }

   public void Chargement_GDV(GridView GV_apv)
   {
       using (Connection con = new Connection())
       {

           var obj = (from ep in con.SujetForums
                      select new
                      {


                          SujetForumId = ep.SujetForumId,
                          titre = ep.Titre,
                          idForum = ep.ForumId,
                          membreId = ep.MembreId,
                          dateCreate = ep.DateCreation,
                          nom = ep.name,


                      }).ToList();

           GV_apv.DataSource = obj;
           GV_apv.DataBind();

       }
   }

   public void chargerForum(DropDownList lst)
   {
       throw new NotImplementedException();
   }

   public int count()
   {
       int n = 0;
       using (Connection con = new Connection())
       {
           var b = (from l in con.SujetForums
                    select l).Count();
           n = b;
       }
       return n;
   }

   public int edit(SujetForum_Class cl, int id)
   {
       using (Connection con = new Connection())
       {
           p = con.SujetForums.Where(x => x.SujetForumId == id).FirstOrDefault();

           try
           {
               p.Titre = cl.Titre;
               p.ForumId = cl.ForumId;
               p.MembreId = cl.MembreId;
               p.DateCreation = cl.DateCreation;
               p.name = cl.name;


               if (con.SaveChanges() == 1)
               {
                   con.SujetForums.Add(p);
                   con.Entry(p).State = EntityState.Modified;
                   return msg = 1;
               }
               else
                   return msg = 0;
           }
           catch (Exception e)
           {

           }
           return msg;
       }
   }

   public void search(GridView GV_apv, string code)
   {
       // SÉCURITÉ : Validation et nettoyage de l'entrée
       if (string.IsNullOrWhiteSpace(code))
       {
           GV_apv.DataSource = null;
           GV_apv.DataBind();
           return;
       }

       // Nettoyer et valider le code de recherche
       code = System.Web.HttpUtility.HtmlEncode(code.Trim());

       // Limiter la longueur pour éviter les attaques DoS
       if (code.Length > 100)
       {
           code = code.Substring(0, 100);
       }

       using (Connection con = new Connection())
       {
           try
           {
               // SÉCURISÉ : Utilisation de LINQ avec paramètres sécurisés
               var obj = (from ep in con.SujetForums
                         join e in con.Membres on ep.MembreId equals e.MembreId
                         where (ep.Titre.Contains(code) ||
                                ep.Body.Contains(code) ||
                                ep.Tags.Contains(code) ||
                                e.Nom.Contains(code) ||
                                e.Prenom.Contains(code))
                         select new
                         {
                             SujetForumId = ep.SujetForumId,
                             titre = ep.Titre,
                             idForum = ep.ForumId,
                             membreId = ep.MembreId,
                             dateCreate = ep.DateCreation,
                             nom = ep.name,
                             score = ep.Score,
                             viewCount = ep.ViewCount
                         }).Take(50).ToList(); // Limiter les résultats

               GV_apv.DataSource = obj;
               GV_apv.DataBind();
           }
           catch (Exception ex)
           {
               // Log l'erreur de manière sécurisée
               System.Diagnostics.Debug.WriteLine($"Erreur de recherche : {ex.Message}");
               GV_apv.DataSource = null;
               GV_apv.DataBind();
           }
       }
   }

   public int supprimer(int id)
   {
       using (Connection con = new Connection())
       {

           p = con.SujetForums.Where(x => x.SujetForumId == id).First();

           if (con.Entry(p).State == EntityState.Detached)
               con.SujetForums.Attach(p);

           con.SujetForums.Remove(p);
           con.SaveChanges();

           return msg = 1;
       }
   }

        // MÉTHODES STACK OVERFLOW STYLE

        public int CreerQuestion(SujetForum_Class question)
        {
            using (Connection con = new Connection())
            {
                var nouveauPost = new SujetForum
                {
                    PostTypeId = 1, // Question
                    Title = question.Title,
                    Body = question.Body,
                    OwnerUserId = question.OwnerUserId,
                    OwnerDisplayName = question.OwnerDisplayName,
                    CreationDate = DateTime.Now,
                    LastActivityDate = DateTime.Now,
                    Tags = question.Tags,
                    CategoryId = question.CategoryId,
                    Score = 0,
                    ViewCount = 0,
                    AnswerCount = 0,
                    CommentCount = 0,
                    FavoriteCount = 0,
                    ContentLicense = "CC BY-SA 4.0",
                    Difficulty = question.Difficulty ?? "Beginner",
                    Language = question.Language ?? "fr",
                    IsPinned = false,
                    IsFeatured = false,
                    IsLocked = false,
                    Priority = question.Priority
                };

                try
                {
                    con.SujetForums.Add(nouveauPost);
                    if (con.SaveChanges() == 1)
                    {
                        return (int)nouveauPost.SujetForumId;
                    }
                    return 0;
                }
                catch (Exception e)
                {
                    return 0;
                }
            }
        }

        public int CreerReponse(SujetForum_Class reponse, long questionId)
        {
            using (Connection con = new Connection())
            {
                var nouvelleReponse = new SujetForum
                {
                    PostTypeId = 2, // Réponse
                    ParentId = questionId,
                    Body = reponse.Body,
                    OwnerUserId = reponse.OwnerUserId,
                    OwnerDisplayName = reponse.OwnerDisplayName,
                    CreationDate = DateTime.Now,
                    LastActivityDate = DateTime.Now,
                    Score = 0,
                    ViewCount = 0,
                    CommentCount = 0,
                    ContentLicense = "CC BY-SA 4.0"
                };

                try
                {
                    con.SujetForums.Add(nouvelleReponse);
                    if (con.SaveChanges() == 1)
                    {
                        // Mettre à jour le compteur de réponses de la question
                        var question = con.SujetForums.Where(q => q.SujetForumId == questionId).FirstOrDefault();
                        if (question != null)
                        {
                            question.AnswerCount++;
                            question.LastActivityDate = DateTime.Now;
                            con.SaveChanges();
                        }

                        return (int)nouvelleReponse.SujetForumId;
                    }
                    return 0;
                }
                catch (Exception e)
                {
                    return 0;
                }
            }
        }

        public void ChargerQuestions(ListView lv, string filtre = "recent", long? categoryId = null)
        {
            using (Connection con = new Connection())
            {
                var query = from q in con.SujetForums
                           join m in con.Membres on q.OwnerUserId equals m.MembreId into memberJoin
                           from m in memberJoin.DefaultIfEmpty()
                           join c in con.Categories on q.CategoryId equals c.CategoryId into catJoin
                           from c in catJoin.DefaultIfEmpty()
                           where q.PostTypeId == 1 // Questions seulement
                           select new
                           {
                               q.SujetForumId,
                               q.Title,
                               q.Body,
                               q.Score,
                               q.ViewCount,
                               q.AnswerCount,
                               q.CreationDate,
                               q.LastActivityDate,
                               q.Tags,
                               q.IsPinned,
                               q.IsFeatured,
                               OwnerName = m != null ? m.Nom + " " + m.Prenom : q.OwnerDisplayName,
                               OwnerReputation = m != null ? m.Reputation : 0,
                               CategoryName = c != null ? c.Name : "",
                               HasAcceptedAnswer = q.AcceptedAnswerId != null,
                               IsAnswered = q.AnswerCount > 0
                           };

                // Filtrer par catégorie si spécifiée
                if (categoryId.HasValue)
                {
                    query = query.Where(q => q.CategoryId == categoryId.Value);
                }

                // Appliquer le filtre
                switch (filtre.ToLower())
                {
                    case "popular":
                        query = query.OrderByDescending(q => q.Score).ThenByDescending(q => q.ViewCount);
                        break;
                    case "unanswered":
                        query = query.Where(q => q.AnswerCount == 0).OrderByDescending(q => q.CreationDate);
                        break;
                    case "featured":
                        query = query.Where(q => q.IsFeatured).OrderByDescending(q => q.CreationDate);
                        break;
                    case "active":
                        query = query.OrderByDescending(q => q.LastActivityDate);
                        break;
                    default: // recent
                        query = query.OrderByDescending(q => q.CreationDate);
                        break;
                }

                lv.DataSource = query.Take(50).ToList();
                lv.DataBind();
            }
        }

        // Méthodes supplémentaires à implémenter...
        public void ChargerQuestionsUtilisateur(ListView lv, long userId, string filtre = "recent") { throw new NotImplementedException(); }
        public void ChargerQuestionAvecReponses(long questionId, SujetForum_Class question, ListView lvReponses) { throw new NotImplementedException(); }
        public void ChargerReponses(ListView lv, long questionId) { throw new NotImplementedException(); }
        public void MarquerReponseAcceptee(long questionId, long reponseId, long userId) { throw new NotImplementedException(); }
        public void SupprimerReponseAcceptee(long questionId, long userId) { throw new NotImplementedException(); }
        public SujetForum_Class ObtenirMeilleureReponse(long questionId) { throw new NotImplementedException(); }
        public int CompterReponses(long questionId) { throw new NotImplementedException(); }
        public void ChargerQuestionsNonRepondues(ListView lv, long? categoryId = null, int limite = 20) { throw new NotImplementedException(); }
        public void ChargerQuestionsPopulaires(ListView lv, string periode = "week", int limite = 20) { throw new NotImplementedException(); }
        public void ChargerQuestionsTendance(ListView lv, int limite = 10) { throw new NotImplementedException(); }
        public void ChargerQuestionsRecentes(ListView lv, long? categoryId = null, int limite = 20) { throw new NotImplementedException(); }
        public void ChargerQuestionsAvecBounty(ListView lv, int limite = 20) { throw new NotImplementedException(); }
        public void RechercherQuestions(ListView lv, string terme, long? categoryId = null, List<string> tags = null) { throw new NotImplementedException(); }
        public void ChargerQuestionsSimilaires(ListView lv, long questionId, int limite = 5) { throw new NotImplementedException(); }
        public void ChargerQuestionsLiees(ListView lv, long questionId, int limite = 5) { throw new NotImplementedException(); }
        public void ChargerQuestionsParTags(ListView lv, List<string> tags, string operateur = "OR") { throw new NotImplementedException(); }
        public void AjouterVue(long questionId, long? userId = null, string ipAddress = null) { throw new NotImplementedException(); }
        public int ObtenirNombreVues(long questionId) { throw new NotImplementedException(); }
        public void ChargerVuesRecentes(ListView lv, long questionId, int limite = 10) { throw new NotImplementedException(); }
        public bool EstVueUnique(long questionId, long? userId, string ipAddress) { throw new NotImplementedException(); }
        public void FermerQuestion(long questionId, long userId, string raison) { throw new NotImplementedException(); }
        public void ReouvriQuestion(long questionId, long userId) { throw new NotImplementedException(); }
        public void EpinglerQuestion(long questionId, bool epingler = true) { throw new NotImplementedException(); }
        public void MarquerCommeFeatured(long questionId, bool featured = true) { throw new NotImplementedException(); }
        public void VerrouillerQuestion(long questionId, bool verrouiller = true) { throw new NotImplementedException(); }
        public void ModifierQuestion(long questionId, SujetForum_Class modifications, long editeurId) { throw new NotImplementedException(); }
        public void ModifierReponse(long reponseId, SujetForum_Class modifications, long editeurId) { throw new NotImplementedException(); }
        public void ChargerHistoriqueModifications(ListView lv, long postId) { throw new NotImplementedException(); }
        public void AnnulerModification(long postId, long revisionId) { throw new NotImplementedException(); }
        public void MettreAJourTags(long questionId, List<string> tags) { throw new NotImplementedException(); }
        public List<string> ObtenirTags(long questionId) { throw new NotImplementedException(); }
        public void ChangerCategorie(long questionId, long nouvelleCategoryId) { throw new NotImplementedException(); }
        public void ChargerQuestionsParCategorie(ListView lv, long categoryId, string filtre = "recent") { throw new NotImplementedException(); }
        public void ChargerStatistiquesUtilisateur(long userId, out int questions, out int reponses, out int vues) { throw new NotImplementedException(); }
        public void ChargerActiviteRecente(ListView lv, long userId, int limite = 10) { throw new NotImplementedException(); }
        public void ChargerTopContributeurs(ListView lv, string periode = "month", int limite = 10) { throw new NotImplementedException(); }
        public void ChargerStatistiquesGlobales(out int totalQuestions, out int totalReponses, out double tauxReponse) { throw new NotImplementedException(); }
        public void SupprimerPost(long postId, long moderateurId, string raison) { throw new NotImplementedException(); }
        public void RestaurerPost(long postId, long moderateurId) { throw new NotImplementedException(); }
        public void ChargerPostsSupprimes(ListView lv, int limite = 20) { throw new NotImplementedException(); }
        public void ChargerPostsSignales(ListView lv, int limite = 20) { throw new NotImplementedException(); }
        public void DemarrerBounty(long questionId, long userId, int montant) { throw new NotImplementedException(); }
        public void AttribuerBounty(long questionId, long reponseId, long userId) { throw new NotImplementedException(); }
        public void ChargerBountiesActifs(ListView lv) { throw new NotImplementedException(); }
        public bool PeutDemarrerBounty(long questionId, long userId) { throw new NotImplementedException(); }
        public string GenererLienPartage(long postId) { throw new NotImplementedException(); }
        public void ExporterQuestion(long questionId, string format = "html") { throw new NotImplementedException(); }
        public void ChargerQuestionsExportees(ListView lv, long userId) { throw new NotImplementedException(); }

    }
}