﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Diagnostics.Eventing.Reader;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
	
    public class PosteImp : IPoste
    {
        int n, msg;
        Post p = new Post();
        public int Ajout(Post_Class add)
        {
            using (Connection con = new Connection())
            {

                p.OrganisationId= add.OrganisationId;
                p.Titre = add.Titre;
                p.Contenu = add.Contenu;
                p.CategoriePostId = add.CategoriePostId;
                p.MembreId = add.MembreId;
                p.DatePublication = add.DatePublication;
                p.summery = add.summery;
                p.author = add.author;
                p.photo = add.photo;

                p.video = add.video;
                p.number_of_view = add.number_of_view;
                p.like = add.like;
                p.dislike = add.dislike;
                p.starttime = add.starttime;
                p.eventduration = add.eventduration;
                p.eventplace = add.eventplace;
                p.whoattend = add.whoattend;
                p.qualificationattend = add.qualificationattend;
                p.langueevent = add.langueevent;
                p.externevent = add.externevent;
                p.MOIS = add.MOIS;
                p.ANNEE = add.ANNEE;
                p.lien_isncription = add.lien_isncription;
                p.pdf = add.pdf;
                p.name = add.name;
                p.DateCreation = add.DateCreation;
                p.DateModification = add.DateModification;
                p.EstPublie = add.EstPublie;
                p.EstPublieEvent = add.EstPublieEvent;
                p.statut=add.statut;
                p.etat=add.etat;

                try
                {
                    con.Posts.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.Posts.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void AfficherDetails(long id,long idorg,int cd, Post_Class pr)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    p = con.Posts.Where(x => x.PostId == id && x.OrganisationId == idorg).FirstOrDefault();

                    if (p != null)
                    {

                        pr.PostId = p.PostId;
                        pr.Titre = p.Titre;
                        pr.Contenu = p.Contenu;
                        pr.CategoriePostId = p.CategoriePostId;
                        pr.MembreId = p.MembreId;
                        pr.DatePublication = p.DatePublication;
                        pr.summery = p.summery;
                        pr.author = p.author;
                        pr.photo = p.photo;
                        pr.video = p.video;
                        pr.number_of_view = p.number_of_view;
                        pr.like = p.like;
                        pr.dislike = p.dislike;
                        pr.starttime = p.starttime;
                        pr.eventduration = p.eventduration;
                        pr.eventplace = p.eventplace;
                        pr.whoattend = p.whoattend;
                        pr.qualificationattend = p.qualificationattend;
                        pr.langueevent = p.langueevent;
                        pr.externevent = p.externevent;
                        pr.MOIS = p.MOIS;
                        pr.ANNEE = p.ANNEE;
                        pr.lien_isncription = p.lien_isncription;
                        pr.pdf = p.pdf;
                        pr.name = p.name;
                        pr.DateCreation = p.DateCreation;
                        pr.DateModification = p.DateModification;
                        pr.EstPublie = p.EstPublie;

                        pr.EstPublieEvent = p.EstPublieEvent;
                        pr.statut = p.statut;
                        pr.etat = p.etat;
                        pr.OrganisationId = p.OrganisationId;

                    }
                }
                

            }
        }

        public void AfficherDetailsname(string name, long idorg, int cd, Post_Class pr)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    p = con.Posts.Where(x => x.name == name && x.OrganisationId==idorg).FirstOrDefault();

                    if (p != null)
                    {
                        pr.PostId = p.PostId;
                        pr.Titre = p.Titre;
                        pr.Contenu = p.Contenu;
                        pr.CategoriePostId = p.CategoriePostId;
                        pr.MembreId = p.MembreId;
                        pr.DatePublication = p.DatePublication;
                        pr.summery = p.summery;
                        pr.author = p.author;
                        pr.photo = p.photo;
                        pr.video = p.video;
                        pr.number_of_view = p.number_of_view;
                        pr.like = p.like;
                        pr.dislike = p.dislike;
                        pr.starttime = p.starttime;
                        pr.eventduration = p.eventduration;
                        pr.eventplace = p.eventplace;
                        pr.whoattend = p.whoattend;
                        pr.qualificationattend = p.qualificationattend;
                        pr.langueevent = p.langueevent;
                        pr.externevent = p.externevent;
                        pr.MOIS = p.MOIS;
                        pr.ANNEE = p.ANNEE;
                        pr.lien_isncription = p.lien_isncription;
                        pr.pdf = p.pdf;
                        pr.name = p.name;
                        pr.DateCreation = p.DateCreation;
                        pr.DateModification = p.DateModification;
                        pr.EstPublie = p.EstPublie;
                        pr.EstPublieEvent = p.EstPublieEvent;
                        pr.statut = p.statut;
                        pr.etat = p.etat;
                        pr.OrganisationId = p.OrganisationId;


                    }

                }else if (cd==1)
                {
                    p = con.Posts.Where(x => x.name == name).FirstOrDefault();

                    if (p != null)
                    {
                        pr.PostId = p.PostId;
                        pr.Titre = p.Titre;
                        pr.Contenu = p.Contenu;
                        pr.CategoriePostId = p.CategoriePostId;
                        pr.MembreId = p.MembreId;
                        pr.DatePublication = p.DatePublication;
                        pr.summery = p.summery;
                        pr.author = p.author;
                        pr.photo = p.photo;
                        pr.video = p.video;
                        pr.number_of_view = p.number_of_view;
                        pr.like = p.like;
                        pr.dislike = p.dislike;
                        pr.starttime = p.starttime;
                        pr.eventduration = p.eventduration;
                        pr.eventplace = p.eventplace;
                        pr.whoattend = p.whoattend;
                        pr.qualificationattend = p.qualificationattend;
                        pr.langueevent = p.langueevent;
                        pr.externevent = p.externevent;
                        pr.MOIS = p.MOIS;
                        pr.ANNEE = p.ANNEE;
                        pr.lien_isncription = p.lien_isncription;
                        pr.pdf = p.pdf;
                        pr.name = p.name;
                        pr.DateCreation = p.DateCreation;
                        pr.DateModification = p.DateModification;
                        pr.EstPublie = p.EstPublie;
                        pr.EstPublieEvent = p.EstPublieEvent;
                        pr.statut = p.statut;
                        pr.etat = p.etat;
                        pr.OrganisationId = p.OrganisationId;


                    }

                }

            }

        }
        public void Chargement_GDV(ListView GV_apv, long id, long idorg, string code,string publie, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var obj = (from ep in con.Posts
                               join ca in con.CategoriePosts on ep.CategoriePostId equals ca.CategoriePostId
                               join o in con.Organisations on ep.OrganisationId equals o.OrganisationId
                               join m in con.Membres on ep.MembreId equals m.MembreId
                               where ep.OrganisationId == idorg && ep.statut == code 

                               select new
                               {
                                   id = ep.PostId,
                                   Titre = ep.Titre,

                                   Contenu = ep.Contenu,
                                   CategoriePostId = ep.CategoriePostId,
                                   MembreId = ep.MembreId,
                                   DatePublication = ep.DatePublication,
                                   summery = ep.summery,
                                   author = ep.author,
                                   photo = ep.photo,

                                   video = ep.video,
                                   number_of_view = ep.number_of_view,
                                   like = ep.like,
                                   dislike = ep.dislike,
                                   starttime = ep.starttime,
                                   eventduration = ep.eventduration,
                                   eventplace = ep.eventplace,
                                   whoattend = ep.whoattend,
                                   qualificationattend = ep.qualificationattend,
                                   langueevent = ep.langueevent,
                                   externevent = ep.externevent,
                                   MOIS = ep.MOIS,
                                   ANNEE = ep.ANNEE,
                                   lien_isncription = ep.lien_isncription,
                                   pdf = ep.pdf,
                                   name = ep.name,
                                   DateCreation = ep.DateCreation,
                                   DateModification = ep.DateModification,
                                   EstPublie = ep.EstPublie,
                                   EstPublieEvent = ep.EstPublieEvent,
                                   statut = ep.statut,
                                   etat = ep.etat,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();
                }
                else if (cd == 1)
                {
                    var obj = (from ep in con.Posts
                               join ca in con.CategoriePosts on ep.CategoriePostId equals ca.CategoriePostId
                               join o in con.Organisations on ep.OrganisationId equals o.OrganisationId
                               join m in con.Membres on ep.MembreId equals m.MembreId
                               where ep.statut==code && ep.EstPublie==publie
                               orderby ep.DatePublication
                               select new
                               {
                                   id = ep.PostId,
                                   Titre = ep.Titre,

                                   Contenu = ep.Contenu,
                                   CategoriePostId = ep.CategoriePostId,
                                   MembreId = ep.MembreId,
                                   DatePublication = ep.DatePublication,
                                   summery = ep.summery,
                                   author = ep.author,
                                   photo = ep.photo,

                                   video = ep.video,
                                   number_of_view = ep.number_of_view,
                                   like = ep.like,
                                   dislike = ep.dislike,
                                   starttime = ep.starttime,
                                   eventduration = ep.eventduration,
                                   eventplace = ep.eventplace,
                                   whoattend = ep.whoattend,
                                   qualificationattend = ep.qualificationattend,
                                   langueevent = ep.langueevent,
                                   externevent = ep.externevent,
                                   MOIS = ep.MOIS,
                                   ANNEE = ep.ANNEE,
                                   lien_isncription = ep.lien_isncription,
                                   pdf = ep.pdf,
                                   name = ep.name,
                                   DateCreation = ep.DateCreation,
                                   DateModification = ep.DateModification,
                                   EstPublie = ep.EstPublie,
                                   EstPublieEvent = ep.EstPublieEvent,
                                   statut = ep.statut,
                                   etat = ep.etat,
                                   auteur=m.Nom+" "+m.Prenom,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();
                }
            }
        }

        public void Chargement_GDV(GridView GV_apv,long id, long idorg,string code,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var obj = (from ep in con.Posts
                               join ca in con.CategoriePosts on ep.CategoriePostId equals ca.CategoriePostId
                               join o in con.Organisations on ep.OrganisationId equals o.OrganisationId
                               join m in con.Membres on ep.MembreId equals m.MembreId
                               where ep.OrganisationId==idorg && ep.statut==code
                               
                               select new
                               {
                                   id = ep.PostId,
                                   Titre = ep.Titre,

                                   Contenu = ep.Contenu,
                                   CategoriePostId = ep.CategoriePostId,
                                   MembreId = ep.MembreId,
                                   DatePublication = ep.DatePublication,
                                   summery = ep.summery,
                                   author = ep.author,
                                   photo = ep.photo,

                                   video = ep.video,
                                   number_of_view = ep.number_of_view,
                                   like = ep.like,
                                   dislike = ep.dislike,
                                   starttime = ep.starttime,
                                   eventduration = ep.eventduration,
                                   eventplace = ep.eventplace,
                                   whoattend = ep.whoattend,
                                   qualificationattend = ep.qualificationattend,
                                   langueevent = ep.langueevent,
                                   externevent = ep.externevent,
                                   MOIS = ep.MOIS,
                                   ANNEE = ep.ANNEE,
                                   lien_isncription = ep.lien_isncription,
                                   pdf = ep.pdf,
                                   name = ep.name,
                                   DateCreation = ep.DateCreation,
                                   DateModification = ep.DateModification,
                                   EstPublie = ep.EstPublie,
                                   EstPublieEvent = ep.EstPublieEvent,
                                   statut = ep.statut,
                                   etat = ep.etat,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();
                }
              else if (cd==1)
                {
                    var obj = (from ep in con.Posts
                               join ca in con.CategoriePosts on ep.CategoriePostId equals ca.CategoriePostId
                               join o in con.Organisations on ep.OrganisationId equals o.OrganisationId
                               join m in con.Membres on ep.MembreId equals m.MembreId
                               where ep.OrganisationId == idorg 

                               select new
                               {
                                   id = ep.PostId,
                                   Titre = ep.Titre,

                                   Contenu = ep.Contenu,
                                   CategoriePostId = ep.CategoriePostId,
                                   MembreId = ep.MembreId,
                                   DatePublication = ep.DatePublication,
                                   summery = ep.summery,
                                   author = ep.author,
                                   photo = ep.photo,

                                   video = ep.video,
                                   number_of_view = ep.number_of_view,
                                   like = ep.like,
                                   dislike = ep.dislike,
                                   starttime = ep.starttime,
                                   eventduration = ep.eventduration,
                                   eventplace = ep.eventplace,
                                   whoattend = ep.whoattend,
                                   qualificationattend = ep.qualificationattend,
                                   langueevent = ep.langueevent,
                                   externevent = ep.externevent,
                                   MOIS = ep.MOIS,
                                   ANNEE = ep.ANNEE,
                                   lien_isncription = ep.lien_isncription,
                                   pdf = ep.pdf,
                                   name = ep.name,
                                   DateCreation = ep.DateCreation,
                                   DateModification = ep.DateModification,
                                   EstPublie = ep.EstPublie,
                                   EstPublieEvent = ep.EstPublieEvent,
                                   statut = ep.statut,
                                   etat = ep.etat,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();
                }
            }
        }

        public int count(int cd, int ct,long idorg, string publie, string code)
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var b = (from p in con.Posts

                             select p).Count();
                    n = b;
                }
                else if (cd == 1)
                {
                    var b = (from p in con.Posts

                             where p.statut == code && p.EstPublie==publie
                             select p).Count();
                    n = b;
                }
               
            }
            return n;
        }
        public void chargerPost(DropDownList ddw, long id, long idorg,string code,string intitule, int cd)
        {
            ddw.Items.Clear();
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var obj = (from ep in con.Posts
                               join ca in con.CategoriePosts on ep.CategoriePostId equals ca.CategoriePostId
                               join o in con.Organisations on ep.OrganisationId equals o.OrganisationId
                               join m in con.Membres on ep.MembreId equals m.MembreId
                               where ep.OrganisationId == idorg && ep.statut == code

                               select new
                               {
                                   PostId = ep.PostId,
                                   Titre = ep.Titre,

                               }).ToList();
                    if (obj != null && obj.Count() > 0)
                    {
                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = intitule;
                        ddw.Items.Add(item0);

                        foreach (var data in obj)
                        {
                            ListItem item = new ListItem();
                            item.Value = data.PostId.ToString();
                            item.Text = data.Titre;
                            ddw.Items.Add(item);
                        }

                    }
                    else
                    {
                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = "Aucune donnée";
                        ddw.Items.Add(item0);
                    }
                }
               

            }
        }

        public int count()
        {
            using (Connection con = new Connection())
            {
                var b = (from l in con.Posts
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(Post_Class add, long id,long idorg)
        {
            using (Connection con = new Connection())
            {
                p = con.Posts.Where(x => x.PostId == id && x.OrganisationId==idorg).FirstOrDefault();

                try
                {
                    p.Titre = add.Titre;
                    p.Contenu = add.Contenu;
                    p.CategoriePostId = add.CategoriePostId;
                  
                    p.DatePublication = add.DatePublication;
                    p.summery = add.summery;
                    p.author = add.author;
                    p.photo = add.photo;

                    p.video = add.video;
                   
                    p.starttime = add.starttime;
                    p.eventduration = add.eventduration;
                    p.eventplace = add.eventplace;
                    p.whoattend = add.whoattend;
                 
                    p.langueevent = add.langueevent;
                    p.externevent = add.externevent;
                    p.MOIS = add.MOIS;
                    p.ANNEE = add.ANNEE;
                    p.lien_isncription = add.lien_isncription;
                    p.pdf = add.pdf;
                    p.name = add.name;
                  
                    p.DateModification = add.DateModification;
                    p.EstPublie = add.EstPublie;
                    p.EstPublieEvent = add.EstPublieEvent;
                    p.statut = add.statut;
                    p.etat = add.etat;

                   


                    if (con.SaveChanges() == 1)
                    {
                        con.Posts.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }

        public int MiseajourData(Post_Class add, long id, long idorg,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    p = con.Posts.Where(x => x.PostId == id).FirstOrDefault();

                    try
                    {
                        p.number_of_view = add.number_of_view;
                       
                        if (con.SaveChanges() == 1)
                        {
                            con.Posts.Add(p);
                            con.Entry(p).State = EntityState.Modified;
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch (Exception e)
                    {

                    }

                }
                
                return msg;
            }
        }

        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.Posts

                           select new
                           {
                               id = ep.PostId,
                               Titre = ep.Titre,

                               Contenu = ep.Contenu,
                               CategoriePostId = ep.CategoriePostId,
                               MembreId = ep.MembreId,
                               DatePublication = ep.DatePublication,
                               summery = ep.summery,
                               author = ep.author,
                               photo = ep.photo,

                               video = ep.video,
                               number_of_view = ep.number_of_view,
                               like = ep.like,
                               dislike = ep.dislike,
                               starttime = ep.starttime,
                               eventduration = ep.eventduration,
                               eventplace = ep.eventplace,
                               whoattend = ep.whoattend,
                               qualificationattend = ep.qualificationattend,
                               langueevent = ep.langueevent,
                               externevent = ep.externevent,
                               MOIS = ep.MOIS,
                               ANNEE = ep.ANNEE,
                               lien_isncription = ep.lien_isncription,
                               pdf = ep.pdf,
                               name = ep.name,
                               DateCreation = ep.DateCreation,
                               DateModification = ep.DateModification,
                               EstPublie = ep.EstPublie,
                               EstPublieEvent = ep.EstPublieEvent,
                               statut = ep.statut,
                               etat = ep.etat,
                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public int supprimer(long id,long idorg)
        {
            using (Connection con = new Connection())
            {

                p = con.Posts.Where(x => x.PostId == id && x.OrganisationId==idorg).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.Posts.Attach(p);

                con.Posts.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }
    }
}