﻿using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listdomaineintervention : System.Web.UI.Page
    {

        DomaineIntervention_Class cat = new DomaineIntervention_Class();
        IDomaineIntervention obj = new DomaineInterventionImp();
        int info;
        static string typenm; static int id, idpers, rol;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {

                getDataGDV();

            }
        }
        public void getDataGDV()
        {
            obj.ChargerDomaines(gdv);
            //  nbr.Text = obj.count().ToString();

        }
        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view")
            {
                Response.Redirect("~/file/domaineintervention.aspx?name=" + index);

            }
            if (e.CommandName == "delete")
            {
                try
                {
                    obj.AfficherDetails(index, cat, 0);
                    info = obj.Supprimer(Convert.ToInt32(cat.DomaineInterventionId));
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listdomaineintervention.aspx");
                    }
                    else
                    {
                        //  msg.Text = "Modification echoue";
                        //msg.Text = id.ToString();
                    }


                }
                catch (SqlException ex)
                {
                    // msg.Text = "Cette Province existe deja";
                }
            }
        }

        protected void btn_srch_Click(object sender, EventArgs e)
        {
            //if (txt_srch.Value == "")
            //    getDataGDV();
            //else obj.search(gdv, txt_srch.Value);
        }
    }
}