﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IProgrammeMentorat
    {
        void AfficherDetails(int programmeMentoratId, ProgrammeMentorat_Class programme);
        int Ajouter(ProgrammeMentorat_Class programme);
        int Modifier(ProgrammeMentorat_Class programme);
        int Supprimer(int programmeMentoratId);
        void ChargerProgrammes(GridView gdv, string status = "");
        void chargerProgrammeMentorat(DropDownList lst);
        void ChargerProgrammesParMembre(GridView gdv, long membreId);

        // Nouvelles méthodes pour récupération depuis Post
        void chargerProgrammeMentoratFromPost(DropDownList lst, long idorg);
        void ChargerProgrammesFromPost(GridView gdv, long idorg, string status = "programmementorat");
        void ChargerProgrammesPublicsFromPost(ListView lv, string status = "programmementorat");
        int countFromPost(long idorg, string status = "programmementorat");
    }
}
