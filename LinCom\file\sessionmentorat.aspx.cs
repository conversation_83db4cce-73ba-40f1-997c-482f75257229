﻿using LinCom.Class;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class sessionmentorat : System.Web.UI.Page
    {
        SessionMentorat_Class sessionObj = new SessionMentorat_Class();
        ISessionMentorat objSession = new SessionMentoratImp();
        CommonCode co = new CommonCode();

        IProgrammeMentorat objProgramme = new ProgrammeMentoratImp();
        IMentor objMentor = new MentorImp();
        IMentore objMentore = new MentoreImp();

        string nsco;
        int sessionId;
        long ide;
        int rolid;

        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            if (!string.IsNullOrEmpty(nsco))
            {
                sessionId = Convert.ToInt32(nsco);
            }

            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {
                    ide = Convert.ToInt64(idperso.Value);
                    rolid = Convert.ToInt32(role.Value);
                }
                else
                {
                    Response.Redirect("~/login.aspx");
                }

                InitialiserMessages();
                ChargerDropDownLists();

                // Si modification, charger les données
                if (sessionId > 0)
                {
                    ChargerDonneesSession();
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_success.Visible = false;
            div_msg_error.Visible = false;
        }

        private void ChargerDropDownLists()
        {
            try
            {
                // Récupérer l'organisation de l'utilisateur
                IOrganisation objOrganisation = new OrganisationImp();
                Organisation_Class org = new Organisation_Class();
                MembresOrganisation_Class memorg = new MembresOrganisation_Class();
                IMembresOrganisation objmemorg = new MembresOrganisationImp();

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                long idorg = Convert.ToInt64(memorg.OrganisationId);

                // Charger les programmes de mentorat depuis la table Post
                objProgramme.chargerProgrammeMentoratFromPost(drpdProgramme, idorg);

                // Charger les mentors
                objMentor.chargerMentors(drpdMentor);

                // Charger les mentorés
                objMentore.chargerMentore(drpdMentore);
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors du chargement des données : " + ex.Message;
            }
        }

        private void ChargerDonneesSession()
        {
            try
            {
                objSession.AfficherDetails(sessionId, sessionObj);

                drpdProgramme.SelectedValue = sessionObj.ProgrammeMentoratId.ToString();
                drpdMentor.SelectedValue = sessionObj.MentorId.ToString();
                drpdMentore.SelectedValue = sessionObj.MentoreId.ToString();
                txtDateSession.Value = sessionObj.DateSession?.ToString("yyyy-MM-ddTHH:mm");
                txtSujet.Value = sessionObj.Sujet;
                txtNotes.Value = sessionObj.Notes;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors du chargement des données : " + ex.Message;
            }
        }

        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            try
            {
                if (ValiderFormulaire())
                {
                    if (sessionId > 0)
                    {
                        ModifierSession();
                    }
                    else
                    {
                        AjouterSession();
                    }
                }
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de l'enregistrement : " + ex.Message;
            }
        }

        private bool ValiderFormulaire()
        {
            if (drpdProgramme.SelectedValue == "-1" ||
                drpdMentor.SelectedValue == "-1" ||
                drpdMentore.SelectedValue == "-1" ||
                string.IsNullOrEmpty(txtDateSession.Value) ||
                string.IsNullOrEmpty(txtSujet.Value))
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                return false;
            }
            return true;
        }

        private void AjouterSession()
        {
            sessionObj.ProgrammeMentoratId = Convert.ToInt32(drpdProgramme.SelectedValue);
            sessionObj.MentorId = Convert.ToInt64(drpdMentor.SelectedValue);
            sessionObj.MentoreId = Convert.ToInt64(drpdMentore.SelectedValue);
            sessionObj.DateSession = Convert.ToDateTime(txtDateSession.Value);
            sessionObj.Sujet = txtSujet.Value;
            sessionObj.Notes = txtNotes.Value;

            int resultat = objSession.Ajouter(sessionObj);

            if (resultat == 1)
            {
                div_msg_success.Visible = true;
                msg_success.InnerText = "Session planifiée avec succès";
                ViderFormulaire();
            }
            else
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de la planification de la session";
            }
        }

        private void ModifierSession()
        {
            sessionObj.ProgrammeMentoratId = Convert.ToInt32(drpdProgramme.SelectedValue);
            sessionObj.MentorId = Convert.ToInt64(drpdMentor.SelectedValue);
            sessionObj.MentoreId = Convert.ToInt64(drpdMentore.SelectedValue);
            sessionObj.DateSession = Convert.ToDateTime(txtDateSession.Value);
            sessionObj.Sujet = txtSujet.Value;
            sessionObj.Notes = txtNotes.Value;

            int resultat = objSession.Modifier(sessionObj);

            if (resultat == 1)
            {
                div_msg_success.Visible = true;
                msg_success.InnerText = "Session modifiée avec succès";
                Response.Redirect("listsessionmentorat.aspx");
            }
            else
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de la modification de la session";
            }
        }

        private void ViderFormulaire()
        {
            drpdProgramme.SelectedValue = "-1";
            drpdMentor.SelectedValue = "-1";
            drpdMentore.SelectedValue = "-1";
            txtDateSession.Value = "";
            txtSujet.Value = "";
            txtNotes.Value = "";
        }
    }
}