﻿<%@ Page Title="Activités des Projets" Language="C#"  ValidateRequest="false" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="activiteprojet.aspx.cs" Inherits="LinCom.file.activiteprojet" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">

                                    <div class="breadcomb-ctn">
                                        <h2>Activités des Projets</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">

                                    <a data-toggle="tooltip" data-placement="left" href="listactiviteprojet.aspx" title="Clique sur ce button pour visualiser la liste des Activités des Projets" class="btn">Liste des Activités des Projets</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->
    <!-- Form Element area Start-->
    <div class="form-element-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="cmp-tb-hd bcs-hd">
                            <p style="color: red">Remplissez les champs obligatoires avec asterix</p>
                        </div>
                        <div class="alert-list">
                            <div class="alert alert-success alert-dismissible" role="alert" runat="server" id="div_msg_succes">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <span runat="server" id="msg_succes">Enregistrement réussi</span>
                            </div>
                            <div class="alert alert-danger alert-dismissible" role="alert" runat="server" id="div_msg_error">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <span runat="server" id="msg_error">Enregistrement échoué</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int form-elet-mg">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Projet *</label>
                                        <asp:DropDownList class="form-control" ID="drpdprojet" runat="server">

                                            <asp:ListItem Value="-1">Selectionner le projet</asp:ListItem>

                                        </asp:DropDownList>
                                    </div>

                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int form-elet-mg">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Titre de l'Activité  *</label>
                                        <input type="text" runat="server" id="txtIntitule" class="form-control" placeholder="Intitulé de l'Activité  *">
                                    </div>

                                </div>
                            </div>


                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int form-elet-mg">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Date de l'Activité  *</label>
                                        <input type="date" runat="server" id="txtDate" class="form-control" placeholder="Date de l'Activité  *">
                                    </div>

                                </div>
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Statut de l'Activité  *</label>
                                        <asp:DropDownList class="form-control" ID="drpdstatut" runat="server">

                                            <asp:ListItem Value="-1">Selectionner le statut</asp:ListItem>
                                            <asp:ListItem Value="en attente">En attente (le post non finalisé)</asp:ListItem>
                                            <asp:ListItem Value="publié">Publié</asp:ListItem>
                                            <asp:ListItem Value="Non publié">Non publié</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Etat de l'Activité  *</label>
                                        <asp:DropDownList class="form-control" ID="drpdetat" runat="server">

                                            <asp:ListItem Value="-1">Selectionner l'etat du Projet</asp:ListItem>
                                            <asp:ListItem Value="en cours">En cours</asp:ListItem>
                                            <asp:ListItem Value="a venir">A venir</asp:ListItem>
                                            <asp:ListItem Value="cloturé">Cloturé</asp:ListItem>

                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-picture"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Photo 1 de couverture </label>
                                        <asp:FileUpload ID="fileupd" runat="server" class="form-control" />

                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-picture"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Photo 2 de couverture </label>
                                        <asp:FileUpload ID="fileupd1" runat="server" class="form-control" />

                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-form"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Description de l'Activité  </label>
                                        <div >

                                            <textarea class="html-editor" runat="server" id="txtdescription" rows="10"></textarea>

                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="row">

                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <button type="button" runat="server" id="btnEnregistrer" onserverclick="btnEnregistrer_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Form Element area End-->
</asp:Content>
