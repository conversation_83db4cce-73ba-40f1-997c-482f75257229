﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class SessionMentoratImp : ISessionMentorat
    {
        int msg;
        private SessionMentorat session = new SessionMentorat();

        public void AfficherDetails(int idSession, SessionMentorat_Class sessionClass)
        {
            using (Connection con = new Connection())
            {
                var s = con.SessionMentorats.FirstOrDefault(x => x.SessionMentoratId == idSession);
                if (s != null)
                {
                    sessionClass.SessionMentoratId = s.SessionMentoratId;
                    sessionClass.ProgrammeMentoratId = s.ProgrammeMentoratId;
                    sessionClass.MentorId = s.MentorId;
                    sessionClass.MentorId = s.MentoreId;
                    sessionClass.DateSession = s.DateSession;
                    sessionClass.Sujet = s.Sujet;
                    sessionClass.Notes = s.Notes;
                }
            }
        }

        public int Ajouter(SessionMentorat_Class sessionClass)
        {
            using (Connection con = new Connection())
            {
                session.ProgrammeMentoratId = sessionClass.ProgrammeMentoratId;
                session.MentoreId = (int?)sessionClass.MentoreId;
                session.MentorId = (int?)sessionClass.MentorId;
                session.DateSession = sessionClass.DateSession;
                session.Sujet = sessionClass.Sujet;
                session.Notes = sessionClass.Notes;


                try
                {
                    con.SessionMentorats.Add(session);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerSessions(GridView gdv, int? idMentor = null, int? idMentoree = null)
        {
            using (Connection con = new Connection())
            {
                var query = from s in con.SessionMentorats
                            //join mentor in con.Membres on s.MentorId equals mentor.MembreId
                            //join mentoree in con.Membres on s.MentoreId equals mentoree.MembreId
                          //  join prog in con.Programmementorats on s.ProgrammeMentoratId equals prog.ProgrammeMentoratId
                            select new
                            {
                                s.SessionMentoratId,
                               // Programme = prog.name,
                               // Mentor = mentor.Nom + " " + mentor.Prenom,
                              //  Mentoree = mentoree.Nom + " " + mentoree.Prenom,
                                s.DateSession,

                            };

                if (idMentor.HasValue)
                {
                    query = query.Where(x => x.SessionMentoratId == idMentor.Value);
                }

                if (idMentoree.HasValue)
                {
                    query = query.Where(x => x.SessionMentoratId == idMentoree.Value);
                }

                gdv.DataSource = query.OrderByDescending(x => x.DateSession).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerSessionsParProgramme(GridView gdv, int idProgramme)
        {
            using (Connection con = new Connection())
            {
                var query = from s in con.SessionMentorats
                            //join mentor in con.Membres on s.MentorId equals mentor.MembreId
                           // join mentoree in con.Membres on s.MentoreId equals mentoree.MembreId
                            where s.ProgrammeMentoratId == idProgramme
                            select new
                            {
                                s.SessionMentoratId,
                              //  Mentor = mentor.Nom + " " + mentor.Prenom,
                              //  Mentoree = mentoree.Nom + " " + mentoree.Prenom,
                                s.DateSession,

                            };

                gdv.DataSource = query.OrderByDescending(x => x.DateSession).ToList();
                gdv.DataBind();
            }
        }



        public void chargerSessionMentorat(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.SessionMentorats select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner la session";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.SessionMentoratId.ToString();
                        item.Text = data.Sujet;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }


        public int Modifier(SessionMentorat_Class sessionClass)
        {
            using (Connection con = new Connection())
            {
                var s = con.SessionMentorats.FirstOrDefault(x => x.SessionMentoratId == sessionClass.SessionMentoratId);
                if (s != null)
                {
                    s.DateSession = sessionClass.DateSession;
                    s.Sujet = sessionClass.Sujet;
                    s.ProgrammeMentoratId = sessionClass.ProgrammeMentoratId;
                    s.Notes = sessionClass.Notes;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(int idSession)
        {
            using (Connection con = new Connection())
            {
                var s = con.SessionMentorats.FirstOrDefault(x => x.SessionMentoratId == idSession);
                if (s != null)
                {

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}