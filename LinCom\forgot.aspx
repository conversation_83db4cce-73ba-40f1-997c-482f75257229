﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="forgot.aspx.cs" Inherits="LinCom.forgot" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
      <!-- Page Title -->
  <div class="page-title">
      <div class="heading">
          <div class="container">
              <div class="row d-flex justify-content-center text-center">
                  <div class="col-lg-8">
                      <h2>Reinitialisation du Mot de Passe</h2>
                      <p class="mb-0">Odio et unde deleniti. Deserunt numquam exercitationem. Officiis quo odio sint voluptas consequatur ut a odio voluptatem. Sit dolorum debitis veritatis natus dolores. Quasi ratione sint. Sit quaerat ipsum dolorem.</p>
                  </div>
              </div>
          </div>
      </div>
      <nav class="breadcrumbs">
          <div class="container">
              <ol>
                  <li><a href="home.aspx">Home</a></li>
                  <li><a href="login.aspx">Connection</a></li>
                  <li class="current"><a href="forgot.aspx">Reinitialiser le mot de passe</a></li>
              </ol>
          </div>
      </nav>
  </div>
  <!-- End Page Title -->
  <main class="main">
      <!-- Contact Section -->
      <section id="contact" class="contact section">

          <!-- Section Title -->
          <div class="container section-title" data-aos="fade-up">
              <h2>Reinitialiser votre mot de passe</h2>
          </div>
          <!-- End Section Title -->

          <div class="container" data-aos="fade-up" data-aos-delay="100">

              <div class="row gx-lg-0 gy-4">

                  <div class="col-lg-2">
                      <div class="info-container d-flex flex-column align-items-center justify-content-center">
                      </div>

                  </div>

                  <div class="col-lg-8">
                      <div class="alert-list">
                          <div class="alert alert-success alert-dismissible" role="alert" runat="server" id="div_msg_succes">
                              <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                              <span runat="server" id="msg_succes">Enregistrement reussi</span>
                          </div>
                          <div class="alert alert-danger alert-dismissible" role="alert" runat="server" id="div_msg_error">
                              <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                              <span runat="server" id="msg_error">Enregistrement echoué</span>
                         
                          </div>

                      </div>
                      <div class="php-email-form" data-aos="fade" data-aos-delay="100">
                          <div class="row gy-4">

                              <div class="col-md-6 ">
                                  <input type="email" runat="server" id="txt_usernm" name="name" class="form-control" placeholder="Entez votre email" required="">
                                  
                              </div>
                              

                              <div class="col-md-12 text-center">


                                  <div class="loading">Loading</div>
                                  <div class="error-message"></div>
                                  <div class="sent-message">Your message has been sent. Thank you!</div>

                                  <button type="submit" runat="server" id="btnreng" onserverclick="btnreng_ServerClick">Envoyer le code</button>
                               <a href="login.aspx"><b><u>Se Connecter</u></b></a>
                                  </div>

                          </div>
                      </div>
                  </div>

                  <div class="col-lg-2">
                      <div class="info-container d-flex flex-column align-items-center justify-content-center">
                      </div>

                  </div>


                  <!-- End Contact Form -->

              </div>

          </div>

      </section>
      <!-- /Contact Section -->

  </main>

</asp:Content>
