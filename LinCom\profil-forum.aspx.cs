using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class profil_forum : System.Web.UI.Page
    {
        private SujetForumImp sujetImp = new SujetForumImp();
        private ReplyForumImp replyImp = new ReplyForumImp();
        private BadgesImp badgesImp = new BadgesImp();
        private ReputationHistoryImp reputationImp = new ReputationHistoryImp();
        private TagsImp tagsImp = new TagsImp();

        private long? ProfileUserId;
        private long? CurrentUserId;

        protected void Page_Load(object sender, EventArgs e)
        {
            // Récupérer l'ID de l'utilisateur du profil
            if (!long.TryParse(Request.QueryString["userId"], out long userId))
            {
                // Si pas d'ID spécifié, utiliser l'utilisateur connecté
                if (Session["MembreId"] == null)
                {
                    Response.Redirect("login.aspx");
                    return;
                }
                ProfileUserId = Convert.ToInt64(Session["MembreId"]);
            }
            else
            {
                ProfileUserId = userId;
            }

            // Récupérer l'utilisateur connecté
            if (Session["MembreId"] != null)
            {
                CurrentUserId = Convert.ToInt64(Session["MembreId"]);
            }

            if (!IsPostBack)
            {
                LoadUserProfile();
                LoadUserStatistics();
                LoadUserQuestions();
                LoadUserAnswers();
                LoadUserActivity();
                LoadReputationHistory();
                LoadUserBadges();
                LoadUserPrivileges();
                LoadUserTags();
            }
        }

        private void LoadUserProfile()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var user = con.Membres.Where(m => m.MembreId == ProfileUserId.Value).FirstOrDefault();
                    if (user != null)
                    {
                        lblUserName.Text = $"{user.Nom} {user.Prenom}";
                        lblUserInitials.Text = GetUserInitials($"{user.Nom} {user.Prenom}");
                        lblMemberSince.Text = user.DateInscription?.ToString("MMMM yyyy") ?? "Date inconnue";
                        lblReputation.Text = user.Reputation?.ToString() ?? "0";
                        
                        // Déterminer le niveau utilisateur
                        string userLevel = GetUserLevel(user.Reputation ?? 0);
                        lblUserLevel.Text = userLevel;
                        lblNextLevel.Text = GetNextLevelInfo(user.Reputation ?? 0);
                    }
                    else
                    {
                        Response.Redirect("forum-questions.aspx");
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement du profil : " + ex.Message);
            }
        }

        private void LoadUserStatistics()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    // Questions posées
                    int questionCount = con.SujetForums.Count(s => s.PostTypeId == 1 && s.OwnerUserId == ProfileUserId.Value);
                    lblQuestionCount.Text = questionCount.ToString();

                    // Réponses données
                    int answerCount = con.SujetForums.Count(s => s.PostTypeId == 2 && s.OwnerUserId == ProfileUserId.Value);
                    lblAnswerCount.Text = answerCount.ToString();

                    // Réponses acceptées
                    int acceptedAnswers = con.SujetForums.Count(s => s.PostTypeId == 2 && s.OwnerUserId == ProfileUserId.Value && s.AcceptedAnswerId.HasValue);
                    lblAcceptedAnswers.Text = acceptedAnswers.ToString();

                    // Vues totales
                    int totalViews = con.SujetForums.Where(s => s.PostTypeId == 1 && s.OwnerUserId == ProfileUserId.Value)
                                                   .Sum(s => (int?)s.ViewCount) ?? 0;
                    lblTotalViews.Text = totalViews.ToString();

                    // Badges obtenus
                    int badgeCount = con.Badges.Count(b => b.UserId == ProfileUserId.Value);
                    lblBadgeCount.Text = badgeCount.ToString();

                    // Votes donnés
                    int votesGiven = con.Votes.Count(v => v.UserId == ProfileUserId.Value);
                    lblVotesGiven.Text = votesGiven.ToString();
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des statistiques : " + ex.Message);
            }
        }

        private void LoadUserQuestions()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var questions = con.SujetForums.Where(s => s.PostTypeId == 1 && s.OwnerUserId == ProfileUserId.Value)
                                                  .OrderByDescending(s => s.CreationDate)
                                                  .Take(10)
                                                  .Select(s => new
                                                  {
                                                      s.SujetForumId,
                                                      s.Title,
                                                      s.Score,
                                                      s.AnswerCount,
                                                      s.ViewCount,
                                                      s.CreationDate
                                                  }).ToList();

                    lvUserQuestions.DataSource = questions;
                    lvUserQuestions.DataBind();
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des questions : " + ex.Message);
            }
        }

        private void LoadUserAnswers()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var answers = (from a in con.SujetForums
                                  join q in con.SujetForums on a.ParentId equals q.SujetForumId
                                  where a.PostTypeId == 2 && a.OwnerUserId == ProfileUserId.Value
                                  orderby a.CreationDate descending
                                  select new
                                  {
                                      QuestionId = q.SujetForumId,
                                      QuestionTitle = q.Title,
                                      Score = a.Score,
                                      IsAccepted = q.AcceptedAnswerId == a.SujetForumId,
                                      CreationDate = a.CreationDate
                                  }).Take(10).ToList();

                    lvUserAnswers.DataSource = answers;
                    lvUserAnswers.DataBind();
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des réponses : " + ex.Message);
            }
        }

        private void LoadUserActivity()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var activities = new List<object>();

                    // Questions récentes
                    var questions = con.SujetForums.Where(s => s.PostTypeId == 1 && s.OwnerUserId == ProfileUserId.Value)
                                                  .OrderByDescending(s => s.CreationDate)
                                                  .Take(5)
                                                  .Select(s => new
                                                  {
                                                      ActionType = "question",
                                                      ActionDescription = "A posé une question",
                                                      Details = s.Title,
                                                      ActionDate = s.CreationDate
                                                  }).ToList();

                    // Réponses récentes
                    var answers = (from a in con.SujetForums
                                  join q in con.SujetForums on a.ParentId equals q.SujetForumId
                                  where a.PostTypeId == 2 && a.OwnerUserId == ProfileUserId.Value
                                  orderby a.CreationDate descending
                                  select new
                                  {
                                      ActionType = "answer",
                                      ActionDescription = "A répondu à une question",
                                      Details = q.Title,
                                      ActionDate = a.CreationDate
                                  }).Take(5).ToList();

                    // Combiner et trier
                    activities.AddRange(questions);
                    activities.AddRange(answers);

                    var sortedActivities = activities.OrderByDescending(a => ((dynamic)a).ActionDate).Take(10).ToList();

                    lvUserActivity.DataSource = sortedActivities;
                    lvUserActivity.DataBind();
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement de l'activité : " + ex.Message);
            }
        }

        private void LoadReputationHistory()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var reputationHistory = con.ReputationHistories.Where(r => r.UserId == ProfileUserId.Value)
                                                                   .OrderByDescending(r => r.CreationDate)
                                                                   .Take(20)
                                                                   .Select(r => new
                                                                   {
                                                                       r.ReputationChange,
                                                                       r.Description,
                                                                       r.CreationDate
                                                                   }).ToList();

                    lvReputationHistory.DataSource = reputationHistory;
                    lvReputationHistory.DataBind();
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement de l'historique de réputation : " + ex.Message);
            }
        }

        private void LoadUserBadges()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var badges = con.Badges.Where(b => b.UserId == ProfileUserId.Value)
                                          .OrderByDescending(b => b.Date)
                                          .Select(b => new
                                          {
                                              b.Name,
                                              b.Description,
                                              b.Class,
                                              b.Date
                                          }).ToList();

                    lvUserBadges.DataSource = badges;
                    lvUserBadges.DataBind();
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des badges : " + ex.Message);
            }
        }

        private void LoadUserPrivileges()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var user = con.Membres.Where(m => m.MembreId == ProfileUserId.Value).FirstOrDefault();
                    if (user != null)
                    {
                        var privileges = GetUserPrivileges(user.Reputation ?? 0);
                        lvUserPrivileges.DataSource = privileges;
                        lvUserPrivileges.DataBind();
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des privilèges : " + ex.Message);
            }
        }

        private void LoadUserTags()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var userTags = (from pt in con.PostTags
                                   join t in con.Tags on pt.TagId equals t.TagId
                                   join s in con.SujetForums on pt.PostId equals s.SujetForumId
                                   where s.OwnerUserId == ProfileUserId.Value
                                   group t by new { t.TagId, t.TagName } into g
                                   orderby g.Count() descending
                                   select new
                                   {
                                       TagName = g.Key.TagName,
                                       Count = g.Count()
                                   }).Take(10).ToList();

                    lvUserTags.DataSource = userTags;
                    lvUserTags.DataBind();
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des tags : " + ex.Message);
            }
        }

        // Méthodes utilitaires
        protected string GetActivityType(string activityType)
        {
            switch (activityType.ToLower())
            {
                case "question":
                    return "question";
                case "answer":
                    return "answer";
                case "vote":
                    return "vote";
                case "badge":
                    return "badge";
                default:
                    return "default";
            }
        }

        protected string GetActivityIcon(string activityType)
        {
            switch (activityType.ToLower())
            {
                case "question":
                    return "fas fa-question-circle";
                case "answer":
                    return "fas fa-comment";
                case "vote":
                    return "fas fa-thumbs-up";
                case "badge":
                    return "fas fa-medal";
                default:
                    return "fas fa-info-circle";
            }
        }

        protected string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "à l'instant";
            if (timeSpan.TotalMinutes < 60)
                return $"il y a {(int)timeSpan.TotalMinutes} min";
            if (timeSpan.TotalHours < 24)
                return $"il y a {(int)timeSpan.TotalHours}h";
            if (timeSpan.TotalDays < 30)
                return $"il y a {(int)timeSpan.TotalDays} jours";
            
            return dateTime.ToString("dd MMM yyyy");
        }

        protected string GetUserInitials(string userName)
        {
            if (string.IsNullOrEmpty(userName))
                return "?";

            var parts = userName.Split(' ');
            if (parts.Length >= 2)
                return (parts[0][0].ToString() + parts[1][0].ToString()).ToUpper();
            
            return userName[0].ToString().ToUpper();
        }

        protected string GetBadgeClass(int badgeClass)
        {
            switch (badgeClass)
            {
                case 1: return "badge-gold";
                case 2: return "badge-silver";
                case 3: return "badge-bronze";
                default: return "badge-bronze";
            }
        }

        private string GetUserLevel(int reputation)
        {
            if (reputation >= 10000) return "Expert";
            if (reputation >= 3000) return "Avancé";
            if (reputation >= 1000) return "Intermédiaire";
            if (reputation >= 100) return "Contributeur";
            return "Débutant";
        }

        private string GetNextLevelInfo(int reputation)
        {
            if (reputation < 100) return $"{100 - reputation} pts pour Contributeur";
            if (reputation < 1000) return $"{1000 - reputation} pts pour Intermédiaire";
            if (reputation < 3000) return $"{3000 - reputation} pts pour Avancé";
            if (reputation < 10000) return $"{10000 - reputation} pts pour Expert";
            return "Niveau maximum atteint";
        }

        private List<object> GetUserPrivileges(int reputation)
        {
            var privileges = new List<object>();

            if (reputation >= 15)
                privileges.Add(new { PrivilegeName = "Voter", RequiredReputation = 15 });
            if (reputation >= 50)
                privileges.Add(new { PrivilegeName = "Commenter", RequiredReputation = 50 });
            if (reputation >= 125)
                privileges.Add(new { PrivilegeName = "Voter négativement", RequiredReputation = 125 });
            if (reputation >= 2000)
                privileges.Add(new { PrivilegeName = "Éditer les posts", RequiredReputation = 2000 });
            if (reputation >= 3000)
                privileges.Add(new { PrivilegeName = "Fermer les questions", RequiredReputation = 3000 });
            if (reputation >= 10000)
                privileges.Add(new { PrivilegeName = "Supprimer les posts", RequiredReputation = 10000 });

            return privileges;
        }

        private void ShowError(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "error", 
                $"toastr.error('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }
    }
}
