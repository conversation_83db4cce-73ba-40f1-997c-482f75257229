﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ICommune
    {
        int add(CommuneClass add);
        void Chargement_GDV(GridView GV_apv);
        void search(GridView GV_apv, string code);
        void afficherDetails(int code, CommuneClass pr);
        void afficherDetails(string code, CommuneClass pr);
        int edit(CommuneClass cl, int id);
        int edit(CommuneClass cl, string code);
        int supprimer(int id);

        void chargerCommune(DropDownList lst);
        void chargerCommune(DropDownList lst,int pro);
        int count();
    }
}
