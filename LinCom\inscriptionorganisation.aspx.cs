﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.file;
using LinCom.Imp;
using LinCom.Model;
using Microsoft.Ajax.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Web;
using System.Web.Script.Serialization;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class inscriptionorganisation : System.Web.UI.Page
    {
        private int info;
        string nscno; static string pht;
        TypeOrganisation_Class cat = new TypeOrganisation_Class();
        ITypeOrganisation objcat = new TypeOrganisationImp();
        Organisation_Class co = new Organisation_Class();
        Organisation_Class org = new Organisation_Class();
        IOrganisation obj = new OrganisationImp();
        Province_Class prov = new Province_Class();
        IProvince objp = new ProvinceImp();
        CommuneClass znc = new CommuneClass();
        ICommune objco = new CommuneImp();
        ICommonCode com=new CommonCode();
        DomaineInterventionOrganisation_Class actco = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objactco = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class domai = new DomaineIntervention_Class();
        IDomaineIntervention objdom=new DomaineInterventionImp();
        Membre_Class mem = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        IMembre objmem=new MembreImp();
        MembresOrganisation_Class memorg=new MembresOrganisation_Class();
        MembresOrganisation_Class memorga=new MembresOrganisation_Class();
        IMembresOrganisation objmemorg=new MembresOrganisationImp();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        DataTable dat = new DataTable();
        long index;
        static string imge;
        protected void Page_Load(object sender, EventArgs e)
        {
            nscno = Request.QueryString["IDCOOP"];
            if (!IsPostBack)
            {
                afficher();
                objp.chargerProvince(drpdprov);
                objcat.ChargerTypesDisponibles(drpdtype);
                objdom.ChargerDomainedisponible(drpddomai);

                if (ViewState["Record"] == null)
                {//depense
                    dat.Columns.Add("#No");
                    dat.Columns.Add("Domaine d'Intervention");

                    ViewState["Record"] = dat;
                }
            }
        }
        void vider()
        {
            txtphone.Value = "";
            txtemail.Value = "";
            txtnm.Value = "";
            drpdcom.SelectedValue = "-1";
            txt_adress.Value = "";
            drpdprov.SelectedValue = "-1";
            drpdtype.SelectedValue = "-1";
            txtConfirmEmail.Value = "";
            txtdatecreation.Value = "";
            drpddomai.SelectedValue = "-1";
            dom.Visible = false;
            chkConditions.Checked = false;
            txtsigle.Value = "";

        }

        public void AjoutOrganisation(int cd)
        {
            try
            {
                // Vérification des conditions
                if (!chkConditions.Checked)
                {
                    ShowAlert("Vous devez accepter les politiques et conditions pour continuer.");
                    return;
                }

                // Vérification du CAPTCHA
                if (!IsCaptchaValid())
                {
                    ShowAlert("Erreur CAPTCHA. Veuillez cocher la case Je ne suis pas un robot.");
                    return;
                }

              
             
                // Validation des champs requis
                if (
                    drpdtype.SelectedValue == "-1" ||
                    drpdexiste.SelectedValue == "-1" ||
                    string.IsNullOrWhiteSpace(txtnm.Value) ||
                    string.IsNullOrWhiteSpace(txtphone.Value) ||
                    drpdprov.SelectedValue == "-1" ||
                    drpdcom.SelectedValue == "-1" ||
                    string.IsNullOrWhiteSpace(txtemail.Value) ||
                    string.IsNullOrWhiteSpace(txtConfirmEmail.Value))
                {
                    ShowAlert("Renseignez au moins les champs avec *.");
                    return;
                }
                obj.AfficherDetails(com.GenerateSlug(txtnm.Value), org);
                if (org.name == com.GenerateSlug(txtnm.Value))
                {
                    ShowAlert("Votre organisation est deja enregistree.");
                    return;
                }
                // Vérification de l'égalité des emails
                if (txtemail.Value.Trim() != txtConfirmEmail.Value.Trim())
                {
                    ShowAlert("L’adresse e-mail et sa confirmation doivent être identiques.");
                    return;
                }

                // Initialisation de l'image
                string img = UploadImage();
                if (img == null) return; // Erreur lors de l'upload

                if (cd == 0)
                {
                    // Préparation des données
                    co.Nom = txtnm.Value;
                    co.TypeOrganisationId = Convert.ToInt32(drpdtype.SelectedValue);
                    co.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                    co.CommuneId = Convert.ToInt32(drpdcom.SelectedValue);
                    co.Email = txtConfirmEmail.Value;
                    co.Telephone = txtphone.Value;
                    co.SiteWeb = "";
                    co.Logo = img;
                    co.RS = "organisation"; ;
                    co.RC = "";
                    co.RC_doc = "No Doc";
                    co.NIF = "";
                    co.NIF_doc = "NIF Doc";
                    co.facebook = "";
                    co.twitter = "";
                    co.instagramme = "";
                    co.linkedin = "";
                    co.youtube = "";
                    co.province =drpdprov.SelectedItem.ToString();
                    co.commune = drpdcom.SelectedItem.ToString();
                    co.Description = "";
                    co.Vision = "";
                    co.Mission = "";
                    co.NbreHomme = 0;
                    co.NbreFemme = 0;
                    co.Enregistre = drpdexiste.SelectedValue;
                    co.DateCreation = Convert.ToDateTime(txtdatecreation.Value);
                    co.Statut = "actif";
                    co.name = com.GenerateSlug(txtnm.Value);
                    co.Adresse = txt_adress.Value;
                    co.sigle = string.IsNullOrWhiteSpace(txtsigle.Value) ? txtnm.Value : txtsigle.Value;
                    co.Latitude = 0;
                    co.Longitude = 0;

                    // Enregistrement
                    int info = obj.Ajouter(co);
                    if (info == 1)
                    {
                        int dc = GridView1.Rows.Count;
                        if (dc > 0) ajourdomainecoop();

                        NouveauMembre();
                        NouveauMembreOrganisation();

                     

                        SendConfirmationEmail(txtemail.Value, txtnm.Value);

                        ShowAlert("Merci, votre inscription a été envoyée avec succès. Consultez votre e-mail de bienvenue.");
                        vider();
                    }
                    else
                    {
                        ShowAlert("Vérifiez bien vos informations : Le Nom, RC, NIF de votre institution.");
                    }
                }
            }
            catch (Exception ex)
            {
                // Loguez ici avec votre outil ou dans un fichier
                ShowAlert("Une erreur est survenue. Veuillez réessayer plus tard.");
            }
        }

        private void ShowAlert(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "alert", $"alert('{message}');", true);
        }

        private bool IsCaptchaValid()
        {
            string captchaResponse = Request.Form["g-recaptcha-response"];
            string secretKey = "6LcJKC0rAAAAAOkxp8DV-yFfpwrG_1tRUpfwq-gJ";

            using (WebClient client = new WebClient())
            {
                string apiUrl = $"https://www.google.com/recaptcha/api/siteverify?secret={secretKey}&response={captchaResponse}";
                string jsonResult = client.DownloadString(apiUrl);
                JavaScriptSerializer js = new JavaScriptSerializer();
                dynamic result = js.Deserialize<dynamic>(jsonResult);
                return result["success"] == true;
            }
        }
        private string UploadImage()
        {
            if (fileupd.HasFile)
            {
                string fileName = Path.GetFileName(fileupd.FileName);
                string extension = Path.GetExtension(fileName).ToLower();

                string[] validExtensions = { ".png", ".jpeg", ".gif", ".jpg", ".jfif" };

                if (!validExtensions.Contains(extension))
                {
                    ShowAlert("La photo doit avoir ces extensions : .png, .jpg, .gif, .jpeg, .jfif.");
                    return null;

                }

                if (fileupd.PostedFile.ContentLength > 104857600)
                {
                    ShowAlert("Votre photo est trop volumineuse. Elle doit faire moins de 100MB.");
                    return null;
                }

                string path = Server.MapPath("~/images/") + fileName;
                fileupd.SaveAs(path);
                imge = fileName;

            }
            else
            {
                    imge = "No Image";
               
            }
            return imge;

        }


    
        private void SendConfirmationEmail(string toEmail, string name)
        {
            objmem.AfficherDetails(com.GenerateSlug("Administrateurde" + txtnm.Value), memb,0);
            SmtpClient smtpClient = new SmtpClient("send.one.com", 587)
            {
                UseDefaultCredentials = true,
                Credentials = new System.Net.NetworkCredential("<EMAIL>", "Newpass1980"),
                DeliveryMethod = SmtpDeliveryMethod.Network,
                EnableSsl = true
            };

            MailMessage mail = new MailMessage
            {
                IsBodyHtml = true,
                From = new MailAddress("<EMAIL>"),
                Subject = "Inscription dans Linked Community Burundi - LinCom Burundi",
                Body = $"Bonjour cher partenaire <strong>{name}</strong>,<br/>" +
                       "Bienvenue dans la plateforme Linked Community Burundi - LinCom Burundi... nous sommes heureux de vous avoir comme membre. <br/><br/><br/> "+
                       " Si vous avez des questions, vous pouvez nous <NAME_EMAIL> <br/><br/><br/><br/>" +
                       " Avec LinCom, vous avez accès à des fonctionnalités qui vous aiderons à mieux organiser dans l'interne vos activités et cooperateurs.<br/><br/><br/>" +
                       " Vous gerez votre espace de travail numérique et profitez la collaboration avec d'autres acteurs et les opportunités des projets."+
                       "<br/><br/><br/>"+
                       "Voici les coordonnées pour gerer votre espace :<br/>"+
                       "Username : "+memb.username+
                       "Mot de passe : "+memb.motpasse+
                       "<br/><br/><br/>"+
                       "Merci Beaucoup"

            };

            mail.To.Add(new MailAddress(toEmail));
            smtpClient.Send(mail);
        }


        public void adOrganisation(int cd)
        { /*----------Enregistrement---------------*/
            try
            {
                if (!chkConditions.Checked)
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Vous devez accepter les politiques et conditions pour continuer.')</script>");

                    return;
                }


                string captchaResponse = Request.Form["g-recaptcha-response"];
                string secretKey = "6LcJKC0rAAAAAOkxp8DV-yFfpwrG_1tRUpfwq-gJ";

                string apiUrl = $"https://www.google.com/recaptcha/api/siteverify?secret={secretKey}&response={captchaResponse}";
                using (WebClient client = new WebClient())
                {
                    string jsonResult = client.DownloadString(apiUrl);
                    JavaScriptSerializer js = new JavaScriptSerializer();
                    dynamic result = js.Deserialize<dynamic>(jsonResult);

                    if (result["success"] == true)
                    {

                        if (cd == 0)
                        {

                            if (txtnm.Value == "" || txtphone.Value == "" || drpdprov.SelectedValue == "-1" || drpdcom.SelectedValue == "-1" || txtemail.Value == null)
                            {
                                Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez au moins les champs avec * ')</script>");
                            }
                            else
                            {
                                // Vérification que les deux emails correspondent
                                if (txtemail.Value.Trim() != txtConfirmEmail.Value.Trim())
                                {
                                    Response.Write("<script LANGUAGE=JavaScript>alert('L’adresse e-mail et la confirmation doivent être identiques.')</script>");
                                    return;
                                }

                                if (fileupd.HasFile)
                                {

                                    fileupd.SaveAs(Server.MapPath("~/images/") + Path.GetFileName(fileupd.FileName));
                                    string img = Path.GetFileName(fileupd.FileName);

                                    FileInfo ext = new FileInfo(img);

                                    if (ext.Extension == ".png" || ext.Extension == ".jpeg" || ext.Extension == ".gif" || ext.Extension == ".jpg" || ext.Extension == ".jfif")
                                    {
                                        if (fileupd.PostedFile.ContentLength < 104857600)
                                        {
                                            pht = img;
                                        }
                                        else
                                        {
                                            Response.Write("<script LANGUAGE=JavaScript>alert('Votre Photo est volumineuse! Il faut qu'elle pese 100Mb!!!')</script>");
                                        }

                                    }
                                    else
                                    {
                                        Response.Write("<script LANGUAGE=JavaScript>alert('La photo doit avoir ces extentions : .png, .jfif, .jpg, .gif, .jpeg!!!')</script>");

                                    }
                                }
                                else pht = "No Image";

                                co.Nom = txtnm.Value;
                                co.TypeOrganisationId = Convert.ToInt32(drpdtype.SelectedValue);
                                co.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                                co.CommuneId = Convert.ToInt32(drpdcom.SelectedValue);
                                co.Email = txtemail.Value;
                                co.Telephone = txtphone.Value;
                                co.SiteWeb = "";
                                co.Logo = pht;
                                co.RS = "";
                                co.RC = "";
                                co.RC_doc = "No Doc";
                                co.NIF = "";
                                co.NIF_doc = "NIF Doc";
                                co.facebook = "";
                                co.twitter = "";
                                co.instagramme = "";
                                co.linkedin = "";
                                co.youtube = "";
                                co.province = "";
                                co.commune = drpdcom.SelectedItem.ToString();
                                co.Description = "";
                                co.Vision = "";
                                co.Mission = "";
                                co.NbreHomme = 0;
                                co.NbreFemme = 0;
                                co.Enregistre = drpdexiste.SelectedValue;
                                co.DateCreation = Convert.ToDateTime(txtdatecreation.Value);
                                co.Statut = "actif";
                                co.name = com.GenerateSlug(txtnm.Value);
                                co.CreatedAt = DateTime.Now;
                                co.UpdatedAt = DateTime.Now;

                                co.TypeOrganisationId = Convert.ToInt32(drpdtype.SelectedValue);

                                if (txtsigle.Value == "") co.sigle = txtnm.Value;
                                else co.sigle = txtsigle.Value;



                                info = obj.Ajouter(co);
                                if (info == 1)
                                {
                                    int dc = GridView1.Rows.Count;
                                    if (dc > 0) ajourdomainecoop();

                                    SmtpClient smtpClient = new SmtpClient("send.one.com", 587);
                                    smtpClient.UseDefaultCredentials = true;
                                    smtpClient.Credentials = new System.Net.NetworkCredential("<EMAIL>", "Newpass1980");
                                    smtpClient.DeliveryMethod = SmtpDeliveryMethod.Network;
                                    smtpClient.EnableSsl = true;
                                    MailMessage mail = new MailMessage();
                                    mail.IsBodyHtml = true;
                                    mail.From = new MailAddress("<EMAIL>");
                                    mail.To.Add(new MailAddress(txtemail.Value));
                                    mail.Subject = "Inscription dans Linked Community Burundi - LinCom Burundi";
                                    mail.Body = "Bonjour cher partenaire<strong>" + txtnm.Value + "</strong> , <br/>" +
                                         " Bienvenu(e) dans la plateforme Linked Community Burundi - LinCom Burundi, nous sommes heureux de vous avoir comme membre. <br/><br/><br/>" +
                                         " Si vous avez des questions, vous pouvez nous <NAME_EMAIL> <br/><br/><br/><br/>" +
                                         " Avec LinCom, vous avez accès à des fonctionnalités qui vous aiderons à mieux organiser dans l'interne vos activités et cooperateurs.<br/><br/><br/>" +
                                         " Vous gerez votre espace de travail numérique et profitez la collaboration avec d'autres acteurs et les opportunités des projets.";
                                    smtpClient.Send(mail);

                                    Response.Write("<script LANGUAGE=JavaScript>alert('Merci , votre Inscription a été envoyée avec Succès. Visitez votre e-mail de bienvenu. Vous allez attendre la confirmation des administrateurs pour beneficier des fonctionnalités de LinCom.!!!')</script>");
                                    vider();
                                    //    Response.Redirect("~/login.aspx");

                                }
                                else
                                {
                                    Response.Write("<script LANGUAGE=JavaScript>alert(' Verifiez bien vos Informations')</script>");

                                }


                            }
                        }
                    }
                    else
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Erreur CAPTCHA. Veuillez cocher la case Je ne suis pas un robot.')</script>");

                    }
                }


            }
            catch (Exception e)
            {

            }

        }

        void inscript()
        {
            //if(radbtnassoc.Checked==true)
            //    inscrptassoc.Visible= true;
            //else inscrptpart.Visible= false;
        }
        protected void radbtnassoc_CheckedChanged(object sender, EventArgs e)
        {
            //if (radbtnassoc.Checked == true)
            //{
            //    inscrptassoc.Visible = true;
            //    inscrptpart.Visible= false;
            //    radbtnparte.Checked = false;
            //}
        }
        protected void radbtnparte_CheckedChanged(object sender, EventArgs e)
        {
            //if (radbtnparte.Checked == true)
            //{
            //    inscrptpart.Visible = true;
            //    inscrptassoc.Visible= false;
            //    radbtnassoc.Checked = false;
            //}
        }
        protected void btn_enreg_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx");
        }

        protected void afficher()
        {
            //if (nscno != null)
            //{
            //    obj.afficherDetails(Convert.ToInt32(nscno), co);
            //    //txt_nm.Value = prov.NMCAT;
            //    //txt_descript.Value = prov.DESCRPITCAT;

            //}
            
        }
        protected void drpd_prov_SelectedIndexChanged(object sender, EventArgs e)
        {
            objco.chargerCommune(drpdcom, Convert.ToInt32(drpdprov.SelectedValue));
        }

        protected void aci_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#hero");
        }

        protected void prop_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#about");
        }

        protected void serv_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#services");
        }

        protected void inst_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#team");
        }

        protected void cont_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#contact");
        }

       
        protected void btn_enreg_ServerClick(object sender, EventArgs e)
        {
            AjoutOrganisation(0);
        }


        protected void btn_enregpart_ServerClick(object sender, EventArgs e)
        {
            AjoutOrganisation(1);
        }

        protected void doma_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = true;
            ajourdom.Visible = false;
        }
      
            public static string GenererMotDePasseParDefaut(int longueur = 10)
            {
                const string caracteres = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%&*!";

                var motDePasse = new StringBuilder();
                var random = new Random();

                for (int i = 0; i < longueur; i++)
                {
                    var index = random.Next(caracteres.Length);
                    motDePasse.Append(caracteres[index]);
                }

                return motDePasse.ToString();
            }
       

        void NouveauMembre()
        {
            try
            {
                objmem.AfficherDetails(com.GenerateSlug("Administrateurde" + txtnm.Value), memb,0);
                if (memb.name == com.GenerateSlug("Administrateurde" + txtnm.Value))
                {
                    ShowAlert("Votre organisation est deja enregistree. Veuillez consulter votre email pour avoir plus d'informations.");
                    return;
                }
                
                mem.Nom = "Administrateur de ";
                mem.Prenom = txtnm.Value;
                mem.Email = txtConfirmEmail.Value;
                mem.Telephone =txtphone.Value;
                mem.Sexe = "Masculin";
                mem.DateNaissance = Convert.ToDateTime(txtdatecreation.Value); ;
                mem.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                mem.CommuneId = Convert.ToInt32(drpdcom.SelectedValue);
                mem.name = com.GenerateSlug("Administrateurde"+txtnm.Value);
                mem.province = drpdprov.SelectedItem.ToString();
                mem.commune = drpdprov.SelectedItem.ToString();
              
                mem.username = txtConfirmEmail.Value;
                mem.motpasse = BCrypt.Net.BCrypt.HashPassword(GenererMotDePasseParDefaut());
                mem.statut = "actif";
                mem.IsActive = 1;
                mem.IsVerified = 1;
                mem.LastLogin = DateTime.Now;
                mem.ResetToken = null;
                mem.ResetTokenExpiry = null;
                objrl.AfficherDetails("membre", rl);
                mem.RoleMembreID = rl.RoleMembreID;
              
                objmem.Ajouter(mem);


            }
            catch (Exception ex)
            {
                // logguer l'exception pour le debug
                System.Diagnostics.Debug.WriteLine("Erreur : " + ex.Message);
            }
        }
       
        void NouveauMembreOrganisation()
        {
            try
            {
                //objmem.AfficherDetails(com.GenerateSlug("Administrateurde" + txtnm.Value), memb,0);
                //if (memb.name == com.GenerateSlug("Administrateurde" + txtnm.Value))
                //{
                //    ShowAlert("Votre organisation est deja enregistree. Veuillez consulter votre email pour avoir plus d'informations.");
                //    return;
                //}

                objmem.AfficherDetails(com.GenerateSlug("Administrateurde" + txtnm.Value), mem,0);
                obj.AfficherDetails(com.GenerateSlug(txtnm.Value),co);
                memorg.MembreId = mem.MembreId;
                memorg.OrganisationId = co.OrganisationId;
                memorg.Poste = "Administrateur de l'organisation";
                memorg.DateAdhesion = DateTime.Now;
                memorg.Name = com.GenerateSlug("Administrateurde" + txtnm.Value);
                memorg.Statut = "actif";

                objrl.AfficherDetails("membre", rl);
                memorg.RoleMembreID = rl.RoleMembreID;

                objmemorg.Ajouter(memorg);
            }
            catch (Exception ex)
            {
                // logguer ou afficher les erreurs pour le débogage
                System.Diagnostics.Debug.WriteLine("Erreur dans MembreOrganisation : " + ex.Message);
            }
        }
        void ajourdomainecoop()
        {
            try
            {
                int dc = GridView1.Rows.Count;
                if (dc > 0)
                {
                    foreach (GridViewRow row in this.GridView1.Rows)
                    {
                        if (row.RowType == DataControlRowType.DataRow)
                        {
                            obj.AfficherDetails(com.GenerateSlug(txtnm.Value),co);
                            index = co.OrganisationId;
                            actco.OrganisationId = index;
                            objdom.AfficherDetails(row.Cells[1].Text, domai,1);
                            actco.DomaineInterventionId = Convert.ToInt32(domai.DomaineInterventionId);
                            actco.Description = "";
                            actco.statut = "actif";
                            actco.name = com.GenerateSlug(row.Cells[1].Text + txtnm.Value);

                            objactco.Ajouter(actco);

                        }
                    }

                }

            }
            catch (Exception ex)
            { // logguer ou afficher les erreurs pour le débogage
                System.Diagnostics.Debug.WriteLine("Erreur dans Domaines d'Intervention de l'organisation : " + ex.Message);

            }

        }

        protected void btnajoutdom_Click(object sender, EventArgs e)
        {
            try
            {
                if (drpddomai.SelectedValue == "-1" || drpddomai.SelectedValue == "0")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    dom.Visible = true;
                    int i = GridView1.Rows.Count;
                    dat = (DataTable)ViewState["Record"];
                    dat.Rows.Add(drpddomai.SelectedValue, drpddomai.SelectedItem);
                    GridView1.DataSource = dat;
                    GridView1.DataBind();

                }

            }
            catch (Exception ec)
            {

            }
        }

        protected void btnvider_Click(object sender, EventArgs e)
        {
            dom.Visible = true;
            dat = (DataTable)ViewState["Record"];
            dat.Rows.Clear();
            GridView1.DataSource = dat;
            GridView1.DataBind();
        }
        public void adCat()
        {
            try
            {
                if (txtnmdom.Value == "")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    domai.Libelle = txtnmdom.Value;
                    domai.name =com.GenerateSlug(txtnmdom.Value);
                    

                    info = objdom.Ajouter(domai);
                    if (info == 1)
                    {
                        objdom.ChargerDomainedisponible(drpddomai);
                        ajourdom.Visible = false;
                        dom.Visible = false;
                    }
                    else
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Ce domaine Existe deja')</script>");


                    }


                }

            }
            catch (Exception e)
            {

            }

            //LabelMsg.Text = obj.ajouterProvince(prov).ToString();
        }

        public void remplissagedomaineorganisation()
        {
            try
            {
                if (drpddomai.SelectedValue == "-1" || drpddomai.SelectedValue == "0")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    dom.Visible = true;
                    int i = GridView1.Rows.Count;
                    dat = (DataTable)ViewState["Record"];
                    dat.Rows.Add(i = i + 1, drpddomai.SelectedItem);
                    GridView1.DataSource = dat;
                    GridView1.DataBind();

                }

            }
            catch (Exception ec)
            {

            }
        }
        public void EnvoyerNotification(string fcmToken, string titre, string message)
        {
            string serverKey = "AIzaSyAp1PHTxzyIXzvdnHZA0B8tkvWjds7cqzg";
            string senderId = "893157791741";

            var req = WebRequest.Create("https://fcm.googleapis.com/fcm/send");
            req.Method = "POST";
            req.ContentType = "application/json";
            req.Headers.Add("Authorization:key=" + serverKey);
            req.Headers.Add("Sender:id=" + senderId);

            var data = new
            {
                to = fcmToken,
                notification = new
                {
                    title = titre,
                    body = message
                }
            };

            string json = new JavaScriptSerializer().Serialize(data);
            byte[] byteArray = Encoding.UTF8.GetBytes(json);
            req.ContentLength = byteArray.Length;

            using (var dataStream = req.GetRequestStream())
            {
                dataStream.Write(byteArray, 0, byteArray.Length);
            }

            using (var response = req.GetResponse())
            {
                using (var reader = new StreamReader(response.GetResponseStream()))
                {
                    string result = reader.ReadToEnd();
                    // Log result if needed
                }
            }
        }

        protected void btnajourdom_ServerClick(object sender, EventArgs e)
        {
            remplissagedomaineorganisation();
        }

        protected void btnannuldom_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = true;
            dat = (DataTable)ViewState["Record"];
            dat.Rows.Clear();
            GridView1.DataSource = dat;
            GridView1.DataBind();
        }

        protected void ajoudom_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = false;
            ajourdom.Visible = true;
        }

        protected void btnajoutnouvdom_ServerClick(object sender, EventArgs e)
        {
            adCat();
        }


        protected void btnreng_ServerClick(object sender, EventArgs e)
        {
            AjoutOrganisation(0);
        }

        protected void drpddomai_SelectedIndexChanged(object sender, EventArgs e)
        {
            dom.Visible = true;
            remplissagedomaineorganisation();
        }

    }
}