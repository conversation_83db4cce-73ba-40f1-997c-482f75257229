﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MentorImp: IMentor
    {
        int msg;
        private Mentor mentor = new Mentor();

        public void AfficherDetails(int mentorId, Mentor_Class mentorClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Mentors.FirstOrDefault(x => x.MentorId == mentorId);
                if (m != null)
                {
                    mentorClass.MentorId = m.MentorId;
                    mentorClass.MembreId = m.MembreId;
                    mentorClass.DomaineExpertise = m.DomaineExpertise;
                    mentorClass.name = m.name;
                    mentorClass.ProgrammeMentoratId = m.ProgrammeMentoratId;
                    mentorClass.status = m.status;
                }
            }
        }

        public int Ajouter(Mentor_Class mentorClass)
        {
            using (Connection con = new Connection())
            {
                mentor.MembreId = mentorClass.MembreId;
                mentor.DomaineExpertise = mentorClass.DomaineExpertise;
                mentor.name = mentorClass.name;
                mentor.ProgrammeMentoratId = mentorClass.ProgrammeMentoratId;
                mentor.status = mentorClass.status ?? "actif";

                try
                {
                    con.Mentors.Add(mentor);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

       
        public void ChargerMentors(GridView gdv, string status = "")
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Mentors
                            join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                            from programme in programmes.DefaultIfEmpty()
                            where string.IsNullOrEmpty(status) || m.status == status
                            select new
                            {
                                m.MentorId,
                                m.MembreId,
                                Membre = membre != null ? membre.Nom : "Inconnu",
                                m.DomaineExpertise,
                                m.name,
                                m.ProgrammeMentoratId,
                                Programme = programme != null ? programme.Titre : "Aucun",
                                m.status
                            };

                gdv.DataSource = query.OrderBy(x => x.Membre).ToList();
                gdv.DataBind();
            }
        }

        public void chargerMentors(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Mentors select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le Mentor";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.MentorId.ToString();
                        item.Text = data.name;
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }


        public void ChargerMentorsParProgramme(GridView gdv, int programmeMentoratId)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Mentors
                            join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            where m.ProgrammeMentoratId == programmeMentoratId
                            select new
                            {
                                m.MentorId,
                                m.MembreId,
                                Membre = membre != null ? membre.Nom : "Inconnu",
                                m.DomaineExpertise,
                                m.name,
                                m.status
                            };

                gdv.DataSource = query.OrderBy(x => x.Membre).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerMentorsParMembre(GridView gdv, long membreId)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Mentors
                            join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                            from programme in programmes.DefaultIfEmpty()
                            where m.MembreId == membreId
                            select new
                            {
                                m.MentorId,
                                m.DomaineExpertise,
                                m.name,
                                m.ProgrammeMentoratId,
                                Programme = programme != null ? programme.Titre : "Aucun",
                                m.status
                            };

                gdv.DataSource = query.OrderBy(x => x.Programme).ToList();
                gdv.DataBind();
            }
        }

        public int Modifier(Mentor_Class mentorClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Mentors.FirstOrDefault(x => x.MentorId == mentorClass.MentorId);
                if (m != null)
                {
                    m.MembreId = mentorClass.MembreId;
                    m.DomaineExpertise = mentorClass.DomaineExpertise;
                    m.name = mentorClass.name;
                    m.ProgrammeMentoratId = mentorClass.ProgrammeMentoratId;
                    m.status = mentorClass.status;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(int mentorId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Mentors.FirstOrDefault(x => x.MentorId == mentorId);
                if (m != null)
                {
                    con.Mentors.Remove(m);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}