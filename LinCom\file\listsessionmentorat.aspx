<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="listsessionmentorat.aspx.cs" Inherits="LinCom.file.listsessionmentorat" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Breadcomb area Start-->
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">
                                    <div class="breadcomb-icon">
                                        <i class="notika-icon notika-form"></i>
                                    </div>
                                    <div class="breadcomb-ctn">
                                        <h2>Liste des Sessions de Mentorat</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">
                                    <a data-toggle="tooltip" data-placement="left" href="sessionmentorat.aspx" title="Cliquez pour planifier une nouvelle session" class="btn">Nouvelle Session</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->

    <!-- Data Table area Start-->
    <div class="normal-table-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="data-table-list">
                        <asp:Label ID="lblMessage" runat="server" Visible="false" CssClass="alert alert-success"></asp:Label>
                        <div class="table-responsive">
                            <asp:GridView ID="gdv" class="table table-striped" runat="server" CssClass="datatbemp"
                                    AutoGenerateColumns="False" EmptyDataText="Aucune session de mentorat trouvée"
                                    GridLines="None" Width="100%" OnRowCommand="gdv_RowCommand">
                                    <AlternatingRowStyle BackColor="#DCDCDC" />
                                    <Columns>
                                        <asp:TemplateField HeaderText="Programme" FooterText="Programme">
                                            <ItemTemplate>
                                                <asp:Label ID="lb_programme" runat="server" Text='<%# Eval("NomProgramme") %>'></asp:Label>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="Mentor" FooterText="Mentor">
                                            <ItemTemplate>
                                                <asp:Label ID="lb_mentor" runat="server" Text='<%# Eval("NomMentor") %>'></asp:Label>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="Mentoré" FooterText="Mentoré">
                                            <ItemTemplate>
                                                <asp:Label ID="lb_mentore" runat="server" Text='<%# Eval("NomMentore") %>'></asp:Label>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="Sujet" FooterText="Sujet">
                                            <ItemTemplate>
                                                <asp:Label ID="lb_sujet" runat="server" Text='<%# Eval("Sujet") %>'></asp:Label>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="Date Session" FooterText="Date Session">
                                            <ItemTemplate>
                                                <asp:Label ID="lb_date" runat="server" Text='<%# Eval("DateSession", "{0:dd/MM/yyyy HH:mm}") %>'></asp:Label>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="Statut" FooterText="Statut">
                                            <ItemTemplate>
                                                <span class="badge badge-<%# DateTime.Parse(Eval("DateSession").ToString()) > DateTime.Now ? "warning" : "success" %>">
                                                    <%# DateTime.Parse(Eval("DateSession").ToString()) > DateTime.Now ? "À venir" : "Terminée" %>
                                                </span>
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                        <asp:TemplateField HeaderText="Action" FooterText="Action">
                                            <ItemTemplate>
                                                <asp:Button ID="btnEdit" Height="100px" BackColor="Green" class="btn btn-info btn-fix" CommandName="view" CommandArgument='<%# Eval("SessionMentoratId") %>' runat="server" Text="Edit" />
                                                <asp:Button class="btn btn-danger notika-btn-danger" BackColor="Red" ID="btn_del" runat="server" Text="Delete" CommandName="delete" CommandArgument='<%# Eval("SessionMentoratId") %>' OnClientClick=" return confirm('Voulez-Vous vraiment supprimer??')" />
                                            </ItemTemplate>
                                        </asp:TemplateField>
                                    </Columns>
                                    <EditRowStyle Font-Bold="True"></EditRowStyle>
                                    <EmptyDataRowStyle HorizontalAlign="Center" Font-Names="century" Font-Size="X-Large"></EmptyDataRowStyle>
                                </asp:GridView>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Data Table area End-->

      <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.js"></script>
   <script>
       $(document).ready(function () {
           // Attendre que tous les autres scripts soient chargés
           setTimeout(function() {
               try {
                   // Vérifier si le tableau existe
                   if ($(".datatbemp").length > 0) {
                       // Créer l'en-tête si nécessaire
                       var $table = $(".datatbemp");
                       if ($table.find("thead").length === 0) {
                           $table.prepend($("<thead></thead>").append($table.find("tr:first").clone()));
                       }

                       // Initialiser DataTable avec configuration sécurisée
                       $table.DataTable({
                           "language": {
                               "url": "//cdn.datatables.net/plug-ins/1.11.3/i18n/French.json"
                           },
                           "pageLength": 10,
                           "responsive": true,
                           "destroy": true // Permet de réinitialiser si déjà initialisé
                       });
                   }
               } catch (e) {
                   console.log("DataTables initialization error:", e);
               }
           }, 500);
       });
   </script>
</asp:Content>
