using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Security;

namespace LinCom.Security
{
    /// <summary>
    /// Classe utilitaire pour la sécurité du forum LinCom
    /// </summary>
    public static class ForumSecurityHelper
    {
        // Expressions régulières pour validation
        private static readonly Regex HtmlTagRegex = new Regex(@"<[^>]*>", RegexOptions.Compiled);
        private static readonly Regex ScriptTagRegex = new Regex(@"<script[^>]*>.*?</script>", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex SqlInjectionRegex = new Regex(@"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE){0,1}|INSERT( +INTO){0,1}|MERGE|SELECT|UPDATE|UNION( +ALL){0,1})\b)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        
        // Mots-clés dangereux
        private static readonly string[] DangerousKeywords = {
            "javascript:", "vbscript:", "onload", "onerror", "onclick", "onmouseover",
            "eval(", "expression(", "url(", "import", "document.cookie", "document.write"
        };

        // Balises HTML autorisées pour le contenu du forum
        private static readonly string[] AllowedHtmlTags = {
            "p", "br", "strong", "b", "em", "i", "u", "code", "pre", "blockquote",
            "h1", "h2", "h3", "h4", "h5", "h6", "ul", "ol", "li", "a", "img"
        };

        #region Validation des entrées

        /// <summary>
        /// Valide et nettoie le titre d'une question
        /// </summary>
        public static ValidationResult ValidateQuestionTitle(string title)
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(title))
            {
                result.IsValid = false;
                result.ErrorMessage = "Le titre ne peut pas être vide.";
                return result;
            }

            title = title.Trim();

            if (title.Length < 10)
            {
                result.IsValid = false;
                result.ErrorMessage = "Le titre doit contenir au moins 10 caractères.";
                return result;
            }

            if (title.Length > 250)
            {
                result.IsValid = false;
                result.ErrorMessage = "Le titre ne peut pas dépasser 250 caractères.";
                return result;
            }

            // Vérifier les caractères dangereux
            if (ContainsDangerousContent(title))
            {
                result.IsValid = false;
                result.ErrorMessage = "Le titre contient des caractères non autorisés.";
                return result;
            }

            result.IsValid = true;
            result.CleanedValue = HttpUtility.HtmlEncode(title);
            return result;
        }

        /// <summary>
        /// Valide et nettoie le contenu d'une question ou réponse
        /// </summary>
        public static ValidationResult ValidatePostContent(string content)
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(content))
            {
                result.IsValid = false;
                result.ErrorMessage = "Le contenu ne peut pas être vide.";
                return result;
            }

            content = content.Trim();

            if (content.Length < 30)
            {
                result.IsValid = false;
                result.ErrorMessage = "Le contenu doit contenir au moins 30 caractères.";
                return result;
            }

            if (content.Length > 30000)
            {
                result.IsValid = false;
                result.ErrorMessage = "Le contenu ne peut pas dépasser 30 000 caractères.";
                return result;
            }

            // Nettoyer le HTML dangereux
            string cleanedContent = SanitizeHtml(content);

            // Vérifier les injections SQL
            if (SqlInjectionRegex.IsMatch(cleanedContent))
            {
                result.IsValid = false;
                result.ErrorMessage = "Le contenu contient des éléments non autorisés.";
                return result;
            }

            result.IsValid = true;
            result.CleanedValue = cleanedContent;
            return result;
        }

        /// <summary>
        /// Valide les tags
        /// </summary>
        public static ValidationResult ValidateTags(string tags)
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(tags))
            {
                result.IsValid = true;
                result.CleanedValue = "";
                return result;
            }

            var tagList = tags.Split(',').Select(t => t.Trim()).Where(t => !string.IsNullOrEmpty(t)).ToList();

            if (tagList.Count > 5)
            {
                result.IsValid = false;
                result.ErrorMessage = "Vous ne pouvez pas utiliser plus de 5 tags.";
                return result;
            }

            foreach (var tag in tagList)
            {
                if (tag.Length < 2)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "Chaque tag doit contenir au moins 2 caractères.";
                    return result;
                }

                if (tag.Length > 25)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "Chaque tag ne peut pas dépasser 25 caractères.";
                    return result;
                }

                if (ContainsDangerousContent(tag))
                {
                    result.IsValid = false;
                    result.ErrorMessage = "Les tags contiennent des caractères non autorisés.";
                    return result;
                }
            }

            result.IsValid = true;
            result.CleanedValue = string.Join(",", tagList.Select(HttpUtility.HtmlEncode));
            return result;
        }

        /// <summary>
        /// Valide un terme de recherche
        /// </summary>
        public static ValidationResult ValidateSearchTerm(string searchTerm)
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                result.IsValid = false;
                result.ErrorMessage = "Le terme de recherche ne peut pas être vide.";
                return result;
            }

            searchTerm = searchTerm.Trim();

            if (searchTerm.Length < 2)
            {
                result.IsValid = false;
                result.ErrorMessage = "Le terme de recherche doit contenir au moins 2 caractères.";
                return result;
            }

            if (searchTerm.Length > 100)
            {
                result.IsValid = false;
                result.ErrorMessage = "Le terme de recherche ne peut pas dépasser 100 caractères.";
                return result;
            }

            // Vérifier les injections SQL
            if (SqlInjectionRegex.IsMatch(searchTerm))
            {
                result.IsValid = false;
                result.ErrorMessage = "Le terme de recherche contient des caractères non autorisés.";
                return result;
            }

            result.IsValid = true;
            result.CleanedValue = HttpUtility.HtmlEncode(searchTerm);
            return result;
        }

        #endregion

        #region Nettoyage HTML

        /// <summary>
        /// Nettoie le HTML en supprimant les éléments dangereux
        /// </summary>
        public static string SanitizeHtml(string html)
        {
            if (string.IsNullOrEmpty(html))
                return string.Empty;

            // Supprimer les scripts
            html = ScriptTagRegex.Replace(html, "");

            // Supprimer les attributs dangereux
            html = RemoveDangerousAttributes(html);

            // Encoder les caractères spéciaux restants
            html = HttpUtility.HtmlEncode(html);

            // Permettre certaines balises sécurisées (Markdown basique)
            html = ConvertMarkdownToSafeHtml(html);

            return html;
        }

        /// <summary>
        /// Convertit le Markdown basique en HTML sécurisé
        /// </summary>
        private static string ConvertMarkdownToSafeHtml(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // Gras
            text = Regex.Replace(text, @"\*\*(.*?)\*\*", "<strong>$1</strong>");
            
            // Italique
            text = Regex.Replace(text, @"\*(.*?)\*", "<em>$1</em>");
            
            // Code inline
            text = Regex.Replace(text, @"`(.*?)`", "<code>$1</code>");
            
            // Code block
            text = Regex.Replace(text, @"```(.*?)```", "<pre><code>$1</code></pre>", RegexOptions.Singleline);
            
            // Liens (sécurisés)
            text = Regex.Replace(text, @"\[([^\]]+)\]\(([^)]+)\)", 
                match => $"<a href=\"{HttpUtility.HtmlAttributeEncode(match.Groups[2].Value)}\" rel=\"nofollow noopener\" target=\"_blank\">{HttpUtility.HtmlEncode(match.Groups[1].Value)}</a>");
            
            // Retours à la ligne
            text = text.Replace("\n", "<br>");

            return text;
        }

        /// <summary>
        /// Supprime les attributs HTML dangereux
        /// </summary>
        private static string RemoveDangerousAttributes(string html)
        {
            // Supprimer tous les attributs commençant par "on" (onclick, onload, etc.)
            html = Regex.Replace(html, @"\s+on\w+\s*=\s*[""'][^""']*[""']", "", RegexOptions.IgnoreCase);
            
            // Supprimer javascript: dans les href
            html = Regex.Replace(html, @"href\s*=\s*[""']javascript:[^""']*[""']", "", RegexOptions.IgnoreCase);
            
            return html;
        }

        #endregion

        #region Vérifications de sécurité

        /// <summary>
        /// Vérifie si le contenu contient des éléments dangereux
        /// </summary>
        public static bool ContainsDangerousContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return false;

            content = content.ToLower();

            return DangerousKeywords.Any(keyword => content.Contains(keyword.ToLower()));
        }

        /// <summary>
        /// Vérifie les permissions de l'utilisateur
        /// </summary>
        public static bool CanUserPerformAction(long userId, string action, long? targetUserId = null)
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var user = con.Membres.Where(m => m.MembreId == userId).FirstOrDefault();
                    if (user == null)
                        return false;

                    switch (action.ToLower())
                    {
                        case "vote":
                            return user.Reputation >= 15; // Minimum pour voter
                        case "comment":
                            return user.Reputation >= 50; // Minimum pour commenter
                        case "edit":
                            return user.Reputation >= 2000 || (targetUserId.HasValue && targetUserId.Value == userId);
                        case "moderate":
                            return user.Reputation >= 3000; // Modérateur
                        case "close":
                            return user.Reputation >= 3000;
                        case "delete":
                            return user.Reputation >= 10000;
                        default:
                            return true;
                    }
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Vérifie si l'utilisateur peut voter (pas de vote multiple)
        /// </summary>
        public static bool CanUserVote(long userId, long postId)
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    // Vérifier si l'utilisateur a déjà voté
                    var existingVote = con.Votes.Where(v => v.UserId == userId && v.PostId == postId).FirstOrDefault();
                    return existingVote == null;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Limite le taux de requêtes par utilisateur (protection DoS)
        /// </summary>
        public static bool IsRateLimited(long userId, string action)
        {
            string cacheKey = $"rate_limit_{userId}_{action}";
            var cache = HttpContext.Current.Cache;
            
            if (cache[cacheKey] != null)
            {
                int count = (int)cache[cacheKey];
                if (count > GetRateLimit(action))
                {
                    return true; // Rate limited
                }
                cache[cacheKey] = count + 1;
            }
            else
            {
                cache.Insert(cacheKey, 1, null, DateTime.Now.AddMinutes(1), TimeSpan.Zero);
            }

            return false;
        }

        private static int GetRateLimit(string action)
        {
            switch (action.ToLower())
            {
                case "post": return 5; // 5 posts par minute
                case "vote": return 30; // 30 votes par minute
                case "comment": return 10; // 10 commentaires par minute
                default: return 20;
            }
        }

        #endregion

        #region Logging sécurisé

        /// <summary>
        /// Log une tentative d'attaque de manière sécurisée
        /// </summary>
        public static void LogSecurityIncident(string incident, long? userId = null, string ipAddress = null)
        {
            try
            {
                // Ne pas logger les données sensibles directement
                string logMessage = $"Security incident: {incident}";
                if (userId.HasValue)
                    logMessage += $" - User: {userId.Value}";
                if (!string.IsNullOrEmpty(ipAddress))
                    logMessage += $" - IP: {ipAddress}";

                System.Diagnostics.Debug.WriteLine($"[SECURITY] {DateTime.Now}: {logMessage}");
                
                // TODO: Implémenter un système de logging plus robuste
                // (base de données, fichier sécurisé, etc.)
            }
            catch
            {
                // Ne pas faire échouer l'application si le logging échoue
            }
        }

        #endregion
    }

    /// <summary>
    /// Résultat de validation
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public string CleanedValue { get; set; }
    }
}
