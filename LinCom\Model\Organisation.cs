//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace LinCom.Model
{
    using System;
    using System.Collections.Generic;
    
    public partial class Organisation
    {
        public long OrganisationId { get; set; }
        public string Nom { get; set; }
        public int TypeOrganisationId { get; set; }
        public int ProvinceId { get; set; }
        public int CommuneId { get; set; }
        public string Adresse { get; set; }
        public string Email { get; set; }
        public string Telephone { get; set; }
        public string SiteWeb { get; set; }
        public string Logo { get; set; }
        public string RS { get; set; }
        public string sigle { get; set; }
        public string Description { get; set; }
        public string Vision { get; set; }
        public string Mission { get; set; }
        public Nullable<int> NbreHomme { get; set; }
        public Nullable<int> NbreFemme { get; set; }
        public string Enregistre { get; set; }
        public string RC { get; set; }
        public string RC_doc { get; set; }
        public string NIF { get; set; }
        public string NIF_doc { get; set; }
        public string facebook { get; set; }
        public string twitter { get; set; }
        public string instagramme { get; set; }
        public string linkedin { get; set; }
        public string youtube { get; set; }
        public string province { get; set; }
        public string commune { get; set; }
        public Nullable<System.DateTime> DateCreation { get; set; }
        public string Statut { get; set; }
        public Nullable<System.DateTime> CreatedAt { get; set; }
        public Nullable<System.DateTime> UpdatedAt { get; set; }
        public string name { get; set; }
        public Nullable<double> Latitude { get; set; }
        public Nullable<double> Longitude { get; set; }
    }
}
