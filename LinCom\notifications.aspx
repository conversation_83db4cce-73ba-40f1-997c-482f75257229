<%@ Page Title="Notifications - LinCom" Language="C#" MasterPageFile="~/MasterPage.Master" AutoEventWireup="true" CodeBehind="notifications.aspx.cs" Inherits="LinCom.notifications" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="Content/forum-lincom.css" rel="stylesheet" />
    <style>
        .notifications-header {
            background: var(--lincom-gradient-primary);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-bottom: 4px solid var(--lincom-accent);
        }
        
        .notification-item {
            background: var(--lincom-white);
            border: 1px solid var(--lincom-border);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--lincom-secondary);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .notification-item.unread {
            border-left-color: var(--lincom-accent);
            background: rgba(231, 76, 60, 0.02);
        }
        
        .notification-item:hover {
            box-shadow: var(--lincom-shadow-hover);
            transform: translateX(5px);
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--lincom-gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            font-weight: 600;
            color: var(--lincom-primary);
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .notification-message {
            color: var(--lincom-text-muted);
            line-height: 1.5;
            margin-bottom: 0.75rem;
        }
        
        .notification-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: var(--lincom-text-muted);
        }
        
        .notification-date {
            font-style: italic;
        }
        
        .notification-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .notification-btn {
            background: none;
            border: 1px solid var(--lincom-border);
            color: var(--lincom-text-muted);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }
        
        .notification-btn:hover {
            background: var(--lincom-secondary);
            color: white;
            border-color: var(--lincom-secondary);
        }
        
        .unread-indicator {
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 12px;
            height: 12px;
            background: var(--lincom-accent);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .filter-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid var(--lincom-border);
        }
        
        .filter-tab {
            padding: 1rem 1.5rem;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 500;
            color: var(--lincom-text-muted);
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .filter-tab.active {
            color: var(--lincom-accent);
            border-bottom-color: var(--lincom-accent);
            background: rgba(231, 76, 60, 0.05);
        }
        
        .filter-tab:hover {
            color: var(--lincom-accent);
            background: rgba(231, 76, 60, 0.03);
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: var(--lincom-white);
            border: 1px solid var(--lincom-border);
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            border-left: 4px solid var(--lincom-secondary);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--lincom-secondary);
            display: block;
        }
        
        .stat-label {
            color: var(--lincom-text-muted);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .bulk-actions {
            background: var(--lincom-light);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--lincom-text-muted);
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Classes pour les types de notifications */
        .notification-type-badge {
            background: var(--lincom-warning) !important;
        }

        .notification-type-reponse {
            background: var(--lincom-success) !important;
        }

        .notification-type-vote {
            background: var(--lincom-info) !important;
        }

        .notification-type-commentaire {
            background: var(--lincom-secondary) !important;
        }

        .notification-type-question {
            background: var(--lincom-accent) !important;
        }

        .notification-type-default {
            background: var(--lincom-primary) !important;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Header -->
    <div class="notifications-header">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h1><i class="fas fa-bell"></i> Mes Notifications</h1>
                    <p class="lead">Restez informé de toute l'activité sur vos questions et réponses</p>
                </div>
                <div class="col-md-4 text-right">
                    <asp:Button ID="btnMarkAllRead" runat="server" Text="Tout marquer comme lu" 
                        CssClass="lincom-btn-primary" OnClick="btnMarkAllRead_Click" />
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistiques -->
        <div class="stats-cards">
            <asp:Repeater ID="rptStats" runat="server">
                <ItemTemplate>
                    <div class="stat-card">
                        <span class="stat-number"><%# Eval("Count") %></span>
                        <div class="stat-label">
                            <i class="<%# Eval("Icon") %>"></i> <%# Eval("Type") %>
                        </div>
                    </div>
                </ItemTemplate>
            </asp:Repeater>
        </div>

        <!-- Filtres -->
        <div class="filter-tabs">
            <asp:LinkButton ID="lnkAll" runat="server" CssClass="filter-tab active" 
                OnClick="FilterNotifications_Click" CommandArgument="all">
                <i class="fas fa-list"></i> Toutes
            </asp:LinkButton>
            <asp:LinkButton ID="lnkUnread" runat="server" CssClass="filter-tab" 
                OnClick="FilterNotifications_Click" CommandArgument="unread">
                <i class="fas fa-bell"></i> Non lues
            </asp:LinkButton>
            <asp:LinkButton ID="lnkForum" runat="server" CssClass="filter-tab" 
                OnClick="FilterNotifications_Click" CommandArgument="forum">
                <i class="fas fa-comments"></i> Forum
            </asp:LinkButton>
            <asp:LinkButton ID="lnkBadges" runat="server" CssClass="filter-tab" 
                OnClick="FilterNotifications_Click" CommandArgument="badges">
                <i class="fas fa-medal"></i> Badges
            </asp:LinkButton>
        </div>

        <!-- Actions en lot -->
        <div class="bulk-actions">
            <asp:CheckBox ID="chkSelectAll" runat="server" Text="Tout sélectionner" />
            <asp:Button ID="btnDeleteSelected" runat="server" Text="Supprimer sélectionnées" 
                CssClass="lincom-btn-accent btn-sm" OnClick="btnDeleteSelected_Click" />
            <asp:Button ID="btnMarkSelectedRead" runat="server" Text="Marquer comme lues" 
                CssClass="lincom-btn-primary btn-sm" OnClick="btnMarkSelectedRead_Click" />
        </div>

        <!-- Liste des Notifications -->
        <asp:ListView ID="lvNotifications" runat="server" OnItemCommand="lvNotifications_ItemCommand">
            <LayoutTemplate>
                <div id="itemPlaceholder" runat="server"></div>
            </LayoutTemplate>
            <ItemTemplate>
                <div class="notification-item <%# Convert.ToBoolean(Eval("Lu")) ? "" : "unread" %>" 
                     data-notification-id="<%# Eval("NotificationId") %>">
                    
                    <%# !Convert.ToBoolean(Eval("Lu")) ? "<div class='unread-indicator'></div>" : "" %>
                    
                    <div class="d-flex">
                        <div class="notification-icon notification-type-<%# GetNotificationTypeClass(Eval("Titre").ToString()) %>">
                            <i class="<%# GetNotificationIcon(Eval("Titre").ToString()) %>"></i>
                        </div>
                        
                        <div class="notification-content">
                            <div class="notification-title"><%# Eval("Titre") %></div>
                            <div class="notification-message"><%# Eval("Message") %></div>
                            
                            <div class="notification-meta">
                                <div class="notification-date">
                                    <i class="fas fa-clock"></i> <%# GetTimeAgo(Convert.ToDateTime(Eval("DateNotification"))) %>
                                </div>
                                <div class="notification-actions">
                                    <%# !Convert.ToBoolean(Eval("Lu")) ? 
                                        "<asp:Button runat='server' Text='Marquer comme lu' CssClass='notification-btn' CommandName='MarkRead' CommandArgument='" + Eval("NotificationId") + "' />" : "" %>
                                    <asp:Button runat="server" Text="Supprimer" CssClass="notification-btn" 
                                        CommandName="Delete" CommandArgument='<%# Eval("NotificationId") %>' />
                                </div>
                            </div>
                        </div>
                        
                        <div class="ml-auto">
                            <asp:CheckBox runat="server" CssClass="notification-checkbox" 
                                data-notification-id='<%# Eval("NotificationId") %>' />
                        </div>
                    </div>
                </div>
            </ItemTemplate>
            <EmptyDataTemplate>
                <div class="empty-state">
                    <i class="fas fa-bell-slash"></i>
                    <h3>Aucune notification</h3>
                    <p>Vous n'avez aucune notification pour le moment.</p>
                    <a href="forum-questions.aspx" class="lincom-btn-primary">
                        <i class="fas fa-comments"></i> Aller au Forum
                    </a>
                </div>
            </EmptyDataTemplate>
        </asp:ListView>

        <!-- Pagination -->
        <div class="text-center mt-4">
            <asp:Button ID="btnLoadMore" runat="server" Text="Charger plus" 
                CssClass="lincom-btn-outline" OnClick="btnLoadMore_Click" />
        </div>
    </div>

    <script>
        // Gestion de la sélection multiple
        document.getElementById('<%=chkSelectAll.ClientID%>').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.notification-checkbox input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });

        // Animation des notifications non lues
        document.querySelectorAll('.notification-item.unread').forEach(item => {
            item.addEventListener('click', function() {
                this.classList.remove('unread');
                const indicator = this.querySelector('.unread-indicator');
                if (indicator) {
                    indicator.style.animation = 'none';
                    setTimeout(() => indicator.remove(), 300);
                }
            });
        });

        // Auto-refresh des notifications
        setInterval(function() {
            // Vérifier s'il y a de nouvelles notifications
            fetch('/api/notifications/check-new')
                .then(response => response.json())
                .then(data => {
                    if (data.hasNew) {
                        // Afficher une notification ou recharger la page
                        location.reload();
                    }
                })
                .catch(error => console.error('Erreur:', error));
        }, 30000); // Vérifier toutes les 30 secondes

        // Marquer comme lu au clic
        document.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', function(e) {
                if (e.target.tagName !== 'BUTTON' && e.target.tagName !== 'INPUT') {
                    const notificationId = this.dataset.notificationId;
                    if (this.classList.contains('unread')) {
                        // Appel AJAX pour marquer comme lu
                        fetch('/api/notifications/mark-read', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ notificationId: notificationId })
                        });
                    }
                }
            });
        });
    </script>
</asp:Content>
