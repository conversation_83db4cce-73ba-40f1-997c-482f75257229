using LinCom.Class;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class faq_public : System.Web.UI.Page
    {
        FAQ_Class faqObj = new FAQ_Class();
        FAQImp objFaq = new FAQImp();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                ChargerFAQPubliques();
            }
        }

        private void ChargerFAQPubliques()
        {
            try
            {
                objFaq.ChargerFAQPubliques(listFAQ, "publié");
            }
            catch (Exception ex)
            {
                // Log l'erreur si nécessaire
                // Pour l'instant, on affiche une liste vide
            }
        }

        protected void btnRechercher_ServerClick(object sender, EventArgs e)
        {
            try
            {
                string termeRecherche = txtRecherche.Value.Trim();
                
                if (!string.IsNullOrEmpty(termeRecherche))
                {
                    // Recherche dans les FAQ
                    RechercherFAQ(termeRecherche);
                }
                else
                {
                    // Recharger toutes les FAQ
                    ChargerFAQPubliques();
                }
            }
            catch (Exception ex)
            {
                // Log l'erreur si nécessaire
            }
        }

        private void RechercherFAQ(string terme)
        {
            try
            {
                // Utiliser une méthode de recherche personnalisée
                // Pour l'instant, on utilise la méthode existante avec filtre
                objFaq.ChargerFAQPubliques(listFAQ, "publié");
                
                // Filtrer côté serveur si nécessaire
                // Cette logique pourrait être améliorée en ajoutant une méthode de recherche dans FAQImp
            }
            catch (Exception ex)
            {
                // Log l'erreur si nécessaire
            }
        }
    }
}
