using LinCom.Class;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class mentorat : System.Web.UI.Page
    {
        IMentor objMentor = new MentorImp();
        IMentore objMentore = new MentoreImp();
        ISessionMentorat objSession = new SessionMentoratImp();
        IProgrammeMentorat objProgramme = new ProgrammeMentoratImp();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                ChargerStatistiques();
                ChargerProgrammes();
                ChargerMentors();
            }
        }

        private void ChargerStatistiques()
        {
            try
            {
                // Charger les statistiques de mentorat
                int nombreMentors = objMentor.count("actif");
                int nombreMentores = objMentore.count("actif");
                int nombreSessions = objSession.count();

                nbMentors.InnerText = nombreMentors.ToString();
                nbMentores.InnerText = nombreMentores.ToString();
                nbSessions.InnerText = nombreSessions.ToString();
            }
            catch (Exception ex)
            {
                // En cas d'erreur, afficher des valeurs par défaut
                nbMentors.InnerText = "0";
                nbMentores.InnerText = "0";
                nbSessions.InnerText = "0";
            }
        }

        private void ChargerProgrammes()
        {
            try
            {
                // Charger les programmes de mentorat publics depuis la table Post
                objProgramme.ChargerProgrammesPublicsFromPost(listProgrammes, "programmementorat");
            }
            catch (Exception ex)
            {
                // Log l'erreur si nécessaire
                // Pour l'instant, on affiche une liste vide
            }
        }

        private void ChargerMentors()
        {
            try
            {
                // Charger les mentors actifs pour affichage public
                objMentor.ChargerMentorsPublics(listMentors, "actif");
            }
            catch (Exception ex)
            {
                // Log l'erreur si nécessaire
                // Pour l'instant, on affiche une liste vide
            }
        }
    }
}
