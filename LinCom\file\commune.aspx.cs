﻿using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class commune : System.Web.UI.Page
    {
        private int info;
        string nscno;
        Province_Class prov = new Province_Class();
        IProvince objp = new ProvinceImp();
        CommuneClass pc = new CommuneClass();
        ICommune obj = new CommuneImp();
        ICommonCode co=new CommonCode();
        static string typenm; static int id, idpers, rol;
        protected void Page_Load(object sender, EventArgs e)
        {
            nscno = Request.QueryString["name"];
            if (!IsPostBack)
            {
              
                initial_msg();
                if (nscno == null)
                {
                    btn_enreg.InnerText = "Enregistrer";
                    // Response.Redirect("~/sima/province.aspx/");
                }
                else

                {
                    btn_enreg.InnerText = "Modifier";
                    afficher();
                }

            }
        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
            objp.chargerProvince(drpdprov);
        }

        public void adCmn()
        {
            try
            {
                if (txtnm.Value == "" || drpdprov.SelectedValue == "-1")
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";
                }
                else
                {
                    pc.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                   pc.name=co.GenerateSlug(txtnm.Value);
                    pc.Nom = txtnm.Value;
                    info = obj.add(pc);
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listcommune.aspx");

                    }
                    else
                    {
                        div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Cette Commune Existe deja";

                    }


                }

            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Cette Commune Existe deja";

            }

            //LabelMsg.InnerText = obj.ajouterProvince(prov).ToString();
        }

        public void upte()
        {
            try
            {
                if (txtnm.Value == "" || drpdprov.SelectedValue == "-1")
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";
                }
                else
                {
                    pc.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                    pc.name = co.GenerateSlug(txtnm.Value);
                    pc.Nom = txtnm.Value;
                    info = obj.edit(pc, nscno);
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listcommune.aspx");
                    }
                    else
                    {
                        Response.Redirect("~/file/listcommune.aspx");

                    }

                }
            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Cette Province Existe deja";

            }
        }

        protected void btn_enreg_ServerClick(object sender, EventArgs e)
        {
            if (nscno == null)
            {
                adCmn();

            }
            else
                upte();
        }

        protected void btn_enreg_Click(object sender, EventArgs e)
        {
            if (nscno == null)
            {
                adCmn();

            }
            else
                upte();

        }

        protected void afficher()
        {
            if (nscno != null)
            {
                obj.afficherDetails(nscno, pc);
                drpdprov.SelectedValue = pc.ProvinceId.ToString();
                txtnm.Value = pc.Nom;
            }
        }


    }
}