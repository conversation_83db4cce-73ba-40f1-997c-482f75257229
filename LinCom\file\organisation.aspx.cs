﻿using Antlr.Runtime.Misc;
using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Reflection;
using System.Runtime.Remoting;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class organisation : System.Web.UI.Page
    {
        private int info;
        static string imge,nsco;
        string organisationId;
        Organisation_Class organisationClass = new Organisation_Class();
        Organisation_Class org = new Organisation_Class();
        Organisation_Class com = new Organisation_Class();
        IOrganisation objOrganisation = new OrganisationImp();
        ITypeOrganisation objTypeOrganisation = new TypeOrganisationImp();
        IProvince objProvince = new ProvinceImp();
        ICommune objCommune = new CommuneImp();
        ICommonCode co = new CommonCode();
        IMembre objmem = new MembreImp();
        DomaineInterventionOrganisation_Class actco = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objactco = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class domai = new DomaineIntervention_Class();
        IDomaineIntervention objdom = new DomaineInterventionImp();
        Membre_Class mem = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        DataTable dat = new DataTable();
        long index;


        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["name"];
            organisationId = Request.QueryString["name"];

            if (!IsPostBack)
            {
                InitialiserMessages();
                ChargerTypesOrganisation();
                ChargerProvinces();
                objdom.ChargerDomainedisponible(drpddomai);


                if (ViewState["Record"] == null)
                {//depense
                    dat.Columns.Add("#No");
                    dat.Columns.Add("Domaine d'Intervention");

                    ViewState["Record"] = dat;
                }

                if (organisationId != null)
                {
                    btnEnregistrer.InnerText = "Modifier";
                    AfficherDetails();
                }
                else
                {
                    btnEnregistrer.InnerText = "Enregistrer";
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
        }

        private void SendConfirmationEmail(string toEmail, string name)
        {
            objmem.AfficherDetails(co.GenerateSlug("Administrateurde" + txtNomOrganisation.Value), memb, 0);
            SmtpClient smtpClient = new SmtpClient("send.one.com", 587)
            {
                UseDefaultCredentials = true,
                Credentials = new System.Net.NetworkCredential("<EMAIL>", "Newpass1980"),
                DeliveryMethod = SmtpDeliveryMethod.Network,
                EnableSsl = true
            };

            MailMessage mail = new MailMessage
            {
                IsBodyHtml = true,
                From = new MailAddress("<EMAIL>"),
                Subject = "Inscription dans Linked Community Burundi - LinCom Burundi",
                Body = $"Bonjour cher partenaire <strong>{name}</strong>,<br/>" +
                       "Bienvenue dans la plateforme Linked Community Burundi - LinCom Burundi... nous sommes heureux de vous avoir comme membre. <br/><br/><br/> " +
                       " Si vous avez des questions, vous pouvez nous <NAME_EMAIL> <br/><br/><br/><br/>" +
                       " Avec LinCom, vous avez accès à des fonctionnalités qui vous aiderons à mieux organiser dans l'interne vos activités et cooperateurs.<br/><br/><br/>" +
                       " Vous gerez votre espace de travail numérique et profitez la collaboration avec d'autres acteurs et les opportunités des projets." +
                       "<br/><br/><br/>" +
                       "Voici les coordonnées pour gerer votre espace :<br/>" +
                       "Username : " + memb.username +
                       "Mot de passe : " + memb.motpasse +
                       "<br/><br/><br/>" +
                       "Merci Beaucoup"

            };

            mail.To.Add(new MailAddress(toEmail));
            smtpClient.Send(mail);
        }
        protected void doma_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = true;
            ajourdom.Visible = false;
        }
        public static string GenererMotDePasseParDefaut(int longueur = 10)
        {
            const string caracteres = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%&*!";

            var motDePasse = new StringBuilder();
            var random = new Random();

            for (int i = 0; i < longueur; i++)
            {
                var index = random.Next(caracteres.Length);
                motDePasse.Append(caracteres[index]);
            }

            return motDePasse.ToString();
        }
        void NouveauMembre()
        {
            try
            {
                objmem.AfficherDetails(co.GenerateSlug("Administrateurde" + txtNomOrganisation.Value), memb, 0);
                if (memb.name == co.GenerateSlug("Administrateurde" + txtNomOrganisation.Value))
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Votre organisation est deja enregistree. Veuillez consulter votre email pour avoir plus d'informations.";

                    return;
                   
                }

                mem.Nom = "Administrateur de ";
                mem.Prenom = txtNomOrganisation.Value;
                mem.Email = txtConfirmEmail.Value;
                mem.Telephone = txtTelephone.Value;
                mem.Sexe = "Masculin";
                mem.DateNaissance = Convert.ToDateTime(txtDateCreation.Value); ;
                mem.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                mem.CommuneId = Convert.ToInt32(drpdcom.SelectedValue);
                mem.name = co.GenerateSlug("Administrateurde" + txtNomOrganisation.Value);
                mem.province = drpdprov.SelectedItem.ToString();
                mem.commune = drpdprov.SelectedItem.ToString();
                
                mem.username = txtConfirmEmail.Value;
                mem.motpasse = BCrypt.Net.BCrypt.HashPassword(GenererMotDePasseParDefaut());
                mem.statut = "actif";
                mem.IsActive = 1;
                mem.IsVerified = 1;
                mem.LastLogin = DateTime.Now;
                mem.ResetToken = null;
                mem.ResetTokenExpiry = null;
                objrl.AfficherDetails("membre", rl);
                mem.RoleMembreID = rl.RoleMembreID;

                objmem.Ajouter(mem);


            }
            catch (Exception ex)
            {
                // logguer l'exception pour le debug
                System.Diagnostics.Debug.WriteLine("Erreur : " + ex.Message);
            }
        }

        void NouveauMembreOrganisation()
        {
            try
            {
                //objmem.AfficherDetails(com.GenerateSlug("Administrateurde" + txtnm.Value), memb,0);
                //if (memb.name == com.GenerateSlug("Administrateurde" + txtnm.Value))
                //{
                //    ShowAlert("Votre organisation est deja enregistree. Veuillez consulter votre email pour avoir plus d'informations.");
                //    return;
                //}

                objmem.AfficherDetails(co.GenerateSlug("Administrateurde" + txtNomOrganisation.Value), mem, 0);
                objOrganisation.AfficherDetails(co.GenerateSlug(txtNomOrganisation.Value), org);
                memorg.MembreId = mem.MembreId;
                memorg.OrganisationId = org.OrganisationId;
                memorg.Poste = "Administrateur de l'organisation";
                memorg.DateAdhesion = DateTime.Now;
                memorg.Name = co.GenerateSlug("Administrateurde" + txtNomOrganisation.Value);
                memorg.Statut = "actif";

                objrl.AfficherDetails("membre", rl);
                memorg.RoleMembreID = rl.RoleMembreID;

                objmemorg.Ajouter(memorg);
            }
            catch (Exception ex)
            {
                // logguer ou afficher les erreurs pour le débogage
                System.Diagnostics.Debug.WriteLine("Erreur dans MembreOrganisation : " + ex.Message);
            }
        }
        void ajourdomainecoop()
        {
            try
            {
                int dc = GridView1.Rows.Count;
                if (dc > 0)
                {
                    foreach (GridViewRow row in this.GridView1.Rows)
                    {
                        if (row.RowType == DataControlRowType.DataRow)
                        {
                            objOrganisation.AfficherDetails(co.GenerateSlug(txtNomOrganisation.Value), org);
                            index = org.OrganisationId;
                            actco.OrganisationId = index;
                            objdom.AfficherDetails(row.Cells[1].Text, domai, 1);
                            actco.DomaineInterventionId = Convert.ToInt32(domai.DomaineInterventionId);
                            actco.Description = "";
                            actco.statut = "actif";
                            actco.name = co.GenerateSlug(row.Cells[1].Text + txtNomOrganisation.Value);

                            objactco.Ajouter(actco);

                        }
                    }

                }

            }
            catch (Exception ex)
            { // logguer ou afficher les erreurs pour le débogage
                System.Diagnostics.Debug.WriteLine("Erreur dans Domaines d'Intervention de l'organisation : " + ex.Message);

            }

        }

        protected void btnajoutdom_Click(object sender, EventArgs e)
        {
            try
            {
                if (drpddomai.SelectedValue == "-1" || drpddomai.SelectedValue == "0")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    dom.Visible = true;
                    int i = GridView1.Rows.Count;
                    dat = (DataTable)ViewState["Record"];
                    dat.Rows.Add(drpddomai.SelectedValue, drpddomai.SelectedItem);
                    GridView1.DataSource = dat;
                    GridView1.DataBind();

                }

            }
            catch (Exception ec)
            {

            }
        }

        protected void btnvider_Click(object sender, EventArgs e)
        {
            dom.Visible = true;
            dat = (DataTable)ViewState["Record"];
            dat.Rows.Clear();
            GridView1.DataSource = dat;
            GridView1.DataBind();
        }
        public void adCat()
        {
            try
            {
                if (txtnmdom.Value == "")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    domai.Libelle = txtnmdom.Value;
                    domai.name = co.GenerateSlug(txtnmdom.Value);


                    info = objdom.Ajouter(domai);
                    if (info == 1)
                    {
                        objdom.ChargerDomainedisponible(drpddomai);
                        ajourdom.Visible = false;
                        dom.Visible = false;
                    }
                    else
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Ce domaine Existe deja')</script>");


                    }


                }

            }
            catch (Exception e)
            {

            }

            //LabelMsg.Text = obj.ajouterProvince(prov).ToString();
        }

        public void remplissagedomaineorganisation()
        {
            try
            {
                if (drpddomai.SelectedValue == "-1" || drpddomai.SelectedValue == "0")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Renseignez votre domaine ')</script>");
                }
                else
                {
                    dom.Visible = true;
                    int i = GridView1.Rows.Count;
                    dat = (DataTable)ViewState["Record"];
                    dat.Rows.Add(i = i + 1, drpddomai.SelectedItem);
                    GridView1.DataSource = dat;
                    GridView1.DataBind();

                }

            }
            catch (Exception ec)
            {

            }
        }
        protected void btnannuldom_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = true;
            dat = (DataTable)ViewState["Record"];
            dat.Rows.Clear();
            GridView1.DataSource = dat;
            GridView1.DataBind();
        }
        protected void ajoudom_ServerClick(object sender, EventArgs e)
        {
            dom.Visible = false;
            ajourdom.Visible = true;
        }

        protected void btnajoutnouvdom_ServerClick(object sender, EventArgs e)
        {
            adCat();
        }

        protected void drpddomai_SelectedIndexChanged(object sender, EventArgs e)
        {
            dom.Visible = true;
            remplissagedomaineorganisation();
        }
        protected void btnajourdom_ServerClick(object sender, EventArgs e)
        {
            remplissagedomaineorganisation();
        }


        private void ChargerTypesOrganisation()
        {
            try
            {
                objTypeOrganisation.ChargerTypesDisponibles(drpdtype);
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Erreur lors du chargement des types d'organisation: " + ex.Message;
            }
        }

        private void ChargerProvinces()
        {
            try
            {
                objProvince.chargerProvince(drpdprov);
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Erreur lors du chargement des provinces: " + ex.Message;
            }
        }

        protected void drpdprov_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (drpdprov.SelectedValue!=null && drpdprov.SelectedValue!="-1")
            {
                objCommune.chargerCommune(drpdcom, Convert.ToInt32(drpdprov.SelectedValue));
            }

           
        }

        protected void btnEnregistrer_Click(object sender, EventArgs e)
        {
            // Vérifier la validation côté serveur
            if (!Page.IsValid)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Veuillez corriger les erreurs avant de continuer.";
                return;
            }

            if (organisationId == null)
            {
                AjouterOrganisation();
            }
            else
            {
                ModifierOrganisation();
            }
        }

        private string UploadImage()
        {
            if (fileupd.HasFile)
            {
                string fileName = Path.GetFileName(fileupd.FileName);
                string extension = Path.GetExtension(fileName).ToLower();

                string[] validExtensions = { ".png", ".jpeg", ".gif", ".jpg", ".jfif" };

                if (!validExtensions.Contains(extension))
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "La photo doit avoir ces extensions : .png, .jpg, .gif, .jpeg, .jfif. ";

                    return null;
                }

                if (fileupd.PostedFile.ContentLength > 104857600)
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Votre photo est trop volumineuse. Elle doit faire moins de 100MB.";

                    return null;
                }

                string path = Server.MapPath("~/images/") + fileName;
                fileupd.SaveAs(path);
                imge = fileName;
               
            }
            else
            {
                if(nsco==null)
                {
                    imge="No Image";
                }
                else
                {
                    objOrganisation.AfficherDetails(nsco, org);
                   
                    imge = org.Logo;

                }
            }
            return imge;
                
        }


        private void AjouterOrganisation()
        {
            try
            {
                // Validation des champs requis
                if (
                    drpdtype.SelectedValue == "-1" ||
                    drpdexiste.SelectedValue == "-1" ||
                    string.IsNullOrWhiteSpace(txtNomOrganisation.Value) ||
                    string.IsNullOrWhiteSpace(txtTelephone.Value) ||
                    drpdprov.SelectedValue == "-1" ||
                    drpdcom.SelectedValue == "-1" ||
                    string.IsNullOrWhiteSpace(txtEmail.Value) ||
                    string.IsNullOrWhiteSpace(txtConfirmEmail.Value))
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Renseignez au moins les champs avec *";
                   
                    return;
                }
                // Vérification de l'égalité des emails
                if (txtEmail.Value.Trim() != txtConfirmEmail.Value.Trim())
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "L’adresse e-mail et sa confirmation doivent être identiques";

                    return;
                }

                objOrganisation.AfficherDetails(co.GenerateSlug(txtNomOrganisation.Value), org);
                if (org.name == co.GenerateSlug(txtNomOrganisation.Value))
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Votre organisation est deja enregistree.";

                    return;
                }


                // Préparation des données
                com.Nom = txtNomOrganisation.Value;
                com.TypeOrganisationId = Convert.ToInt32(drpdtype.SelectedValue);
                com.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                com.CommuneId = Convert.ToInt32(drpdcom.SelectedValue);
                com.Email = txtConfirmEmail.Value;
                com.Telephone = txtTelephone.Value;
                com.SiteWeb = "";
                com.Logo = UploadImage();
                com.RS = "organisation";
                com.RC = "";
                com.RC_doc = "No Doc";
                com.NIF = "";
                com.NIF_doc = "NIF Doc";
                com.facebook = "";
                com.twitter = "";
                com.instagramme = "";
                com.linkedin = "";
                com.youtube = "";
                com.province = drpdprov.SelectedItem.ToString();
                com.commune = drpdcom.SelectedItem.ToString();
                com.Description = txtdescription.Value;
                com.Vision = "";
                com.Mission = "";
                com.NbreHomme = 0;
                com.NbreFemme = 0;
                com.Enregistre = drpdexiste.SelectedValue;
                com.DateCreation = Convert.ToDateTime(txtDateCreation.Value);
                com.Statut = "actif";
                com.name = co.GenerateSlug(txtNomOrganisation.Value);
                com.sigle = string.IsNullOrWhiteSpace(txtSigle.Value) ? txtNomOrganisation.Value : txtSigle.Value;
                com.Adresse = txtAdresse.Value;


                // Ajouter l'organisation à la base de données
                info = objOrganisation.Ajouter(com);
                if (info == 1)
                {
                    int dc = GridView1.Rows.Count;
                    if (dc > 0) ajourdomainecoop();

                    NouveauMembre();
                    NouveauMembreOrganisation();


                    SendConfirmationEmail(txtConfirmEmail.Value, txtNomOrganisation.Value);

                    div_msg_succes.Visible = true;
                    msg_succes.Text = "L'organisation a été enregistrée avec succès";

                    // Réinitialiser le formulaire
                    ReinitialiserFormulaire();

                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Erreur lors de l'enregistrement de l'organisation";
                }
            
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Une erreur s'est produite: " + ex.Message;
            }
        }

        private void ModifierOrganisation()
        {
            try
            {
                // Validation des champs requis
                if (
                    drpdtype.SelectedValue == "-1" ||
                    drpdexiste.SelectedValue == "-1" ||
                    string.IsNullOrWhiteSpace(txtNomOrganisation.Value) ||
                    string.IsNullOrWhiteSpace(txtTelephone.Value) ||
                    drpdprov.SelectedValue == "-1" ||
                    drpdcom.SelectedValue == "-1" ||
                    string.IsNullOrWhiteSpace(txtEmail.Value) ||
                    string.IsNullOrWhiteSpace(txtConfirmEmail.Value))
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "Renseignez au moins les champs avec *";

                    return;
                }
                // Vérification de l'égalité des emails
                if (txtEmail.Value.Trim() != txtConfirmEmail.Value.Trim())
                {
                    div_msg_error.Visible = true;
                    msg_error.Text = "L’adresse e-mail et sa confirmation doivent être identiques";

                    return;
                }

                // Préparation des données
                com.Nom = txtNomOrganisation.Value;
                com.TypeOrganisationId = Convert.ToInt32(drpdtype.SelectedValue);
                com.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                com.CommuneId = Convert.ToInt32(drpdcom.SelectedValue);
                com.Email = txtConfirmEmail.Value;
                com.Telephone = txtTelephone.Value;
                com.Description = txtdescription.Value;
                com.RS = "organisation";

                com.Logo = UploadImage();
                com.province = drpdprov.SelectedItem.ToString();
                com.commune = drpdcom.SelectedItem.ToString();
                com.Enregistre = drpdexiste.SelectedValue;
                com.DateCreation = Convert.ToDateTime(txtDateCreation.Value);
                com.Statut = drpdstatut.SelectedValue;
                com.name = co.GenerateSlug(txtNomOrganisation.Value);
                com.sigle = string.IsNullOrWhiteSpace(txtSigle.Value) ? txtNomOrganisation.Value : txtSigle.Value;
                com.Adresse = txtAdresse.Value;
                com.Latitude = 0;
                com.Longitude = 0;

                // Modifier l'organisation dans la base de données
                objOrganisation.AfficherDetails(nsco, org);
                info = objOrganisation.Modifier(com,Convert.ToInt64( org.OrganisationId));

                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    msg_succes.Text = "L'organisation a été modifiée avec succès";
                    Response.Redirect("~/file/listorganisation.aspx");
                }
            else
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Erreur lors de la modification de l'organisation";
            }
        }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Une erreur s'est produite: " + ex.Message;
            }
        }

        private void AfficherDetails()
        {
            try
            {
                if (nsco != null)
                {
                    objOrganisation.AfficherDetails(nsco, organisationClass);

                    if (organisationClass.OrganisationId > 0)
                    {
                        txtNomOrganisation.Value = organisationClass.Nom;
                        txtSigle.Value = organisationClass.sigle;
                        drpdexiste.SelectedValue = organisationClass.Enregistre;

                        if (organisationClass.TypeOrganisationId > 0)
                            drpdtype.SelectedValue = organisationClass.TypeOrganisationId.ToString();

                        txtEmail.Value = organisationClass.Email;
                        txtConfirmEmail.Value = organisationClass.Email;
                        txtTelephone.Value = organisationClass.Telephone;
                        txtdescription.Value=organisationClass.Description;
                        //chkEnregistree.Checked = organisationClass.Enregistre;
                        statu.Visible = true;

                        if (organisationClass.ProvinceId > 0)
                        {
                            drpdprov.SelectedValue = organisationClass.ProvinceId.ToString();
                            objCommune.chargerCommune(drpdcom);
                            if (organisationClass.CommuneId > 0)
                                drpdcom.SelectedValue = organisationClass.CommuneId.ToString();
                        }

                        txtAdresse.Value = organisationClass.Adresse;

                        if (organisationClass.DateCreation.HasValue)
                            txtDateCreation.Value = organisationClass.DateCreation.Value.ToString("yyyy-MM-dd");

                        if (!string.IsNullOrEmpty(organisationClass.Statut))
                            drpdstatut.SelectedValue = organisationClass.Statut;

                    }
                    else
                    {
                        div_msg_error.Visible = true;
                        msg_error.Text = "L'organisation demandée n'existe pas";
                    }
                }
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Erreur lors du chargement des données: " + ex.Message;
            }
        }

        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            // Vérifier la validation côté serveur
            if (!Page.IsValid)
            {
                div_msg_error.Visible = true;
                msg_error.Text = "Veuillez corriger les erreurs avant de continuer.";
                return;
            }

            if (nsco == null)
            {
                AjouterOrganisation();
            }
            else
            {
                ModifierOrganisation();
            }
        }

        private void ReinitialiserFormulaire()
        {
            txtNomOrganisation.Value = ""; ;
            txtSigle.Value = ""; ;
            drpdtype.SelectedValue = "-1";
            txtEmail.Value = "";
            txtConfirmEmail.Value = "";
            txtTelephone.Value = ""; ;
            drpdprov.SelectedValue = "-1";
            drpddomai.SelectedValue = "-1";
            drpdcom.SelectedValue = "-1";
            txtAdresse.Value = ""; ;
            txtDateCreation.Value = ""; ;
            drpdstatut.SelectedValue = "-1";
            drpdtype.SelectedValue= "-1";
            txtAdresse.Value = "";
            txtDateCreation.Value = "";
            dom.Visible = false;
            txtdescription.Value = "";
        }

    }
}