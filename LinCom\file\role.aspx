﻿<%@ Page Title="Roles des utilisateurs"  ValidateRequest="false"  Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="role.aspx.cs" Inherits="LinCom.file.role" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
   <div class="breadcomb-area">
     <div class="container">
         <div class="row">
             <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                 <div class="breadcomb-list">
                     <div class="row">
                         <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                             <div class="breadcomb-wp">

                                 <div class="breadcomb-ctn">
                                     <h2>Roles des utilisateurs</h2>
                                 </div>
                             </div>
                         </div>
                         <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                             <div class="breadcomb-report">

                                 <a data-toggle="tooltip" data-placement="left" href="listroles.aspx" title="Clique sur ce button pour visualiser la liste des Roles des utilisateurs" class="btn">Liste des Roles des utilisateurs</a>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
     </div>
 </div>
 <!-- Breadcomb area End-->
 <!-- Form Element area Start-->
 <div class="form-element-area">
     <div class="container">
         <div class="row">
             <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                 <div class="form-element-list">

                     <div class="cmp-tb-hd bcs-hd">
                         <p style="color: red">Remplissez les champs obligatoires avec asterix</p>
                     </div>
                     <div class="alert-list">
                         <div class="alert alert-success alert-dismissible" role="alert" runat="server" id="div_msg_succes">
                             <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                             <span runat="server" id="msg_succes">Enregistrement réussi</span>
                         </div>
                         <div class="alert alert-danger alert-dismissible" role="alert" runat="server" id="div_msg_error">
                             <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                             <span runat="server" id="msg_error">Enregistrement échoué</span>
                         </div>
                     </div>
                     <div class="row">
                        
                         <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                             <div class="form-group ic-cmp-int form-elet-mg">
                                 <div class="form-ic-cmp">
                                     <i class="notika-icon notika-edit"></i>
                                 </div>
                                 <div class="nk-int-st">
                                     <label>Nom du Role d'Utilisateur *</label>
                                     <input type="text" runat="server" id="txtnm" class="form-control" placeholder="Nom du Role d'Utilisateur *">
                                 </div>

                             </div>
                         </div>
                          <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
     <div class="form-group ic-cmp-int">
         <div class="form-ic-cmp">
             <i class="notika-icon notika-edit"></i>
         </div>
         <div class="nk-int-st">
             <label>Type d'utilisateur *</label>
             <asp:DropDownList class="form-control" ID="drpdtype" runat="server">

                 <asp:ListItem Value="-1">Selectionner le type</asp:ListItem>
                 <asp:ListItem Value="organisation">Administrateur</asp:ListItem>
                 <asp:ListItem Value="membre">Membre</asp:ListItem>
               
             </asp:DropDownList>
         </div>
     </div>
 </div>
                         <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                             <div class="form-group ic-cmp-int">
                                 <div class="form-ic-cmp">
                                     <i class="notika-icon notika-edit"></i>
                                 </div>
                                 <div class="nk-int-st">
                                     <label>Statut *</label>
                                     <asp:DropDownList class="form-control" ID="drpdstatut" runat="server">

                                         <asp:ListItem Value="-1">Selectionner le statut</asp:ListItem>
                                         <asp:ListItem Value="actif">Actif</asp:ListItem>
                                         <asp:ListItem Value="inactif">Inactif</asp:ListItem>
                                       
                                     </asp:DropDownList>
                                 </div>
                             </div>
                         </div>

                        
                         <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                             <div class="form-group ic-cmp-int">
                                 <div class="form-ic-cmp">
                                     <i class="notika-icon notika-form"></i>
                                 </div>
                                 <div class="nk-int-st">
                                     <label>Description du Role d'Utilisateur </label>
                                     <div>

                                         <textarea class="html-editor" runat="server" id="txtdescription" rows="10"></textarea>

                                     </div>

                                 </div>
                             </div>
                         </div>
                     </div>

                 </div>
             </div>
         </div>

         <div class="row">
             <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                 <div class="form-element-list">

                     <div class="row">

                         <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                             <button type="button" runat="server" id="btnEnregistrer" onserverclick="btn_enreg_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
                         </div>
                     </div>

                 </div>
             </div>
         </div>
     </div>
 </div>
 <!-- Form Element area End-->
</asp:Content>
