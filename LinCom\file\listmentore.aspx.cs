using LinCom.Class;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listmentore : System.Web.UI.Page
    {
        private int info;
        Mentore_Class mentoreObj = new Mentore_Class();
        IMentore objMentore = new MentoreImp();
        ICommonCode co = new CommonCode();

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();

        static long ide, idorg;
        static int rolid;
        static string nameorg;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {
                    ide = Convert.ToInt64(idperso.Value);
                    rolid = Convert.ToInt32(role.Value);
                }
                else
                {
                    Response.Redirect("~/login.aspx");
                }

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                getDataGDV();
            }
        }

        public void getDataGDV()
        {
            try
            {
                // Charger tous les mentorés avec le statut par défaut
                objMentore.ChargerMentores(gdv, "");
            }
            catch (Exception ex)
            {
                lblMessage.Text = "Erreur lors du chargement des mentorés : " + ex.Message;
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }

        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            
            if (e.CommandName == "view")
            {
                Response.Redirect("~/file/mentore.aspx?id=" + index);
            }
            else if (e.CommandName == "delete")
            {
                try
                {
                    int mentoreId = Convert.ToInt32(index);
                    objMentore.AfficherDetails(mentoreId, mentoreObj);
                    info = objMentore.Supprimer(mentoreId);

                    if (info == 1)
                    {
                        lblMessage.Text = "Mentoré supprimé avec succès";
                        lblMessage.CssClass = "alert alert-success";
                        lblMessage.Visible = true;
                        getDataGDV(); // Recharger la liste
                    }
                    else
                    {
                        lblMessage.Text = "Erreur lors de la suppression du mentoré";
                        lblMessage.CssClass = "alert alert-danger";
                        lblMessage.Visible = true;
                    }
                }
                catch (SqlException ex)
                {
                    lblMessage.Text = "Erreur SQL lors de la suppression : " + ex.Message;
                    lblMessage.CssClass = "alert alert-danger";
                    lblMessage.Visible = true;
                    System.Diagnostics.Debug.WriteLine("Erreur SQL dans gdv_RowCommand: " + ex.ToString());
                }
                catch (Exception ex)
                {
                    lblMessage.Text = "Erreur lors de la suppression : " + ex.Message;
                    lblMessage.CssClass = "alert alert-danger";
                    lblMessage.Visible = true;
                    System.Diagnostics.Debug.WriteLine("Erreur dans gdv_RowCommand: " + ex.ToString());
                }
            }
        }
    }
}
