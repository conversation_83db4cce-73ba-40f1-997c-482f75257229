<%@ Page Title="" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="mentore.aspx.cs" Inherits="LinCom.file.mentore" ValidateRequest="false" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">
                                    <div class="breadcomb-ctn">
                                        <h2>Gestion Mentoré</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">
                                    <a data-toggle="tooltip" data-placement="left" href="listmentore.aspx" title="Liste des mentorés" class="btn">Liste des Mentorés</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <div id="div_msg_success" runat="server" visible="false" class="alert alert-success alert-dismissible fade show" role="alert">
        <span id="msg_success" runat="server"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    
    <div id="div_msg_error" runat="server" visible="false" class="alert alert-danger alert-dismissible fade show" role="alert">
        <span id="msg_error" runat="server"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Form Element area Start-->
    <div class="form-element-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">
                        <div class="cmp-tb-hd bcs-hd">
                            <p style="color:red">Remplissez les champs obligatoires avec astérisque (*)</p>
                        </div>

                        <div class="row">
                                <!-- Membre -->
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                    <div class="form-group ic-cmp-int form-elet-mg">
                                        <div class="form-ic-cmp">
                                            <i class="notika-icon notika-edit"></i>
                                        </div>
                                        <div class="nk-int-st">
                                            <asp:DropDownList class="form-control" ID="drpdMembre" runat="server">
                                                <asp:ListItem Value="-1">Sélectionner le membre</asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                </div>

                                <!-- Programme de Mentorat -->
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                    <div class="form-group ic-cmp-int">
                                        <div class="form-ic-cmp">
                                            <i class="notika-icon notika-edit"></i>
                                        </div>
                                        <div class="nk-int-st">
                                            <asp:DropDownList class="form-control" ID="drpdProgramme" runat="server">
                                                <asp:ListItem Value="-1">Sélectionner le programme</asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                </div>

                                <!-- Statut -->
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                    <div class="form-group ic-cmp-int">
                                        <div class="form-ic-cmp">
                                            <i class="notika-icon notika-edit"></i>
                                        </div>
                                        <div class="nk-int-st">
                                            <asp:DropDownList class="form-control" ID="drpdStatut" runat="server">
                                                <asp:ListItem Value="-1">Sélectionner le statut</asp:ListItem>
                                                <asp:ListItem Value="actif">Actif</asp:ListItem>
                                                <asp:ListItem Value="inactif">Inactif</asp:ListItem>
                                                <asp:ListItem Value="en_attente">En attente</asp:ListItem>
                                            </asp:DropDownList>
                                        </div>
                                    </div>
                                </div>

                                <!-- Boutons -->
                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                    <div class="payment-adress">
                                        <button type="button" id="btnEnregistrer" runat="server" onserverclick="btnEnregistrer_ServerClick" class="btn btn-success notika-btn-success">
                                            <i class="notika-icon notika-sent"></i> Enregistrer
                                        </button>
                                        <a href="listmentore.aspx" class="btn btn-secondary">
                                            <i class="notika-icon notika-back"></i> Retour à la liste
                                        </a>
                                    </div>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Form Element area End-->
</asp:Content>
