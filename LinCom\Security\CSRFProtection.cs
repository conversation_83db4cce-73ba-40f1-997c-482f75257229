using System;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using System.Web.UI;

namespace LinCom.Security
{
    /// <summary>
    /// Protection contre les attaques CSRF (Cross-Site Request Forgery)
    /// </summary>
    public static class CSRFProtection
    {
        private const string CSRF_TOKEN_KEY = "__CSRFToken";
        private const string CSRF_SESSION_KEY = "__CSRFSession";

        /// <summary>
        /// Génère un token CSRF unique pour la session
        /// </summary>
        public static string GenerateToken(HttpContext context)
        {
            if (context.Session[CSRF_SESSION_KEY] == null)
            {
                // Générer un token unique basé sur la session et un salt aléatoire
                string sessionId = context.Session.SessionID;
                string randomSalt = GenerateRandomString(32);
                string timestamp = DateTime.UtcNow.Ticks.ToString();
                
                string tokenData = $"{sessionId}:{randomSalt}:{timestamp}";
                string token = ComputeHash(tokenData);
                
                context.Session[CSRF_SESSION_KEY] = token;
            }

            return context.Session[CSRF_SESSION_KEY].ToString();
        }

        /// <summary>
        /// Valide un token CSRF
        /// </summary>
        public static bool ValidateToken(HttpContext context, string token)
        {
            if (context.Session[CSRF_SESSION_KEY] == null)
                return false;

            string sessionToken = context.Session[CSRF_SESSION_KEY].ToString();
            
            // Comparaison sécurisée pour éviter les attaques de timing
            return SecureStringCompare(sessionToken, token);
        }

        /// <summary>
        /// Ajoute un token CSRF caché à une page
        /// </summary>
        public static void AddTokenToPage(Page page)
        {
            string token = GenerateToken(HttpContext.Current);
            
            // Créer un champ caché avec le token
            var hiddenField = new HiddenField
            {
                ID = CSRF_TOKEN_KEY,
                Value = token
            };

            // Ajouter le champ au formulaire
            if (page.Form != null)
            {
                page.Form.Controls.Add(hiddenField);
            }
        }

        /// <summary>
        /// Valide le token CSRF depuis une requête POST
        /// </summary>
        public static bool ValidateRequest(HttpContext context)
        {
            if (context.Request.HttpMethod.ToUpper() != "POST")
                return true; // Pas de validation nécessaire pour GET

            string token = context.Request.Form[CSRF_TOKEN_KEY];
            
            if (string.IsNullOrEmpty(token))
                return false;

            return ValidateToken(context, token);
        }

        /// <summary>
        /// Middleware pour valider automatiquement les requêtes POST
        /// </summary>
        public static void ValidateRequestOrThrow(HttpContext context)
        {
            if (!ValidateRequest(context))
            {
                ForumSecurityHelper.LogSecurityIncident("CSRF token validation failed", 
                    GetCurrentUserId(context), 
                    context.Request.UserHostAddress);
                
                throw new HttpException(403, "Requête non autorisée - Token CSRF invalide");
            }
        }

        /// <summary>
        /// Génère une chaîne aléatoire sécurisée
        /// </summary>
        private static string GenerateRandomString(int length)
        {
            using (var rng = new RNGCryptoServiceProvider())
            {
                byte[] randomBytes = new byte[length];
                rng.GetBytes(randomBytes);
                return Convert.ToBase64String(randomBytes);
            }
        }

        /// <summary>
        /// Calcule un hash sécurisé
        /// </summary>
        private static string ComputeHash(string input)
        {
            using (var sha256 = SHA256.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = sha256.ComputeHash(inputBytes);
                return Convert.ToBase64String(hashBytes);
            }
        }

        /// <summary>
        /// Comparaison sécurisée de chaînes (résistante aux attaques de timing)
        /// </summary>
        private static bool SecureStringCompare(string a, string b)
        {
            if (a == null || b == null)
                return false;

            if (a.Length != b.Length)
                return false;

            int result = 0;
            for (int i = 0; i < a.Length; i++)
            {
                result |= a[i] ^ b[i];
            }

            return result == 0;
        }

        /// <summary>
        /// Récupère l'ID de l'utilisateur actuel
        /// </summary>
        private static long? GetCurrentUserId(HttpContext context)
        {
            try
            {
                if (context.Session["MembreId"] != null)
                {
                    return Convert.ToInt64(context.Session["MembreId"]);
                }
            }
            catch
            {
                // Ignore les erreurs de conversion
            }
            return null;
        }
    }

    /// <summary>
    /// Attribut pour marquer les pages nécessitant une protection CSRF
    /// </summary>
    [AttributeUsage(AttributeTargets.Class)]
    public class RequireCSRFTokenAttribute : Attribute
    {
        public bool ValidateOnPostBack { get; set; } = true;
    }

    /// <summary>
    /// Module HTTP pour la validation automatique des tokens CSRF
    /// </summary>
    public class CSRFProtectionModule : IHttpModule
    {
        public void Init(HttpApplication context)
        {
            context.PreRequestHandlerExecute += (sender, e) =>
            {
                var httpContext = HttpContext.Current;
                var page = httpContext.Handler as Page;

                if (page != null)
                {
                    // Vérifier si la page nécessite une protection CSRF
                    var csrfAttribute = Attribute.GetCustomAttribute(page.GetType(), typeof(RequireCSRFTokenAttribute)) as RequireCSRFTokenAttribute;
                    
                    if (csrfAttribute != null && csrfAttribute.ValidateOnPostBack)
                    {
                        page.PreInit += (s, args) =>
                        {
                            // Ajouter le token à la page
                            CSRFProtection.AddTokenToPage(page);
                        };

                        page.PreLoad += (s, args) =>
                        {
                            // Valider le token sur les requêtes POST
                            if (page.IsPostBack)
                            {
                                CSRFProtection.ValidateRequestOrThrow(httpContext);
                            }
                        };
                    }
                }
            };
        }

        public void Dispose()
        {
            // Nettoyage si nécessaire
        }
    }
}
