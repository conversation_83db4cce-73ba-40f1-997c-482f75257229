using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class BadgesImp : IBadges
    {
        int msg;
        Badges p = new Badges();

        public int Add(Badges_Class badge)
        {
            using (Connection con = new Connection())
            {
                p.UserId = badge.UserId;
                p.Name = badge.Name;
                p.Date = DateTime.Now;
                p.Class = badge.Class;
                p.TagBased = badge.TagBased;
                p.Description = badge.Description;
                p.IconClass = badge.IconClass;
                p.Points = badge.Points;
                p.CategoryId = badge.CategoryId;

                try
                {
                    con.Badges.Add(p);
                    if (con.SaveChanges() == 1)
                    {
                        // Notifier l'utilisateur du nouveau badge
                        NotifierNouveauBadge(badge.UserId, badge.Name);
                        
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public void VerifierEtAttribuerBadges(long userId)
        {
            using (Connection con = new Connection())
            {
                // Vérifier les badges de première fois
                VerifierBadgesPremiereFois(userId, "general");
                
                // Vérifier les badges de qualité
                VerifierBadgesQualite(userId);
                
                // Vérifier les badges d'activité
                VerifierBadgesActivite(userId);
                
                // Vérifier les badges basés sur les tags
                VerifierBadgesTags(userId);
                
                // Vérifier les badges de modération
                VerifierBadgesModeration(userId);
                
                // Vérifier les badges de communauté
                VerifierBadgesCommunaute(userId);
            }
        }

        public void VerifierBadgesPourAction(long userId, string action, long? postId = null)
        {
            switch (action.ToLower())
            {
                case "firstpost":
                    VerifierBadgesPremiereFois(userId, "FirstPost");
                    break;
                case "firstanswer":
                    VerifierBadgesPremiereFois(userId, "FirstAnswer");
                    break;
                case "upvote":
                case "downvote":
                    VerifierBadgesQualite(userId);
                    VerifierBadgesModeration(userId);
                    break;
                case "acceptedanswer":
                    VerifierBadgesQualite(userId);
                    VerifierBadgesCommunaute(userId);
                    break;
                case "login":
                    VerifierBadgesActivite(userId);
                    break;
            }
        }

        public void AttribuerBadge(long userId, string badgeName, long? postId = null)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si l'utilisateur possède déjà ce badge
                if (!PossedeBadge(userId, badgeName))
                {
                    // Obtenir la définition du badge
                    var definition = ObtenirDefinitionBadge(badgeName);
                    if (definition != null)
                    {
                        var badge = new Badges_Class
                        {
                            UserId = userId,
                            Name = badgeName,
                            Class = definition.Class,
                            TagBased = definition.TagBased,
                            Description = definition.Description,
                            IconClass = definition.IconClass,
                            Points = definition.Points,
                            CategoryId = null
                        };

                        Add(badge);
                    }
                }
            }
        }

        public bool PossedeBadge(long userId, string badgeName)
        {
            using (Connection con = new Connection())
            {
                return con.Badges.Any(b => b.UserId == userId && b.Name == badgeName);
            }
        }

        public int CompterBadgesUtilisateur(long userId, int classe = 0)
        {
            using (Connection con = new Connection())
            {
                if (classe == 0)
                {
                    return con.Badges.Count(b => b.UserId == userId);
                }
                else
                {
                    return con.Badges.Count(b => b.UserId == userId && b.Class == classe);
                }
            }
        }

        public void ChargerBadgesOr(ListView lv, long? userId = null)
        {
            using (Connection con = new Connection())
            {
                var query = from b in con.Badges
                           join u in con.Membres on b.UserId equals u.MembreId
                           where b.Class == 1 // Or
                           select new
                           {
                               b.BadgeId,
                               b.Name,
                               b.Description,
                               b.Date,
                               b.IconClass,
                               UserName = u.Nom + " " + u.Prenom,
                               u.Reputation,
                               TotalAwarded = con.Badges.Count(bb => bb.Name == b.Name)
                           };

                if (userId.HasValue)
                {
                    query = query.Where(b => b.UserId == userId.Value);
                }

                lv.DataSource = query.OrderByDescending(b => b.Date).ToList();
                lv.DataBind();
            }
        }

        public void ChargerBadgesArgent(ListView lv, long? userId = null)
        {
            using (Connection con = new Connection())
            {
                var query = from b in con.Badges
                           join u in con.Membres on b.UserId equals u.MembreId
                           where b.Class == 2 // Argent
                           select new
                           {
                               b.BadgeId,
                               b.Name,
                               b.Description,
                               b.Date,
                               b.IconClass,
                               UserName = u.Nom + " " + u.Prenom,
                               u.Reputation,
                               TotalAwarded = con.Badges.Count(bb => bb.Name == b.Name)
                           };

                if (userId.HasValue)
                {
                    query = query.Where(b => b.UserId == userId.Value);
                }

                lv.DataSource = query.OrderByDescending(b => b.Date).ToList();
                lv.DataBind();
            }
        }

        public void ChargerBadgesBronze(ListView lv, long? userId = null)
        {
            using (Connection con = new Connection())
            {
                var query = from b in con.Badges
                           join u in con.Membres on b.UserId equals u.MembreId
                           where b.Class == 3 // Bronze
                           select new
                           {
                               b.BadgeId,
                               b.Name,
                               b.Description,
                               b.Date,
                               b.IconClass,
                               UserName = u.Nom + " " + u.Prenom,
                               u.Reputation,
                               TotalAwarded = con.Badges.Count(bb => bb.Name == b.Name)
                           };

                if (userId.HasValue)
                {
                    query = query.Where(b => b.UserId == userId.Value);
                }

                lv.DataSource = query.OrderByDescending(b => b.Date).ToList();
                lv.DataBind();
            }
        }

        public void ChargerBadgesUtilisateur(ListView lv, long userId)
        {
            using (Connection con = new Connection())
            {
                var badges = from b in con.Badges
                            where b.UserId == userId
                            orderby b.Class, b.Date descending
                            select new
                            {
                                b.BadgeId,
                                b.Name,
                                b.Description,
                                b.Date,
                                b.Class,
                                b.IconClass,
                                ClassName = b.Class == 1 ? "Or" : b.Class == 2 ? "Argent" : "Bronze",
                                ClassColor = b.Class == 1 ? "#FFD700" : b.Class == 2 ? "#C0C0C0" : "#CD7F32",
                                TotalAwarded = con.Badges.Count(bb => bb.Name == b.Name),
                                IsRare = con.Badges.Count(bb => bb.Name == b.Name) < 100
                            };

                lv.DataSource = badges.ToList();
                lv.DataBind();
            }
        }

        public void VerifierBadgesPremiereFois(long userId, string action)
        {
            using (Connection con = new Connection())
            {
                switch (action)
                {
                    case "FirstPost":
                        var premierPost = con.SujetForums.Where(s => s.OwnerUserId == userId && s.PostTypeId == 1).FirstOrDefault();
                        if (premierPost != null && !PossedeBadge(userId, "First Post"))
                        {
                            AttribuerBadge(userId, "First Post");
                        }
                        break;

                    case "FirstAnswer":
                        var premiereReponse = con.SujetForums.Where(s => s.OwnerUserId == userId && s.PostTypeId == 2).FirstOrDefault();
                        if (premiereReponse != null && !PossedeBadge(userId, "First Answer"))
                        {
                            AttribuerBadge(userId, "First Answer");
                        }
                        break;

                    case "general":
                        // Vérifier tous les badges de première fois
                        VerifierBadgesPremiereFois(userId, "FirstPost");
                        VerifierBadgesPremiereFois(userId, "FirstAnswer");
                        break;
                }
            }
        }

        public void VerifierBadgesQualite(long userId)
        {
            using (Connection con = new Connection())
            {
                // Nice Answer (10+ upvotes sur une réponse)
                var bonnesReponses = con.SujetForums.Where(s => s.OwnerUserId == userId && s.PostTypeId == 2 && s.Score >= 10).Count();
                if (bonnesReponses >= 1 && !PossedeBadge(userId, "Nice Answer"))
                {
                    AttribuerBadge(userId, "Nice Answer");
                }

                // Good Answer (25+ upvotes sur une réponse)
                var excellentesReponses = con.SujetForums.Where(s => s.OwnerUserId == userId && s.PostTypeId == 2 && s.Score >= 25).Count();
                if (excellentesReponses >= 1 && !PossedeBadge(userId, "Good Answer"))
                {
                    AttribuerBadge(userId, "Good Answer");
                }

                // Great Answer (100+ upvotes sur une réponse)
                var superbesReponses = con.SujetForums.Where(s => s.OwnerUserId == userId && s.PostTypeId == 2 && s.Score >= 100).Count();
                if (superbesReponses >= 1 && !PossedeBadge(userId, "Great Answer"))
                {
                    AttribuerBadge(userId, "Great Answer");
                }

                // Teacher (Première réponse acceptée avec score 1+)
                var reponsesAcceptees = con.SujetForums.Where(s => s.OwnerUserId == userId && s.PostTypeId == 2 && s.Score >= 1)
                                                       .Join(con.SujetForums, r => r.SujetForumId, q => q.AcceptedAnswerId, (r, q) => r)
                                                       .Count();
                if (reponsesAcceptees >= 1 && !PossedeBadge(userId, "Teacher"))
                {
                    AttribuerBadge(userId, "Teacher");
                }
            }
        }

        public void VerifierBadgesActivite(long userId)
        {
            using (Connection con = new Connection())
            {
                // Enthusiast (30 jours consécutifs de connexion)
                // Cette logique nécessiterait un tracking des connexions
                
                // Supporter (Premier vote positif)
                var premierVotePositif = con.Votes.Where(v => v.UserId == userId && v.VoteTypeId == 2).FirstOrDefault();
                if (premierVotePositif != null && !PossedeBadge(userId, "Supporter"))
                {
                    AttribuerBadge(userId, "Supporter");
                }

                // Critic (Premier vote négatif)
                var premierVoteNegatif = con.Votes.Where(v => v.UserId == userId && v.VoteTypeId == 3).FirstOrDefault();
                if (premierVoteNegatif != null && !PossedeBadge(userId, "Critic"))
                {
                    AttribuerBadge(userId, "Critic");
                }
            }
        }

        public void VerifierBadgesTags(long userId)
        {
            using (Connection con = new Connection())
            {
                // Obtenir les tags les plus utilisés par l'utilisateur
                var tagsUtilisateur = from pt in con.PostTags
                                     join s in con.SujetForums on pt.PostId equals s.SujetForumId
                                     join t in con.Tags on pt.TagId equals t.TagId
                                     where s.OwnerUserId == userId
                                     group new { pt, s, t } by new { t.TagId, t.TagName } into g
                                     select new
                                     {
                                         TagId = g.Key.TagId,
                                         TagName = g.Key.TagName,
                                         PostCount = g.Count(),
                                         Score = g.Sum(x => x.s.Score),
                                         AnswerCount = g.Count(x => x.s.PostTypeId == 2)
                                     };

                foreach (var tag in tagsUtilisateur)
                {
                    // Bronze: 20 réponses avec score 0+ dans un tag
                    if (tag.AnswerCount >= 20 && !PossedeBadge(userId, $"{tag.TagName} Bronze"))
                    {
                        AttribuerBadgeTag(userId, tag.TagName, 3); // Bronze
                    }

                    // Silver: 80 réponses avec score 25+ dans un tag
                    var reponsesQualite = con.SujetForums.Join(con.PostTags, s => s.SujetForumId, pt => pt.PostId, (s, pt) => new { s, pt })
                                                         .Where(x => x.s.OwnerUserId == userId && x.s.PostTypeId == 2 && x.s.Score >= 25 && x.pt.TagId == tag.TagId)
                                                         .Count();
                    if (reponsesQualite >= 80 && !PossedeBadge(userId, $"{tag.TagName} Silver"))
                    {
                        AttribuerBadgeTag(userId, tag.TagName, 2); // Silver
                    }

                    // Gold: 200 réponses avec score 100+ dans un tag
                    var reponsesExcellentes = con.SujetForums.Join(con.PostTags, s => s.SujetForumId, pt => pt.PostId, (s, pt) => new { s, pt })
                                                             .Where(x => x.s.OwnerUserId == userId && x.s.PostTypeId == 2 && x.s.Score >= 100 && x.pt.TagId == tag.TagId)
                                                             .Count();
                    if (reponsesExcellentes >= 200 && !PossedeBadge(userId, $"{tag.TagName} Gold"))
                    {
                        AttribuerBadgeTag(userId, tag.TagName, 1); // Gold
                    }
                }
            }
        }

        public void AttribuerBadgeTag(long userId, string tagName, int niveau)
        {
            string badgeName = $"{tagName} " + (niveau == 1 ? "Gold" : niveau == 2 ? "Silver" : "Bronze");
            
            var badge = new Badges_Class
            {
                UserId = userId,
                Name = badgeName,
                Class = niveau,
                TagBased = true,
                Description = $"Badge {(niveau == 1 ? "Or" : niveau == 2 ? "Argent" : "Bronze")} pour expertise en {tagName}",
                IconClass = $"fa-tag",
                Points = niveau == 1 ? 100 : niveau == 2 ? 50 : 25
            };

            Add(badge);
        }

        public void NotifierNouveauBadge(long userId, string badgeName)
        {
            using (Connection con = new Connection())
            {
                var notification = new Notification
                {
                    UserId = userId,
                    Type = "NewBadge",
                    Title = "Nouveau badge obtenu !",
                    Message = $"Félicitations ! Vous avez obtenu le badge '{badgeName}'",
                    IsRead = false,
                    CreatedDate = DateTime.Now
                };

                con.Notifications.Add(notification);
                con.SaveChanges();
            }
        }

        public BadgeDefinitions_Class ObtenirDefinitionBadge(string badgeName)
        {
            // Définitions des badges par défaut
            var definitions = new Dictionary<string, BadgeDefinitions_Class>
            {
                ["First Post"] = new BadgeDefinitions_Class { Name = "First Post", Class = 3, Description = "Première question postée", Points = 1 },
                ["First Answer"] = new BadgeDefinitions_Class { Name = "First Answer", Class = 3, Description = "Première réponse postée", Points = 1 },
                ["Nice Answer"] = new BadgeDefinitions_Class { Name = "Nice Answer", Class = 3, Description = "Réponse avec 10+ votes positifs", Points = 10 },
                ["Good Answer"] = new BadgeDefinitions_Class { Name = "Good Answer", Class = 2, Description = "Réponse avec 25+ votes positifs", Points = 25 },
                ["Great Answer"] = new BadgeDefinitions_Class { Name = "Great Answer", Class = 1, Description = "Réponse avec 100+ votes positifs", Points = 100 },
                ["Teacher"] = new BadgeDefinitions_Class { Name = "Teacher", Class = 3, Description = "Première réponse acceptée avec score 1+", Points = 5 },
                ["Supporter"] = new BadgeDefinitions_Class { Name = "Supporter", Class = 3, Description = "Premier vote positif", Points = 1 },
                ["Critic"] = new BadgeDefinitions_Class { Name = "Critic", Class = 3, Description = "Premier vote négatif", Points = 1 }
            };

            return definitions.ContainsKey(badgeName) ? definitions[badgeName] : null;
        }

        public void Chargement_GDV(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var obj = (from b in con.Badges
                           join u in con.Membres on b.UserId equals u.MembreId
                           select new
                           {
                               BadgeId = b.BadgeId,
                               Name = b.Name,
                               UserName = u.Nom + " " + u.Prenom,
                               Class = b.Class == 1 ? "Or" : b.Class == 2 ? "Argent" : "Bronze",
                               Date = b.Date,
                               Description = b.Description
                           }).OrderByDescending(b => b.Date).ToList();

                gdv.DataSource = obj;
                gdv.DataBind();
            }
        }

        public void AfficherDetails(long badgeId, Badges_Class badge) { throw new NotImplementedException(); }
        public int Edit(Badges_Class badge, long badgeId) { throw new NotImplementedException(); }
        public int Supprimer(long badgeId) { throw new NotImplementedException(); }
        public void Search(GridView gdv, string searchTerm) { throw new NotImplementedException(); }
        public int Count() { throw new NotImplementedException(); }

        // Méthodes supplémentaires à implémenter...
        public void ChargerTousBadges(ListView lv, long? userId = null)
        {
            using (Connection con = new Connection())
            {
                var query = from b in con.Badges
                           join u in con.Membres on b.UserId equals u.MembreId
                           select new
                           {
                               b.BadgeId,
                               b.Name,
                               b.Description,
                               b.Date,
                               b.Class,
                               b.IconClass,
                               UserName = u.Nom + " " + u.Prenom,
                               u.Reputation,
                               ClassName = b.Class == 1 ? "Or" : b.Class == 2 ? "Argent" : "Bronze",
                               ClassColor = b.Class == 1 ? "#FFD700" : b.Class == 2 ? "#C0C0C0" : "#CD7F32"
                           };

                if (userId.HasValue)
                {
                    query = query.Where(b => b.UserId == userId.Value);
                }

                lv.DataSource = query.OrderBy(b => b.Class).ThenByDescending(b => b.Date).ToList();
                lv.DataBind();
            }
        }
        public List<Badges_Class> ObtenirBadgesTags(long userId) { throw new NotImplementedException(); }
        public void ChargerProgressionBadgesTags(ListView lv, long userId) { throw new NotImplementedException(); }
        public void ChargerTopBadges(ListView lv, int limite = 20) { throw new NotImplementedException(); }
        public void ChargerBadgesRares(ListView lv, int limite = 10) { throw new NotImplementedException(); }
        public void ChargerBadgesRecents(ListView lv, int limite = 20) { throw new NotImplementedException(); }
        public void ChargerStatistiquesBadges(out int total, out int or, out int argent, out int bronze) { throw new NotImplementedException(); }
        public void ChargerDefinitionsBadges(ListView lv) { throw new NotImplementedException(); }
        public void AjouterDefinitionBadge(BadgeDefinitions_Class definition) { throw new NotImplementedException(); }
        public void ModifierDefinitionBadge(BadgeDefinitions_Class definition) { throw new NotImplementedException(); }
        public void SupprimerDefinitionBadge(int definitionId) { throw new NotImplementedException(); }
        public void ChargerProgressionUtilisateur(ListView lv, long userId) { throw new NotImplementedException(); }
        public void CalculerProgressionBadge(long userId, string badgeName, out int actuel, out int requis) { throw new NotImplementedException(); }
        public List<string> ObtenirBadgesProches(long userId, int limite = 5) { throw new NotImplementedException(); }
        public void ChargerCriteresManquants(ListView lv, long userId, string badgeName) { throw new NotImplementedException(); }
        public void VerifierBadgesModeration(long userId) { throw new NotImplementedException(); }
        public void VerifierBadgesCommunaute(long userId) { throw new NotImplementedException(); }
        public void ChargerBadgesNonLus(ListView lv, long userId) { throw new NotImplementedException(); }
        public void MarquerBadgeCommeLu(long badgeId) { throw new NotImplementedException(); }
        public void GenererAffichageBadges(Literal literal, long userId) { throw new NotImplementedException(); }
        public void RecalculerTousBadges() { throw new NotImplementedException(); }
        public void RecalculerBadgesUtilisateur(long userId) { throw new NotImplementedException(); }
        public void SupprimerBadgesInvalides() { throw new NotImplementedException(); }
        public void MettreAJourCompteursBadges() { throw new NotImplementedException(); }
        public void ChargerRapportBadges(DateTime dateDebut, DateTime dateFin) { throw new NotImplementedException(); }
        public void ExporterBadgesUtilisateur(long userId) { throw new NotImplementedException(); }
        public void ImporterDefinitionsBadges(List<BadgeDefinitions_Class> definitions) { throw new NotImplementedException(); }
        public void SauvegarderConfigurationBadges() { throw new NotImplementedException(); }
    }
}
