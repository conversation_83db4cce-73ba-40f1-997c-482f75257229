using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class forum_accueil : System.Web.UI.Page
    {
        private SujetForumImp sujetImp = new SujetForumImp();
        private ReplyForumImp replyImp = new ReplyForumImp();
        private PostViewsImp viewsImp = new PostViewsImp();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LoadStatistics();
                LoadRecentActivity();
                LoadPopularQuestions();
            }
        }

        private void LoadStatistics()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    // Compter les questions
                    int totalQuestions = con.SujetForums.Count(s => s.PostTypeId == 1);
                    lblTotalQuestions.Text = totalQuestions.ToString();

                    // Compter les réponses
                    int totalAnswers = con.SujetForums.Count(s => s.PostTypeId == 2);
                    lblTotalAnswers.Text = totalAnswers.ToString();

                    // Compter les utilisateurs actifs (qui ont posté au moins une question ou réponse)
                    int totalUsers = con.SujetForums.Where(s => s.OwnerUserId.HasValue)
                                                   .Select(s => s.OwnerUserId.Value)
                                                   .Distinct()
                                                   .Count();
                    lblTotalUsers.Text = totalUsers.ToString();

                    // Compter les vues totales
                    int totalViews = con.SujetForums.Where(s => s.PostTypeId == 1)
                                                   .Sum(s => (int?)s.ViewCount) ?? 0;
                    lblTotalViews.Text = FormatNumber(totalViews);
                }
            }
            catch (Exception ex)
            {
                // En cas d'erreur, afficher des valeurs par défaut
                lblTotalQuestions.Text = "0";
                lblTotalAnswers.Text = "0";
                lblTotalUsers.Text = "0";
                lblTotalViews.Text = "0";
            }
        }

        private void LoadRecentActivity()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var recentActivity = (from s in con.SujetForums
                                        join m in con.Membres on s.OwnerUserId equals m.MembreId into memberJoin
                                        from m in memberJoin.DefaultIfEmpty()
                                        where s.CreationDate >= DateTime.Now.AddDays(-7)
                                        orderby s.CreationDate descending
                                        select new
                                        {
                                            Type = s.PostTypeId == 1 ? "question" : "answer",
                                            Title = s.PostTypeId == 1 ? s.Title : "Nouvelle réponse",
                                            UserName = m != null ? m.Nom + " " + m.Prenom : s.OwnerDisplayName,
                                            Date = s.CreationDate,
                                            PostId = s.SujetForumId
                                        }).Take(10).ToList();

                    lvRecentActivity.DataSource = recentActivity;
                    lvRecentActivity.DataBind();
                }
            }
            catch (Exception ex)
            {
                // En cas d'erreur, laisser vide
            }
        }

        private void LoadPopularQuestions()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var popularQuestions = (from s in con.SujetForums
                                          where s.PostTypeId == 1 && s.Score > 0
                                          orderby s.Score descending, s.ViewCount descending
                                          select new
                                          {
                                              s.SujetForumId,
                                              s.Title,
                                              s.Score,
                                              s.AnswerCount,
                                              s.ViewCount,
                                              s.CreationDate
                                          }).Take(10).ToList();

                    lvPopularQuestions.DataSource = popularQuestions;
                    lvPopularQuestions.DataBind();
                }
            }
            catch (Exception ex)
            {
                // En cas d'erreur, laisser vide
            }
        }

        protected string GetActivityColor(string activityType)
        {
            switch (activityType.ToLower())
            {
                case "question":
                    return "var(--lincom-secondary)";
                case "answer":
                    return "var(--lincom-success)";
                case "vote":
                    return "var(--lincom-warning)";
                case "badge":
                    return "var(--lincom-accent)";
                default:
                    return "var(--lincom-primary)";
            }
        }

        protected string GetActivityIcon(string activityType)
        {
            switch (activityType.ToLower())
            {
                case "question":
                    return "fas fa-question-circle";
                case "answer":
                    return "fas fa-comment";
                case "vote":
                    return "fas fa-thumbs-up";
                case "badge":
                    return "fas fa-medal";
                default:
                    return "fas fa-info-circle";
            }
        }

        protected string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "à l'instant";
            if (timeSpan.TotalMinutes < 60)
                return $"il y a {(int)timeSpan.TotalMinutes} min";
            if (timeSpan.TotalHours < 24)
                return $"il y a {(int)timeSpan.TotalHours}h";
            if (timeSpan.TotalDays < 30)
                return $"il y a {(int)timeSpan.TotalDays} jours";
            if (timeSpan.TotalDays < 365)
                return $"il y a {(int)(timeSpan.TotalDays / 30)} mois";
            
            return $"il y a {(int)(timeSpan.TotalDays / 365)} ans";
        }

        protected string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            if (text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength) + "...";
        }

        private string FormatNumber(int number)
        {
            if (number >= 1000000)
                return (number / 1000000.0).ToString("0.#") + "M";
            if (number >= 1000)
                return (number / 1000.0).ToString("0.#") + "K";
            
            return number.ToString();
        }
    }
}
