﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace LinCom.Imp
{
    public class ConditionUtilisationImp:IConditionUtilisation
    {
        int msg,id;
        ConditionUtilisation condition = new ConditionUtilisation();

        public void AfficherDetails(int idCondition, ConditionUtilisation_Class conditionClass)
        {
            using (Connection con = new Connection())
            {
                var c = con.ConditionUtilisations.FirstOrDefault(x => x.ConditionId == idCondition);
                if (c != null)
                {
                    conditionClass.ConditionId = c.ConditionId;
                    conditionClass.Contenu = c.Contenu;
                    conditionClass.name = c.name;
                    conditionClass.DatePublication = c.DatePublication;

                }
            }
        }
        public void AfficherDetails(string code, ConditionUtilisation_Class conditionClass)
        {
            using (Connection con = new Connection())
            {
                var c = con.ConditionUtilisations.FirstOrDefault(x => x.name == code);
                if (c != null)
                {
                    conditionClass.ConditionId = c.ConditionId;
                    conditionClass.Contenu = c.Contenu;
                    conditionClass.name = c.name;
                    conditionClass.DatePublication = c.DatePublication;

                }
            }
        }

        public int Ajouter(ConditionUtilisation_Class conditionClass)
        {
            using (Connection con = new Connection())
            {

                condition.Contenu = conditionClass.Contenu;
                condition.DatePublication = DateTime.Now;
                condition.name = conditionClass.name;


                try
                {
                    con.ConditionUtilisations.Add(condition);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }
        public int ID()
        {
            using (Connection con = new Connection())
            {
                ConditionUtilisation tp = new ConditionUtilisation();
             
                tp = con.ConditionUtilisations.OrderByDescending(x => x.ConditionId).FirstOrDefault();
                if (tp != null)
                    id = Convert.ToInt32(tp.ConditionId);
                else id = -1;
            }
            return id;
        }
        public int countID()
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                var b = (from l in con.ConditionUtilisations
                         select l).Count();
                n = b;
            }
            return n;
        }
        public int Modifier(ConditionUtilisation_Class conditionClass, int idCondition)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    // Recherche de la condition d'utilisation à modifier
                    var conditionToUpdate = con.ConditionUtilisations.FirstOrDefault(x => x.ConditionId == idCondition);

                    if (conditionToUpdate != null)
                    {
                        // Mise à jour des propriétés
                        conditionToUpdate.Contenu = conditionClass.Contenu;
                      

                        // Sauvegarde des modifications
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                        {
                            return msg = 0;
                        }
                    }
                    else
                    {
                        // La condition d'utilisation n'a pas été trouvée
                        return msg = 0;
                    }
                }
                catch
                {
                    return msg = 0;
                }
            }
        }
    }

}