﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IActiviteProjet
    {
        void AfficherDetails(long financementId, long idorg, string name, int cd, ActiviteProjet_Class actproj);
        int Ajouter(ActiviteProjet_Class f);
        void ChargerActiviteProjet(GridView gdv, long id, long idorg, string name, int cd);
        int Modifier(ActiviteProjet_Class f, long id, long idorg, string name, int cd);
        int Supprimer(long id, long idorg);
    }
}
