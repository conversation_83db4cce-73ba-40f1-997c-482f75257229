using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IReputationHistory
    {
        // Méthodes CRUD de base
        int Add(ReputationHistory_Class reputation);
        void AfficherDetails(long reputationId, ReputationHistory_Class reputation);
        int Edit(ReputationHistory_Class reputation, long reputationId);
        int Supprimer(long reputationId);
        void Chargement_GDV(GridView gdv);
        void Search(GridView gdv, string searchTerm);
        int Count();

        // Gestion de la réputation (Stack Overflow style)
        void AjouterReputation(long userId, int points, int typeAction, long? postId = null, string description = null);
        void SupprimerReputation(long userId, int points, int typeAction, long? postId = null, string description = null);
        int CalculerReputationTotale(long userId);
        void MettreAJourReputationUtilisateur(long userId);

        // Historique et suivi
        void ChargerHistoriqueUtilisateur(ListView lv, long userId, int limite = 50);
        void ChargerHistoriquePost(ListView lv, long postId);
        void ChargerActiviteRecente(ListView lv, int limite = 20);
        void ChargerChangementsJour(ListView lv, long userId, DateTime date);

        // Statistiques de réputation
        void ChargerStatistiquesUtilisateur(long userId, out int total, out int aujourdhui, out int semaine, out int mois);
        void ChargerTopUtilisateurs(ListView lv, int limite = 20, string periode = "all");
        void ChargerProgressionReputation(ListView lv, long userId, int jours = 30);
        void ChargerRepartitionReputation(long userId, out Dictionary<string, int> repartition);

        // Calculs et règles de réputation
        int CalculerPointsAction(int typeAction, bool estAuteur = false);
        bool PeutGagnerReputation(long userId, int typeAction);
        bool AAtteintLimiteJournaliere(long userId);
        int ObtenirLimiteJournaliere(long userId);
        void AppliquerReglesCap(long userId);

        // Types d'actions de réputation
        void ChargerTypesActions(DropDownList ddl);
        void AjouterTypeAction(ReputationHistoryTypes_Class type);
        void ModifierTypeAction(ReputationHistoryTypes_Class type);
        ReputationHistoryTypes_Class ObtenirTypeAction(int typeId);
        void ChargerTousTypesActions(ListView lv);

        // Niveaux et privilèges
        string CalculerNiveauUtilisateur(int reputation);
        List<string> ObtenirPrivileges(int reputation);
        int ObtenirPointsPourNiveauSuivant(int reputationActuelle);
        void ChargerNiveauxDisponibles(ListView lv);
        bool APrivilege(long userId, string privilege);

        // Événements spéciaux
        void GererVotePositif(long userId, long postId, bool estQuestion);
        void GererVoteNegatif(long userId, long postId, bool estQuestion);
        void GererReponseAcceptee(long userId, long postId);
        void GererPremierPost(long userId, long postId);
        void GererBountyAttribue(long userId, int montant);

        // Modération et ajustements
        void AjusterReputation(long userId, int ajustement, string raison);
        void AnnulerChangementsReputation(long userId, DateTime dateDebut, DateTime dateFin);
        void RecalculerReputationComplete(long userId);
        void ChargerAjustementsAdmin(ListView lv, int limite = 50);

        // Rapports et analytics
        void ChargerRapportReputationPeriode(DateTime debut, DateTime fin, ListView lv);
        void ChargerTendancesReputation(ListView lv, int jours = 30);
        void ChargerDistributionReputation(out Dictionary<string, int> distribution);
        void ExporterHistoriqueUtilisateur(long userId, DateTime? debut = null, DateTime? fin = null);

        // Notifications et alertes
        void NotifierChangementReputation(long userId, int changement, string raison);
        void ChargerNotificationsReputation(ListView lv, long userId);
        void MarquerNotificationLue(long notificationId);
        void ConfigurerAlertes(long userId, bool alertes);

        // Maintenance et optimisation
        void NettoierHistoriqueAncien(DateTime dateLimit);
        void CompacterHistorique(long userId);
        void RecalculerToutesReputations();
        void VerifierCoherenceReputation();
    }
}
