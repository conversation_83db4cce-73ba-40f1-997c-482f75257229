﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IMenu
    {
        void AfficherDetails(int menuID, Menu_Class menuClass);
        int Ajouter(Menu_Class menuClass);
        void ChargerMenus(GridView gdv, int? parentID = null);
        List<Menu_Class> ObtenirMenusParRole(int roleID);
        int Modifier(Menu_Class menuClass);
        int Supprimer(int menuID);
    }
}
