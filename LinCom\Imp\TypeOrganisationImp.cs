﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class TypeOrganisationImp : ITypeOrganisation
    {
        int msg;
        private TypeOrganisation typeOrganisation = new TypeOrganisation();

        public void AfficherDetails(int idTypeOrganisation, TypeOrganisation_Class typeOrganisationClass)
        {
            using (Connection con = new Connection())
            {
                var t = con.TypeOrganisations.FirstOrDefault(x => x.TypeOrganisationId == idTypeOrganisation);
                if (t != null)
                {
                    typeOrganisationClass.TypeOrganisationId = t.TypeOrganisationId;
                    typeOrganisationClass.Libelle = t.Libelle;
                    typeOrganisationClass.name = t.name;
                    
                    
                }
            }
        }
        public void AfficherDetails(string code, TypeOrganisation_Class typeOrganisationClass)
        {
            using (Connection con = new Connection())
            {
                var t = con.TypeOrganisations.FirstOrDefault(x => x.name == code);
                if (t != null)
                {
                    typeOrganisationClass.TypeOrganisationId = t.TypeOrganisationId;
                    typeOrganisationClass.Libelle = t.Libelle;
                    typeOrganisationClass.name = t.name;


                }
            }
        }

        public int Ajouter(TypeOrganisation_Class typeOrganisationClass)
        {
            using (Connection con = new Connection())
            {
                var typeOrg = new TypeOrganisation
                {
                    Libelle = typeOrganisationClass.Libelle,
                    name = typeOrganisationClass.name,
                   
                };

                con.TypeOrganisations.Add(typeOrg);
                try
                {
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerTypesOrganisation(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var query = from t in con.TypeOrganisations
                            select new
                            {
                                id = t.TypeOrganisationId,
                                libelle= t.Libelle,
                                name=t.name
                               };

                gdv.DataSource = query.OrderBy(x => x.libelle).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerTypesDisponibles(DropDownList ddw)
        {
           
                ddw.Items.Clear();
                using (Connection con = new Connection())
                {
                    var obj = (from p in con.TypeOrganisations select p).ToList();

                    if (obj != null && obj.Count() > 0)
                    {
                        ddw.Items.Clear();

                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = "-- Sélectionner le type d'organisation (statut juridique) --";
                        ddw.Items.Add(item0);

                        foreach (var data in obj)
                        {
                            ListItem item = new ListItem();
                            item.Value = data.TypeOrganisationId.ToString();
                            item.Text = data.Libelle;
                            ddw.Items.Add(item);
                        }

                    }
                    else
                    {
                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = "Aucune donnée";
                        ddw.Items.Add(item0);
                    }

      
            }
        }

        public int Modifier(int id, TypeOrganisation_Class typeOrganisationClass)
        {
            using (Connection con = new Connection())
            {
                var t = con.TypeOrganisations.FirstOrDefault(x => x.TypeOrganisationId == id);
                if (t != null)
                {
                    t.Libelle = typeOrganisationClass.Libelle;
                    t.name = typeOrganisationClass.name;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(int idTypeOrganisation)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si le type est utilisé par des organisations
                var organisationsAssociees = con.Organisations.Any(o => o.TypeOrganisationId == idTypeOrganisation);
                if (organisationsAssociees)
                {
                    return -1; // Impossible de supprimer car utilisé
                }

                var t = con.TypeOrganisations.FirstOrDefault(x => x.TypeOrganisationId == idTypeOrganisation);
                if (t != null)
                {
                    con.TypeOrganisations.Remove(t);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}