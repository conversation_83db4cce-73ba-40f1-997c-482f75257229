﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listdomaineinterventionorganisation : System.Web.UI.Page
    {
        private DomaineInterventionOrganisationImp obj = new DomaineInterventionOrganisationImp();
        private DomaineInterventionOrganisation_Class dominter = new DomaineInterventionOrganisation_Class();
        static long ide, idorg;
        static string nameorg;
        static int rolid;
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();

        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (!IsPostBack)
            {
                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    ide = Convert.ToInt64(idperso.Value);
                    rolid = Convert.ToInt32(role.Value);

                   
                    objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                    idorg = Convert.ToInt64(memorg.OrganisationId);

                    objrl.AfficherDetails(Convert.ToInt32(rolid), idorg,"",0, rl);

                }
                else Response.Redirect("~/login.aspx");

                ChargerDomainesInterventionOrganisation();
            }
        }

        private void ChargerDomainesInterventionOrganisation()
        {
            // Appeler la méthode avec le paramètre organisationId
            obj.ChargerDomainesInterventionOrganisation(gdv, idorg);
        }

        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            int id = Convert.ToInt32(e.CommandArgument);

            if (e.CommandName == "view")
            {
                Response.Redirect("~/file/domaineinterventionorganisation.aspx?id=" + id);
            }
            else if (e.CommandName == "delete")
            {
               
                int result = obj.Supprimer(id);
                if (result > 0)
                {
                    // Recharger la liste après suppression
                    ChargerDomainesInterventionOrganisation();
                    
                }
                else
                {
                    
                }
            }
        }
    }
}