﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
   
    public class CategoriePostImp : ICategoryPost
    {
        int msg;
        CategoriePost p = new CategoriePost();
        public int add(CategoriePost_Class add)
        {
            using (Connection con = new Connection())
            {
                p.Libelle = add.Libelle;
                p.name = add.name;

                try
                {
                    con.CategoriePosts.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.CategoriePosts.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }



        public void afficherDetails(int code, CategoriePost_Class pr)
        {
            using (Connection con = new Connection())
            {
                p = con.CategoriePosts.Where(x => x.CategoriePostId == code).FirstOrDefault();

                if (p != null)
                {
                    pr.CategoriePostId = p.CategoriePostId;
                    pr.<PERSON> = p.<PERSON>;
                    pr.name = p.name;

                }

            }
        }
        public void afficherDetails(string code, CategoriePost_Class pr)
        {
            using (Connection con = new Connection())
            {
                p = con.CategoriePosts.Where(x => x.name == code).FirstOrDefault();

                if (p != null)
                {
                    pr.CategoriePostId = p.CategoriePostId;
                    pr.Libelle = p.Libelle;
                    pr.name = p.name;

                }

            }
        }


        public void Chargement_GDV(GridView GV_apv)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.CategoriePosts
                           select new
                           {
                               id = ep.CategoriePostId,
                               libelle = ep.Libelle,
                               name = ep.name,

                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public void chargerCategoriePost(DropDownList ddw)
        {
                ddw.Items.Clear();
                using (Connection con = new Connection())
                {
                    var obj = (from p in con.CategoriePosts select p).ToList();

                    if (obj != null && obj.Count() > 0)
                    {
                        ddw.Items.Clear();

                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = "Selectionner la categorie";
                        ddw.Items.Add(item0);

                        foreach (var data in obj)
                        {
                            ListItem item = new ListItem();
                            item.Value = data.CategoriePostId.ToString();
                            item.Text = data.Libelle;
                            ddw.Items.Add(item);
                        }

                    }
                    else
                    {
                        ListItem item0 = new ListItem();
                        item0.Value = "-1";
                        item0.Text = "Aucune donnée";
                        ddw.Items.Add(item0);
                    }

                }
           
        }



        public int count()
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                var b = (from l in con.CategoriePosts
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(CategoriePost_Class cl, int id)
        {
            using (Connection con = new Connection())
            {
                p = con.CategoriePosts.Where(x => x.CategoriePostId == id).FirstOrDefault();

                try
                {
                    p.Libelle = cl.Libelle;
                    p.name = cl.name;

                    if (con.SaveChanges() == 1)
                    {
                        con.CategoriePosts.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }



        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.CategoriePosts
                           where (ep.Libelle.Contains(code) && ep.name.Contains(code))
                           select new
                           {
                               id = ep.CategoriePostId,
                               libelle = ep.Libelle,
                               name = ep.name,

                           }).ToList();


                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public int supprimer(int id)
        {
            using (Connection con = new Connection())
            {

                p = con.CategoriePosts.Where(x => x.CategoriePostId == id).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.CategoriePosts.Attach(p);

                con.CategoriePosts.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }
    }
}