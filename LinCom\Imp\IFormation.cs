﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IFormation
    {
        void AfficherDetails(long formationId, long idorg, Formation_Class formationClass);
        void AfficherDetails(string name, long idorg, Formation_Class formationClass);
        int Ajouter(Formation_Class formation);
        int Modifier(Formation_Class formationClass, long id, long idorg);
        int Supprimer(long formationId);
        void ChargerGridView(GridView gdv);

    }
}
