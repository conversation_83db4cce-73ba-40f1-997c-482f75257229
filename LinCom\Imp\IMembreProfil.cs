﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LinCom.Imp
{
    internal interface IMembreProfil
    {
        void AfficherDetails(long membreProfilId, MembreProfil_Class membreProfilClass);
        int Ajouter(MembreProfil_Class membreProfilClass);
        void ChargerProfil(long membreId, MembreProfil_Class membreProfilClass);
        int Modifier(MembreProfil_Class membreProfilClass);
        int Supprimer(long membreProfilId);
    }
}
