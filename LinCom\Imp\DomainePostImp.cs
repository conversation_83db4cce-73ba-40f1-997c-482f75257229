﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.Services.Description;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class DomainePostImp : IDomainePost
    {

         DomainePost domainePost = new DomainePost();
         DomainePost p = new DomainePost();
        int msg;
        public void AfficherDetails(DomainePost_Class domainePostClass,long domainePostId,long idorg,long iddom,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var dp = con.DomainePosts.FirstOrDefault(x => x.DomainePostId == domainePostId && x.OrganisationId==idorg);
                    if (dp != null)
                    {
                        domainePostClass.DomainePostId = dp.DomainePostId;
                        domainePostClass.PostId = dp.PostId;
                        domainePostClass.DomaineInterventionOrganisationId = dp.DomaineInterventionOrganisationId;
                        domainePostClass.DateCreation = dp.DateCreation;
                        domainePostClass.statut = dp.statut;
                        domainePostClass.MembreId = dp.MembreId;
                        domainePostClass.OrganisationId = dp.OrganisationId;
                    }
                }
                
            }
        }
       
        public int Ajouter(DomainePost_Class domainePostClass)
        {
            using (Connection con = new Connection())
            {
                domainePost.PostId = domainePostClass.PostId;
                domainePost.DomaineInterventionOrganisationId = domainePostClass.DomaineInterventionOrganisationId;
                domainePost.DateCreation = domainePostClass.DateCreation ?? DateTime.Now;
                domainePost.statut = domainePostClass.statut ?? "actif";
                domainePost.MembreId = domainePostClass.MembreId;
                domainePost.OrganisationId = domainePostClass.OrganisationId;

                try
                {
                    con.DomainePosts.Add(domainePost);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public int Modifier(DomainePost_Class domainePostClass, long id, long idorg,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var dp = con.DomainePosts.FirstOrDefault(x => x.DomainePostId == id && x.OrganisationId == idorg);
                    if (dp != null)
                    {
                        dp.PostId = domainePostClass.PostId;
                        dp.DomaineInterventionOrganisationId = domainePostClass.DomaineInterventionOrganisationId;
                        dp.statut = domainePostClass.statut;
                        dp.MembreId = domainePostClass.MembreId;

                        try
                        {
                            if (con.SaveChanges() == 1)
                            {
                                con.DomainePosts.Add(dp);
                                con.Entry(dp).State = EntityState.Modified;
                                return msg = 1;
                            }
                            else
                                return msg = 0;
                        }
                        catch
                        {
                            return msg = 0;
                        }
                    }
                }
                
                
                return msg;
            }
        }

        public int Supprimer(long id,long idorg)
        {
            using (Connection con = new Connection())
            {

                p = con.DomainePosts.Where(x => x.DomainePostId == id && x.OrganisationId==idorg).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.DomainePosts.Attach(p);

                con.DomainePosts.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }
        public void ChargerListView(ListView gdv,long idpost,long idorg,int cd, string statut)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var query = from dp in con.DomainePosts
                                join p in con.Posts on dp.PostId equals p.PostId
                                join dio in con.DomaineInterventionOrganisations on dp.DomaineInterventionOrganisationId equals dio.DomaineInterventionOrganisationId
                                join m in con.Membres on dp.MembreId equals m.MembreId
                                join o in con.Organisations on dp.OrganisationId equals o.OrganisationId
                                join doc in con.DomaineInterventions on dio.DomaineInterventionId equals doc.DomaineInterventionId
                                where dp.PostId == idpost && dp.OrganisationId==idorg
                                select new
                                {
                                    id = dp.DomainePostId,
                                    idpost = dp.PostId,
                                    PostTitre = doc.Libelle,

                                };

                    gdv.DataSource = query.OrderByDescending(x => x.id).ToList();
                    gdv.DataBind();
                }
                else if(cd==1)
                {
                    var query = from dp in con.DomainePosts
                                join p in con.Posts on dp.PostId equals p.PostId
                                join dio in con.DomaineInterventionOrganisations on dp.DomaineInterventionOrganisationId equals dio.DomaineInterventionOrganisationId
                                join m in con.Membres on dp.MembreId equals m.MembreId
                                join o in con.Organisations on dp.OrganisationId equals o.OrganisationId
                                join doc in con.DomaineInterventions on dio.DomaineInterventionId equals doc.DomaineInterventionId
                                where dp.PostId == idpost && dp.statut==statut
                                select new
                                {
                                    id = dp.DomainePostId,
                                    idpost = dp.PostId,
                                    PostTitre = doc.Libelle,

                                };

                    gdv.DataSource = query.OrderByDescending(x => x.id).ToList();
                    gdv.DataBind();
                }
                  
            }
        }

        public void ChargerGridView(GridView gdv, long idpost, long idorg, int cd, string statut)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var query = from dp in con.DomainePosts
                                join p in con.Posts on dp.PostId equals p.PostId
                                join ct in con.CategoriePosts on p.CategoriePostId equals ct.CategoriePostId
                                join dio in con.DomaineInterventionOrganisations on dp.DomaineInterventionOrganisationId equals dio.DomaineInterventionOrganisationId
                                join m in con.Membres on dp.MembreId equals m.MembreId
                                join o in con.Organisations on dp.OrganisationId equals o.OrganisationId
                                join doc in con.DomaineInterventions on dio.DomaineInterventionId equals doc.DomaineInterventionId
                                where dp.OrganisationId == idorg
                                select new
                                {
                                    id = dp.DomainePostId,
                                    idpost = dp.PostId,
                                    PostDom = doc.Libelle,
                                    PostTitre = p.Titre,
                                    PostCateg=ct.Libelle,

                                };

                    gdv.DataSource = query.OrderByDescending(x => x.id).ToList();
                    gdv.DataBind();
                }
               
            }
        }

        public void ChargerDomainesParPost(GridView gdv, long postId)
        {
            using (Connection con = new Connection())
            {
                var query = from dp in con.DomainePosts
                            where dp.PostId == postId
                            join dio in con.DomaineInterventionOrganisations on dp.DomaineInterventionOrganisationId equals dio.DomaineInterventionOrganisationId into dioGroup
                            from dio in dioGroup.DefaultIfEmpty()
                            select new
                            {
                                dp.DomainePostId,
                                dp.DomaineInterventionOrganisationId,
                                DomaineNom = dio != null ? dio.name : "Non spécifié",
                                dp.DateCreation,
                                dp.statut
                            };

                gdv.DataSource = query.OrderBy(x => x.DomaineNom).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerDomainesParOrganisation(GridView gdv, long organisationId)
        {
            using (Connection con = new Connection())
            {
                var query = from dp in con.DomainePosts
                            where dp.OrganisationId == organisationId
                            join p in con.Posts on dp.PostId equals p.PostId
                            join dio in con.DomaineInterventionOrganisations on dp.DomaineInterventionOrganisationId equals dio.DomaineInterventionOrganisationId into dioGroup
                            from dio in dioGroup.DefaultIfEmpty()
                            select new
                            {
                                dp.DomainePostId,
                                dp.PostId,
                                PostTitre = p.Titre,
                                dp.DomaineInterventionOrganisationId,
                                DomaineNom = dio != null ? dio.name : "Non spécifié",
                                dp.DateCreation,
                                dp.statut
                            };

                gdv.DataSource = query.OrderByDescending(x => x.DateCreation).ToList();
                gdv.DataBind();
            }
        }

       
    }
}