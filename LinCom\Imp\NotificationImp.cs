﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class NotificationImp : INotification
    {
        int msg;
        Notification p = new Notification();

        public void AfficherDetails(long notificationId, Notification_Class notification)
        {
            using (Connection con = new Connection())
            {
                var notif = con.Notifications.Where(n => n.NotificationId == notificationId).FirstOrDefault();
                if (notif != null)
                {
                    notification.NotificationId = notif.NotificationId;
                    notification.MembreId = notif.MembreId;
                    notification.Titre = notif.Titre;
                    notification.Message = notif.Message;
                    notification.DateNotification = notif.DateNotification;
                    notification.Lu = notif.Lu;
                    notification.name = notif.name;
                }
            }
        }

        public int Ajouter(Notification_Class notification)
        {
            using (Connection con = new Connection())
            {
                p.MembreId = notification.MembreId;
                p.Titre = notification.Titre;
                p.Message = notification.Message;
                p.DateNotification = DateTime.Now;
                p.Lu = false;
                p.name = notification.name;

                try
                {
                    con.Notifications.Add(p);
                    if (con.SaveChanges() == 1)
                        return msg = 1;
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public int Modifier(Notification_Class notification)
        {
            using (Connection con = new Connection())
            {
                var notif = con.Notifications.Where(n => n.NotificationId == notification.NotificationId).FirstOrDefault();
                if (notif != null)
                {
                    notif.Titre = notification.Titre;
                    notif.Message = notification.Message;
                    notif.Lu = notification.Lu;
                    notif.name = notification.name;

                    try
                    {
                        if (con.SaveChanges() == 1)
                            return msg = 1;
                        else
                            return msg = 0;
                    }
                    catch (Exception e)
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(long notificationId)
        {
            using (Connection con = new Connection())
            {
                var notif = con.Notifications.Where(n => n.NotificationId == notificationId).FirstOrDefault();
                if (notif != null)
                {
                    try
                    {
                        con.Notifications.Remove(notif);
                        if (con.SaveChanges() == 1)
                            return msg = 1;
                        else
                            return msg = 0;
                    }
                    catch (Exception e)
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public void ChargerNotifications(GridView gdv, long membreId, bool nonLues = false)
        {
            using (Connection con = new Connection())
            {
                var query = con.Notifications.Where(n => n.MembreId == membreId);

                if (nonLues)
                    query = query.Where(n => n.Lu == false);

                var notifications = query.OrderByDescending(n => n.DateNotification)
                                        .Select(n => new
                                        {
                                            n.NotificationId,
                                            n.Titre,
                                            n.Message,
                                            n.DateNotification,
                                            n.Lu
                                        }).ToList();

                gdv.DataSource = notifications;
                gdv.DataBind();
            }
        }

        public int MarquerCommeLue(long notificationId)
        {
            using (Connection con = new Connection())
            {
                var notif = con.Notifications.Where(n => n.NotificationId == notificationId).FirstOrDefault();
                if (notif != null)
                {
                    notif.Lu = true;
                    try
                    {
                        if (con.SaveChanges() == 1)
                            return msg = 1;
                        else
                            return msg = 0;
                    }
                    catch (Exception e)
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public void ChargerNotificationsParType(GridView gdv, string type)
        {
            using (Connection con = new Connection())
            {
                var notifications = con.Notifications.Where(n => n.Titre.Contains(type))
                                                    .OrderByDescending(n => n.DateNotification)
                                                    .Select(n => new
                                                    {
                                                        n.NotificationId,
                                                        n.Titre,
                                                        n.Message,
                                                        n.DateNotification,
                                                        n.Lu,
                                                        n.MembreId
                                                    }).ToList();

                gdv.DataSource = notifications;
                gdv.DataBind();
            }
        }

        public int EnvoyerNotificationGroupe(Notification_Class notification, long[] membreIds)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    foreach (var membreId in membreIds)
                    {
                        var notif = new Notification
                        {
                            MembreId = membreId,
                            Titre = notification.Titre,
                            Message = notification.Message,
                            DateNotification = DateTime.Now,
                            Lu = false,
                            name = notification.name
                        };
                        con.Notifications.Add(notif);
                    }

                    if (con.SaveChanges() > 0)
                        return msg = 1;
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerStatistiques(Repeater rpt, long membreId)
        {
            using (Connection con = new Connection())
            {
                var stats = new[]
                {
                    new {
                        Type = "Total",
                        Count = con.Notifications.Count(n => n.MembreId == membreId),
                        Icon = "fas fa-bell"
                    },
                    new {
                        Type = "Non lues",
                        Count = con.Notifications.Count(n => n.MembreId == membreId && n.Lu == false),
                        Icon = "fas fa-bell-slash"
                    },
                    new {
                        Type = "Aujourd'hui",
                        Count = con.Notifications.Count(n => n.MembreId == membreId &&
                                                            n.DateNotification.HasValue &&
                                                            n.DateNotification.Value.Date == DateTime.Today),
                        Icon = "fas fa-calendar-day"
                    }
                };

                rpt.DataSource = stats;
                rpt.DataBind();
            }
        }

        public void PurgerAnciennesNotifications(int joursConservation)
        {
            using (Connection con = new Connection())
            {
                var dateLimit = DateTime.Now.AddDays(-joursConservation);
                var anciennesNotifications = con.Notifications.Where(n => n.DateNotification < dateLimit && n.Lu == true);

                try
                {
                    con.Notifications.RemoveRange(anciennesNotifications);
                    con.SaveChanges();
                }
                catch (Exception e)
                {
                    // Log error
                }
            }
        }

        // Méthodes spécifiques au forum
        public void NotifierNouvelleReponse(long questionId, long auteurQuestionId, string titreQuestion, string auteurReponse)
        {
            var notification = new Notification_Class
            {
                MembreId = auteurQuestionId,
                Titre = "Nouvelle réponse à votre question",
                Message = $"{auteurReponse} a répondu à votre question '{titreQuestion}'",
                name = "Forum"
            };
            Ajouter(notification);
        }

        public void NotifierCommentaire(long postId, long auteurPostId, string auteurCommentaire, string typePost)
        {
            var notification = new Notification_Class
            {
                MembreId = auteurPostId,
                Titre = $"Nouveau commentaire sur votre {typePost}",
                Message = $"{auteurCommentaire} a commenté votre {typePost}",
                name = "Forum"
            };
            Ajouter(notification);
        }

        public void NotifierVote(long postId, long auteurPostId, string typeVote, string typePost)
        {
            var notification = new Notification_Class
            {
                MembreId = auteurPostId,
                Titre = $"Vote {typeVote} sur votre {typePost}",
                Message = $"Votre {typePost} a reçu un vote {typeVote}",
                name = "Forum"
            };
            Ajouter(notification);
        }

        public void NotifierReponseAcceptee(long reponseId, long auteurReponseId, string titreQuestion)
        {
            var notification = new Notification_Class
            {
                MembreId = auteurReponseId,
                Titre = "Votre réponse a été acceptée !",
                Message = $"Votre réponse à la question '{titreQuestion}' a été acceptée comme meilleure réponse",
                name = "Forum"
            };
            Ajouter(notification);
        }
    }
}