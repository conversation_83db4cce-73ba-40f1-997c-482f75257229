﻿<%@ Page Title="Ressources"  ValidateRequest="false" Language="C#" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="ressources.aspx.cs" Inherits="LinCom.file.ressources" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
          <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">

                                    <div class="breadcomb-ctn">
                                        <h2>Ressouces</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">

                                    <a data-toggle="tooltip" data-placement="left" href="listressources.aspx" title="Clique sur ce button pour visualiser la liste des Resources" class="btn">Liste des Ressources</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->
    <!-- Form Element area Start-->
    <div class="form-element-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="cmp-tb-hd bcs-hd">
                            <p style="color: red">Remplissez les champs obligatoires avec asterix</p>
                        </div>
                        <div class="alert-list">
                            <div class="alert alert-success alert-dismissible" role="alert" runat="server" id="div_msg_succes">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button><span runat="server" id="msg_succes">Enregistrement réussi</span>
                            </div>
                            <div class="alert alert-danger alert-dismissible" role="alert" runat="server" id="div_msg_error">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button><span runat="server" id="msg_error">Enregistrement échoué</span>
                            </div>
                        </div>
                        <div class="row">
                             <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
     <div class="form-group ic-cmp-int">
         <div class="form-ic-cmp">
             <i class="notika-icon notika-edit"></i>
         </div>
         <div class="nk-int-st">
              <label>Type de ressources * </label>
             <asp:DropDownList class="form-control" ID="drpdtyperessources" runat="server">

                 <asp:ListItem Value="-1">Selectionner le type de ressources</asp:ListItem>
                 <asp:ListItem Value="formation">Livre de Formation</asp:ListItem>
                 <asp:ListItem Value="document administratif">Document Administratif</asp:ListItem>
                 <asp:ListItem Value="rapport">Rapport</asp:ListItem>
             </asp:DropDownList>
         </div>
     </div>
 </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-house"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Titre * </label>
                                        <input type="text" runat="server" id="txtTitre" class="form-control" placeholder="Titre *">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-calendar"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Date de Publication * </label>
                                        <input type="date" runat="server" id="txtDatePublication" class="form-control" placeholder="Date de Publication *">
                                    </div>
                                </div>
                            </div>
                               <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
     <div class="form-group ic-cmp-int">
         <div class="form-ic-cmp">
             <i class="notika-icon notika-house"></i>
         </div>
         <div class="nk-int-st">
              <label>Nombre de Page * </label>
             <input type="number" runat="server" id="txtnombrepage" class="form-control" placeholder="Nombre de page *">
         </div>
     </div>
 </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Statut * </label>
                                        <asp:DropDownList class="form-control" ID="drpdstatutforma" runat="server">

                                            <asp:ListItem Value="-1">Selectionner l'etat de la formation</asp:ListItem>
                                            <asp:ListItem Value="En attente">En attente</asp:ListItem>
                                            <asp:ListItem Value="Publié">Publié</asp:ListItem>
                                            <asp:ListItem Value="Non publié">Non publié</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 ">
      <h6>Information sur les domaines d'interventions</h6>
      <div class="col-md-12">
          <i>Vous pouvez choisir un ou plusieurs domaines d'intervention en selectionnant dans la liste.</i>
          <asp:DropDownList ID="drpddomai" runat="server" data-parsley-trigger="change" autocomplete="off" class="form-control" AutoPostBack="true" OnSelectedIndexChanged="drpddomai_SelectedIndexChanged">
              <asp:ListItem Value="-1">Selectionner le domaine d'Intervention</asp:ListItem>
          </asp:DropDownList>
           <a href="srdikis.aspx" runat="server" id="ajoudom" onserverclick="ajoudom_ServerClick" visible="false">Mon domaine n'existe pas, je veux creer</a>
          <div id="dom" runat="server" visible="false">
              <h6 runat="server" visible="false">Veuillez selectionner le domaine et cliquer sur le button Ajouter un domaine, et ajoutez d'autres domaines</h6>
              <button type="submit" visible="false" runat="server" id="btnajourdom" onserverclick="btnajourdom_ServerClick">Ajouter à la un domaine</button>
              <button type="submit" runat="server" id="btnannuldom" onserverclick="btnannuldom_ServerClick" class="btn btn-danger notika-gp-danger">Recommencer</button>

              <asp:GridView ID="GridView1" runat="server"
                  ShowHeaderWhenEmpty="True"
                  AutoGenerateColumns="True"
                  EmptyDataText="Aucune Donnée Trouvée pour votre Rercherche"
                  ShowFooter="true" FooterStyle-Font-Bold="true"
                  EditRowStyle-Font-Bold="true"
                  EmptyDataRowStyle-Font-Names="century"
                  EmptyDataRowStyle-Font-Size="X-Large"
                  EmptyDataRowStyle-HorizontalAlign="Center"
                  GridLines="None" AllowPaging="True"
                  CellSpacing="0" Width="100%">
                  <AlternatingRowStyle BackColor="#DCDCDC" />

              </asp:GridView>
          </div>

      </div>
  </div>
                         <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
    <div class="form-group ic-cmp-int">
        <div class="form-ic-cmp">
            <i class="notika-icon notika-picture"></i>
        </div>
        <div class="nk-int-st">
            <label>Photo de couverture </label>
            <asp:FileUpload ID="fileupd" runat="server" class="form-control" />

        </div>
    </div>
</div>
<div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
    <div class="form-group ic-cmp-int">
        <div class="form-ic-cmp">
            <i class="notika-icon notika-picture"></i>
        </div>
        <div class="nk-int-st">
             <label>Fichier(pdf) </label>
            <asp:FileUpload ID="fileupdoc" runat="server" class="form-control" />

        </div>
    </div>
</div>
                             <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
     <div class="form-group ic-cmp-int">
         <div class="form-ic-cmp">
             <i class="notika-icon notika-form"></i>
         </div>
         <div class="nk-int-st">
              <label>Description </label>
             <div>
                 
                 <textarea class="html-editor" runat="server" id="txtdescription" rows="10"></textarea>

             </div>

         </div>
     </div>
 </div>
                                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
         <button type="button" runat="server" id="btn_ajouter" onserverclick="btn_ajouter_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
     </div>
</div>
                        </div>
                </div>
            </div>

        </div>
    </div>
    <!-- Form Element area End-->
</asp:Content>
