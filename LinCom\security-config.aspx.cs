using LinCom.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class security_config : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            // Vérifier si l'utilisateur est administrateur
            if (Session["MembreId"] == null)
            {
                Response.Redirect("login.aspx");
                return;
            }

            // Vérifier les permissions d'administrateur
            if (!IsUserAdmin())
            {
                Response.Redirect("forum-questions.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadSecurityMetrics();
            }
        }

        private bool IsUserAdmin()
        {
            try
            {
                long userId = Convert.ToInt64(Session["MembreId"]);
                using (var con = new LinCom.Model.Connection())
                {
                    var user = con.Membres.Where(m => m.MembreId == userId).FirstOrDefault();
                    return user != null && (user.Reputation >= 10000 || user.Role == "Admin");
                }
            }
            catch
            {
                return false;
            }
        }

        private void LoadSecurityMetrics()
        {
            try
            {
                // Simuler des métriques de sécurité
                // Dans une vraie implémentation, ces données viendraient d'une base de données de logs
                lblSqlInjectionBlocked.Text = GetSecurityMetric("sql_injection_blocked");
                lblXssAttacksBlocked.Text = GetSecurityMetric("xss_attacks_blocked");
                lblCsrfTokensInvalid.Text = GetSecurityMetric("csrf_tokens_invalid");
                lblRateLimitingActive.Text = GetSecurityMetric("rate_limiting_active");
                lblLastSecurityUpdate.Text = DateTime.Now.ToString("dd/MM/yyyy");
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des métriques : " + ex.Message);
            }
        }

        private string GetSecurityMetric(string metricName)
        {
            // Simuler des données de sécurité
            // Dans une vraie implémentation, ceci viendrait d'une base de données de logs
            var random = new Random();
            switch (metricName)
            {
                case "sql_injection_blocked":
                    return random.Next(0, 5).ToString();
                case "xss_attacks_blocked":
                    return random.Next(0, 10).ToString();
                case "csrf_tokens_invalid":
                    return random.Next(0, 3).ToString();
                case "rate_limiting_active":
                    return random.Next(5, 25).ToString();
                default:
                    return "0";
            }
        }

        protected void btnViewFullLogs_Click(object sender, EventArgs e)
        {
            try
            {
                // Rediriger vers une page de logs détaillés
                Response.Redirect("security-logs.aspx");
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de l'accès aux logs : " + ex.Message);
            }
        }

        protected void btnClearLogs_Click(object sender, EventArgs e)
        {
            try
            {
                // Vider les logs de sécurité
                // Dans une vraie implémentation, ceci supprimerait les logs de la base de données
                ShowSuccess("Logs de sécurité vidés avec succès.");
                LoadSecurityMetrics();
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la suppression des logs : " + ex.Message);
            }
        }

        protected void btnRunSecurityScan_Click(object sender, EventArgs e)
        {
            try
            {
                // Lancer un scan de sécurité
                RunSecurityScan();
                ShowSuccess("Scan de sécurité terminé. Aucune vulnérabilité critique détectée.");
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du scan de sécurité : " + ex.Message);
            }
        }

        protected void btnUpdateSecurityRules_Click(object sender, EventArgs e)
        {
            try
            {
                // Mettre à jour les règles de sécurité
                UpdateSecurityRules();
                ShowSuccess("Règles de sécurité mises à jour avec succès.");
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la mise à jour des règles : " + ex.Message);
            }
        }

        protected void btnExportSecurityReport_Click(object sender, EventArgs e)
        {
            try
            {
                // Exporter un rapport de sécurité
                ExportSecurityReport();
                ShowSuccess("Rapport de sécurité exporté avec succès.");
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de l'export du rapport : " + ex.Message);
            }
        }

        protected void btnTestSecurityFeatures_Click(object sender, EventArgs e)
        {
            try
            {
                // Tester les fonctionnalités de sécurité
                TestSecurityFeatures();
                ShowSuccess("Tests de sécurité terminés. Toutes les fonctionnalités sont opérationnelles.");
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors des tests de sécurité : " + ex.Message);
            }
        }

        private void RunSecurityScan()
        {
            // Simuler un scan de sécurité
            System.Threading.Thread.Sleep(1000); // Simuler le temps de traitement

            // Dans une vraie implémentation, ceci effectuerait :
            // - Scan des vulnérabilités connues
            // - Vérification des configurations de sécurité
            // - Test des protections en place
            // - Génération d'un rapport détaillé

            ForumSecurityHelper.LogSecurityIncident("Security scan completed", 
                Convert.ToInt64(Session["MembreId"]), 
                Request.UserHostAddress);
        }

        private void UpdateSecurityRules()
        {
            // Simuler la mise à jour des règles de sécurité
            System.Threading.Thread.Sleep(500);

            // Dans une vraie implémentation, ceci mettrait à jour :
            // - Les règles de validation
            // - Les listes noires/blanches
            // - Les paramètres de rate limiting
            // - Les configurations de sécurité

            ForumSecurityHelper.LogSecurityIncident("Security rules updated", 
                Convert.ToInt64(Session["MembreId"]), 
                Request.UserHostAddress);
        }

        private void ExportSecurityReport()
        {
            // Simuler l'export d'un rapport
            System.Threading.Thread.Sleep(800);

            // Dans une vraie implémentation, ceci générerait :
            // - Un rapport PDF ou Excel
            // - Les métriques de sécurité
            // - Les incidents récents
            // - Les recommandations

            ForumSecurityHelper.LogSecurityIncident("Security report exported", 
                Convert.ToInt64(Session["MembreId"]), 
                Request.UserHostAddress);
        }

        private void TestSecurityFeatures()
        {
            // Simuler les tests de sécurité
            System.Threading.Thread.Sleep(1200);

            // Dans une vraie implémentation, ceci testerait :
            // - La protection CSRF
            // - La validation XSS
            // - La protection SQL injection
            // - Le rate limiting
            // - L'authentification

            bool csrfTest = TestCSRFProtection();
            bool xssTest = TestXSSProtection();
            bool sqlTest = TestSQLInjectionProtection();
            bool rateLimitTest = TestRateLimiting();

            if (!csrfTest || !xssTest || !sqlTest || !rateLimitTest)
            {
                throw new Exception("Certains tests de sécurité ont échoué.");
            }

            ForumSecurityHelper.LogSecurityIncident("Security features tested successfully", 
                Convert.ToInt64(Session["MembreId"]), 
                Request.UserHostAddress);
        }

        private bool TestCSRFProtection()
        {
            // Test de la protection CSRF
            try
            {
                string token = CSRFProtection.GenerateToken(HttpContext.Current);
                return !string.IsNullOrEmpty(token) && CSRFProtection.ValidateToken(HttpContext.Current, token);
            }
            catch
            {
                return false;
            }
        }

        private bool TestXSSProtection()
        {
            // Test de la protection XSS
            try
            {
                string maliciousInput = "<script>alert('xss')</script>";
                var result = ForumSecurityHelper.ValidatePostContent(maliciousInput);
                return !result.IsValid || !result.CleanedValue.Contains("<script>");
            }
            catch
            {
                return false;
            }
        }

        private bool TestSQLInjectionProtection()
        {
            // Test de la protection SQL injection
            try
            {
                string maliciousInput = "'; DROP TABLE Users; --";
                var result = ForumSecurityHelper.ValidateSearchTerm(maliciousInput);
                return !result.IsValid;
            }
            catch
            {
                return false;
            }
        }

        private bool TestRateLimiting()
        {
            // Test du rate limiting
            try
            {
                long userId = Convert.ToInt64(Session["MembreId"]);
                
                // Simuler plusieurs requêtes rapides
                for (int i = 0; i < 10; i++)
                {
                    if (ForumSecurityHelper.IsRateLimited(userId, "test"))
                    {
                        return true; // Rate limiting fonctionne
                    }
                }
                
                return false; // Rate limiting ne fonctionne pas
            }
            catch
            {
                return false;
            }
        }

        private void ShowError(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "error", 
                $"toastr.error('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }

        private void ShowSuccess(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "success", 
                $"toastr.success('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }

        private void ShowWarning(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "warning", 
                $"toastr.warning('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }
    }
}
