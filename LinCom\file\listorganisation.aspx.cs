﻿using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listorganisation : System.Web.UI.Page
    {
        private OrganisationImp organisationImp = new OrganisationImp();
        private Organisation_Class organisationClass = new Organisation_Class();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                ChargerOrganisations();
            }
        }

        private void ChargerOrganisations()
        {
            organisationImp.ChargerGridView(gdv);
        }

        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            string id = e.CommandArgument.ToString();

            if (e.CommandName == "view")
            {
                Response.Redirect("organisation.aspx?name=" + id);
            }
            else if (e.CommandName == "delete")
            {
                organisationImp.AfficherDetails(id,organisationClass);
                int result = organisationImp.Supprimer(organisationClass.OrganisationId);
                if (result > 0)
                {
                    // Recharger la liste après suppression
                    ChargerOrganisations();
                   
                }
                else
                {
                    // Vous pourriez ajouter un message d'erreur ici si nécessaire
                }
            }
        }
    }
}