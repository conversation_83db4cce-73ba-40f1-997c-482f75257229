﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class MentoreImp:IMentore
    {
        int msg;
        private Mentore mentore = new Mentore();

        public void AfficherDetails(int mentoreId, Mentore_Class mentoreClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Mentores.FirstOrDefault(x => x.MentoreId == mentoreId);
                if (m != null)
                {
                    mentoreClass.MentoreId = m.MentoreId;
                    mentoreClass.MembreId = m.MembreId;
                    mentoreClass.ProgrammeMentoratId = m.ProgrammeMentoratId;
                    mentoreClass.status = m.status;
                }
            }
        }

        public int Ajouter(Mentore_Class mentoreClass)
        {
            using (Connection con = new Connection())
            {
                mentore.MembreId = mentoreClass.MembreId;
                mentore.ProgrammeMentoratId = mentoreClass.ProgrammeMentoratId;
                mentore.status = mentoreClass.status ?? "actif";

                try
                {
                    con.Mentores.Add(mentore);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }


        

        public void ChargerMentores(GridView gdv, string status = "")
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Mentores
                            join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                            from programme in programmes.DefaultIfEmpty()
                            where string.IsNullOrEmpty(status) || m.status == status
                            select new
                            {
                                m.MentoreId,
                                m.MembreId,
                                Membre = membre != null ? membre.Nom : "Inconnu",
                                m.ProgrammeMentoratId,
                                Programme = programme != null ? programme.Titre : "Aucun",
                                m.status
                            };

                gdv.DataSource = query.OrderBy(x => x.Membre).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerMentoresParProgramme(GridView gdv, int programmeMentoratId)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Mentores
                            join mb in con.Membres on m.MembreId equals mb.MembreId into membres
                            from membre in membres.DefaultIfEmpty()
                            where m.ProgrammeMentoratId == programmeMentoratId
                            select new
                            {
                                m.MentoreId,
                                m.MembreId,
                                Membre = membre != null ? membre.Nom : "Inconnu",
                                m.status
                            };

                gdv.DataSource = query.OrderBy(x => x.Membre).ToList();
                gdv.DataBind();
            }
        }

        public void ChargerMentoresParMembre(GridView gdv, long membreId)
        {
            using (Connection con = new Connection())
            {
                var query = from m in con.Mentores
                            join p in con.Programmementorats on m.ProgrammeMentoratId equals p.ProgrammeMentoratId into programmes
                            from programme in programmes.DefaultIfEmpty()
                            where m.MembreId == membreId
                            select new
                            {
                                m.MentoreId,
                                m.ProgrammeMentoratId,
                                Programme = programme != null ? programme.Titre : "Aucun",
                                m.status
                            };

                gdv.DataSource = query.OrderBy(x => x.Programme).ToList();
                gdv.DataBind();
            }
        }

        public void chargerMentore(DropDownList lst)
        {
            lst.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Mentores select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner le Mentore";
                    lst.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.MentoreId.ToString();
                        
                        lst.Items.Add(item);
                    }

                }
                else
                {
                    lst.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Aucune donnée";
                    lst.Items.Add(item0);
                }

            }
        }


        public int Modifier(Mentore_Class mentoreClass)
        {
            using (Connection con = new Connection())
            {
                var m = con.Mentores.FirstOrDefault(x => x.MentoreId == mentoreClass.MentoreId);
                if (m != null)
                {
                    m.MembreId = mentoreClass.MembreId;
                    m.ProgrammeMentoratId = mentoreClass.ProgrammeMentoratId;
                    m.status = mentoreClass.status;

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public int Supprimer(int mentoreId)
        {
            using (Connection con = new Connection())
            {
                var m = con.Mentores.FirstOrDefault(x => x.MentoreId == mentoreId);
                if (m != null)
                {
                    con.Mentores.Remove(m);
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}