using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ITags
    {
        // Méthodes CRUD de base
        int Add(Tags_Class tag);
        void AfficherDetails(long tagId, Tags_Class tag);
        int Edit(Tags_Class tag, long tagId);
        int Supprimer(long tagId);
        void Chargement_GDV(GridView gdv);
        void Search(GridView gdv, string searchTerm);
        int Count();

        // Méthodes Stack Overflow style
        void ChargerTagsPopulaires(ListView lv, int limite = 50);
        void ChargerTagsTendance(ListView lv, int limite = 20);
        void ChargerTagsRecents(ListView lv, int limite = 20);
        void ChargerTagsParCategorie(ListView lv, long categoryId);
        void ChargerTagsUtilisateur(ListView lv, long userId, int limite = 20);

        // Auto-suggestion et recherche
        List<Tags_Class> SuggererTags(string terme, int limite = 10);
        void RechercherTags(ListView lv, string terme);
        List<Tags_Class> ObtenirTagsSimilaires(long tagId, int limite = 5);
        List<Tags_Class> ObtenirTagsRelies(string tagName, int limite = 10);

        // Gestion des posts et tags
        void AssocierTagPost(long postId, long tagId);
        void DissocierTagPost(long postId, long tagId);
        void MettreAJourTagsPost(long postId, List<string> tagNames);
        List<Tags_Class> ObtenirTagsPost(long postId);
        void ChargerPostsParTag(ListView lv, long tagId, string filtre = "recent");

        // Statistiques et analytics
        void ChargerStatistiquesTag(long tagId, out int questions, out int reponses, out int utilisateurs);
        void ChargerActiviteTag(ListView lv, long tagId, int limite = 10);
        void ChargerExpertsTag(ListView lv, long tagId, int limite = 5);
        void ChargerQuestionsNonRepondues(ListView lv, long tagId, int limite = 10);

        // Gestion avancée
        void FusionnerTags(long tagSource, long tagDestination);
        void CreerSynonyme(long tagPrincipal, string synonyme);
        void SupprimerSynonyme(long tagId, string synonyme);
        List<string> ObtenirSynonymes(long tagId);

        // Modération
        void MarquerCommeRequis(long tagId, bool requis);
        void MarquerCommeModerator(long tagId, bool moderator);
        void DefinirWiki(long tagId, string wikiContent);
        void DefinirExtrait(long tagId, string excerpt);

        // Calculs et mises à jour
        void MettreAJourCompteur(long tagId);
        void MettreAJourTousCompteurs();
        void CalculerPopularite(long tagId);
        void MarquerCommeTendance(long tagId, bool tendance);

        // Export et import
        List<Tags_Class> ExporterTags();
        void ImporterTags(List<Tags_Class> tags);
        void GenererNuageTags(Literal literal, int limite = 100);
    }
}
