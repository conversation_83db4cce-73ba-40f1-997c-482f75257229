﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IMenuPermission
    {
        void AfficherDetails(int menuPermissionID, MenuPermission_Class menuPermissionClass);
        int Ajouter(MenuPermission_Class menuPermissionClass);
        void ChargerPermissionsMenu(GridView gdv, int menuID);
        void chargermenus(DropDownList lst);
        int Supprimer(int menuPermissionID);
    }
}
