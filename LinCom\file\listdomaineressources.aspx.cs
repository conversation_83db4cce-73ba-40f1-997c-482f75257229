﻿using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listdomaineressources : System.Web.UI.Page
    {


        DomaineRessource_Class cat = new DomaineRessource_Class();
        IDomaineRessource obj = new DomaineRessourceImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        int info;
        static string typenm; static int id, idpers, rol;
        static string imge, imge1, pdfe, nameorg;
        static long ide, idorg;
        static int rolid;
        long index;
        static string nsco;
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    ide = Convert.ToInt64(idperso.Value);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;


                getDataGDV();

            }
        }
        public void getDataGDV()
        {
            obj.Chargement_GDV(gdv,"",-1,idorg,-1,0 );
            //  nbr.Text = obj.count().ToString();

        }
        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            long index = Convert.ToInt64(e.CommandArgument);
            if (e.CommandName == "view")
            {
                Response.Redirect("~/file/domaineressources.aspx?id=" + index);

            }
            if (e.CommandName == "delete")
            {
                try
                {
                    info = obj.supprimer(index, idorg);
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listdomaineressources.aspx");
                    }
                    else
                    {
                        //  msg.Text = "Modification echoue";
                        //msg.Text = id.ToString();
                    }


                }
                catch (SqlException ex)
                {
                    // msg.Text = "Cette Province existe deja";
                }
            }
        }

        protected void btn_srch_Click(object sender, EventArgs e)
        {
            //if (txt_srch.Value == "")
            //    getDataGDV();
            //else obj.search(gdv, txt_srch.Value);
        }
    }
}