using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class CategoriesImp : ICategories
    {
        int msg;
        Categories p = new Categories();

        public int Add(Categories_Class category)
        {
            using (Connection con = new Connection())
            {
                p.Name = category.Name;
                p.Description = category.Description;
                p.Slug = category.Slug;
                p.IconClass = category.IconClass;
                p.Color = category.Color;
                p.ParentCategoryId = category.ParentCategoryId;
                p.SortOrder = category.SortOrder;
                p.IsActive = category.IsActive;
                p.CreatedDate = DateTime.Now;

                try
                {
                    con.Categories.Add(p);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public void AfficherDetails(long categoryId, Categories_Class category)
        {
            using (Connection con = new Connection())
            {
                p = con.Categories.Where(x => x.CategoryId == categoryId).FirstOrDefault();

                if (p != null)
                {
                    category.CategoryId = p.CategoryId;
                    category.Name = p.Name;
                    category.Description = p.Description;
                    category.Slug = p.Slug;
                    category.IconClass = p.IconClass;
                    category.Color = p.Color;
                    category.ParentCategoryId = p.ParentCategoryId;
                    category.SortOrder = p.SortOrder;
                    category.IsActive = p.IsActive;
                    category.CreatedDate = p.CreatedDate;

                    // Charger le nom de la catégorie parent
                    if (p.ParentCategoryId.HasValue)
                    {
                        var parent = con.Categories.Where(x => x.CategoryId == p.ParentCategoryId.Value).FirstOrDefault();
                        category.ParentCategoryName = parent?.Name ?? "";
                    }

                    // Calculer les statistiques
                    category.PostCount = con.SujetForums.Count(s => s.CategoryId == p.CategoryId);
                    category.QuestionCount = con.SujetForums.Count(s => s.CategoryId == p.CategoryId && s.PostTypeId == 1);
                    category.AnswerCount = con.SujetForums.Count(s => s.CategoryId == p.CategoryId && s.PostTypeId == 2);

                    // Dernière activité
                    var lastActivity = con.SujetForums.Where(s => s.CategoryId == p.CategoryId)
                                                     .OrderByDescending(s => s.LastActivityDate)
                                                     .FirstOrDefault();
                    if (lastActivity != null)
                    {
                        category.LastActivityDate = lastActivity.LastActivityDate;
                        category.LastActivityUser = lastActivity.OwnerDisplayName;
                    }
                }
            }
        }

        public void Chargement_GDV(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var obj = (from c in con.Categories
                           select new
                           {
                               CategoryId = c.CategoryId,
                               Name = c.Name,
                               Description = c.Description,
                               Slug = c.Slug,
                               IconClass = c.IconClass,
                               Color = c.Color,
                               ParentCategoryName = c.ParentCategoryId.HasValue ? 
                                   con.Categories.Where(p => p.CategoryId == c.ParentCategoryId.Value).Select(p => p.Name).FirstOrDefault() : "",
                               SortOrder = c.SortOrder,
                               IsActive = c.IsActive,
                               CreatedDate = c.CreatedDate,
                               PostCount = con.SujetForums.Count(s => s.CategoryId == c.CategoryId)
                           }).OrderBy(c => c.SortOrder).ToList();

                gdv.DataSource = obj;
                gdv.DataBind();
            }
        }

        public void ChargerCategoriesActives(ListView lv)
        {
            using (Connection con = new Connection())
            {
                var categories = from c in con.Categories
                                where c.IsActive == true
                                orderby c.SortOrder, c.Name
                                select new
                                {
                                    c.CategoryId,
                                    c.Name,
                                    c.Description,
                                    c.Slug,
                                    c.IconClass,
                                    c.Color,
                                    PostCount = con.SujetForums.Count(s => s.CategoryId == c.CategoryId),
                                    QuestionCount = con.SujetForums.Count(s => s.CategoryId == c.CategoryId && s.PostTypeId == 1),
                                    AnswerCount = con.SujetForums.Count(s => s.CategoryId == c.CategoryId && s.PostTypeId == 2),
                                    LastActivity = con.SujetForums.Where(s => s.CategoryId == c.CategoryId)
                                                                  .OrderByDescending(s => s.LastActivityDate)
                                                                  .Select(s => s.LastActivityDate)
                                                                  .FirstOrDefault(),
                                    LastUser = con.SujetForums.Where(s => s.CategoryId == c.CategoryId)
                                                              .OrderByDescending(s => s.LastActivityDate)
                                                              .Select(s => s.OwnerDisplayName)
                                                              .FirstOrDefault()
                                };

                lv.DataSource = categories.ToList();
                lv.DataBind();
            }
        }

        public void ChargerCategoriesAvecStatistiques(ListView lv)
        {
            using (Connection con = new Connection())
            {
                var categories = from c in con.Categories
                                where c.IsActive == true
                                select new
                                {
                                    c.CategoryId,
                                    c.Name,
                                    c.Description,
                                    c.Slug,
                                    c.IconClass,
                                    c.Color,
                                    PostCount = con.SujetForums.Count(s => s.CategoryId == c.CategoryId),
                                    QuestionCount = con.SujetForums.Count(s => s.CategoryId == c.CategoryId && s.PostTypeId == 1),
                                    AnswerCount = con.SujetForums.Count(s => s.CategoryId == c.CategoryId && s.PostTypeId == 2),
                                    ViewCount = con.SujetForums.Where(s => s.CategoryId == c.CategoryId).Sum(s => (int?)s.ViewCount) ?? 0,
                                    TotalScore = con.SujetForums.Where(s => s.CategoryId == c.CategoryId).Sum(s => (int?)s.Score) ?? 0,
                                    ActiveUsers = con.SujetForums.Where(s => s.CategoryId == c.CategoryId && s.CreationDate >= DateTime.Now.AddDays(-30))
                                                                 .Select(s => s.OwnerUserId)
                                                                 .Distinct()
                                                                 .Count(),
                                    LastActivity = con.SujetForums.Where(s => s.CategoryId == c.CategoryId)
                                                                  .OrderByDescending(s => s.LastActivityDate)
                                                                  .Select(s => s.LastActivityDate)
                                                                  .FirstOrDefault()
                                };

                lv.DataSource = categories.OrderByDescending(c => c.PostCount).ToList();
                lv.DataBind();
            }
        }

        public void ChargerCategoriesParent(DropDownList ddl)
        {
            ddl.Items.Clear();
            using (Connection con = new Connection())
            {
                var categories = con.Categories.Where(c => c.IsActive == true && c.ParentCategoryId == null)
                                              .OrderBy(c => c.SortOrder)
                                              .ThenBy(c => c.Name)
                                              .ToList();

                if (categories != null && categories.Count() > 0)
                {
                    ListItem item0 = new ListItem();
                    item0.Value = "";
                    item0.Text = "Sélectionner une catégorie parent";
                    ddl.Items.Add(item0);

                    foreach (var category in categories)
                    {
                        ListItem item = new ListItem();
                        item.Value = category.CategoryId.ToString();
                        item.Text = category.Name;
                        ddl.Items.Add(item);
                    }
                }
            }
        }

        public void ChargerCategoriesPopulaires(ListView lv, int limite = 10)
        {
            using (Connection con = new Connection())
            {
                var categories = from c in con.Categories
                                where c.IsActive == true
                                let postCount = con.SujetForums.Count(s => s.CategoryId == c.CategoryId)
                                where postCount > 0
                                orderby postCount descending
                                select new
                                {
                                    c.CategoryId,
                                    c.Name,
                                    c.Description,
                                    c.Slug,
                                    c.IconClass,
                                    c.Color,
                                    PostCount = postCount,
                                    QuestionCount = con.SujetForums.Count(s => s.CategoryId == c.CategoryId && s.PostTypeId == 1),
                                    RecentActivity = con.SujetForums.Count(s => s.CategoryId == c.CategoryId && s.CreationDate >= DateTime.Now.AddDays(-7))
                                };

                lv.DataSource = categories.Take(limite).ToList();
                lv.DataBind();
            }
        }

        public int Count()
        {
            using (Connection con = new Connection())
            {
                return con.Categories.Count();
            }
        }

        public int Edit(Categories_Class category, long categoryId)
        {
            using (Connection con = new Connection())
            {
                p = con.Categories.Where(x => x.CategoryId == categoryId).FirstOrDefault();

                try
                {
                    p.Name = category.Name;
                    p.Description = category.Description;
                    p.Slug = category.Slug;
                    p.IconClass = category.IconClass;
                    p.Color = category.Color;
                    p.ParentCategoryId = category.ParentCategoryId;
                    p.SortOrder = category.SortOrder;
                    p.IsActive = category.IsActive;

                    if (con.SaveChanges() == 1)
                    {
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public void Search(GridView gdv, string searchTerm)
        {
            using (Connection con = new Connection())
            {
                var obj = (from c in con.Categories
                           where c.Name.Contains(searchTerm) || c.Description.Contains(searchTerm)
                           select new
                           {
                               CategoryId = c.CategoryId,
                               Name = c.Name,
                               Description = c.Description,
                               Slug = c.Slug,
                               IsActive = c.IsActive,
                               PostCount = con.SujetForums.Count(s => s.CategoryId == c.CategoryId)
                           }).ToList();

                gdv.DataSource = obj;
                gdv.DataBind();
            }
        }

        public int Supprimer(long categoryId)
        {
            using (Connection con = new Connection())
            {
                p = con.Categories.Where(x => x.CategoryId == categoryId).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.Categories.Attach(p);

                con.Categories.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }

        // Méthodes supplémentaires à implémenter...
        public void ChargerSousCategoriesParent(ListView lv, long parentId) { throw new NotImplementedException(); }
        public void ChargerCategoriesTendance(ListView lv, int limite = 5) { throw new NotImplementedException(); }
        public void ChargerArbreCategories(TreeView tv) { throw new NotImplementedException(); }
        public void ChargerCategoriesHierarchiques(ListView lv, long? parentId = null) { throw new NotImplementedException(); }
        public List<Categories_Class> ObtenirCheminCategorie(long categoryId) { throw new NotImplementedException(); }
        public bool ADesSousCategories(long categoryId) { throw new NotImplementedException(); }
        public int CompterSousCategories(long categoryId) { throw new NotImplementedException(); }
        public void ChargerStatistiquesCategorie(long categoryId, out int questions, out int reponses, out int membres) { throw new NotImplementedException(); }
        public void ChargerActiviteRecente(ListView lv, long categoryId, int limite = 10) { throw new NotImplementedException(); }
        public void ChargerMembresActifs(ListView lv, long categoryId, int limite = 5) { throw new NotImplementedException(); }
        public void ChargerQuestionsPopulaires(ListView lv, long categoryId, int limite = 10) { throw new NotImplementedException(); }
        public void ChargerTagsCategorie(ListView lv, long categoryId) { throw new NotImplementedException(); }
        public void AssocierTagCategorie(long categoryId, long tagId) { throw new NotImplementedException(); }
        public void DissocierTagCategorie(long categoryId, long tagId) { throw new NotImplementedException(); }
        public void RechercherCategories(ListView lv, string terme) { throw new NotImplementedException(); }
        public void FiltrerParActivite(ListView lv, DateTime dateDebut, DateTime dateFin) { throw new NotImplementedException(); }
        public void ChargerCategoriesParNiveau(ListView lv, int niveau) { throw new NotImplementedException(); }
        public void ReorganiserOrdre(long categoryId, int nouvelOrdre) { throw new NotImplementedException(); }
        public void ChangerStatut(long categoryId, bool actif) { throw new NotImplementedException(); }
        public void FusionnerCategories(long categorieSource, long categorieDestination) { throw new NotImplementedException(); }
        public void DeplacerSousCategorie(long categorieId, long? nouveauParentId) { throw new NotImplementedException(); }
    }
}
