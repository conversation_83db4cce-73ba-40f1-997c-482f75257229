<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="faq-public.aspx.cs" Inherits="LinCom.faq_public" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Titre de la page -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2 class="display-5 fw-bold">Questions Fréquemment Posées</h2>
                        <p class="lead">Trouvez rapidement les réponses aux questions les plus courantes sur notre plateforme.</p>
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Accueil</a></li>
                    <li class="current">FAQ</li>
                </ol>
            </div>
        </nav>
    </div>

    <main class="main">
        <!-- Section FAQ -->
        <section class="faq-section section">
            <div class="container">
                <div class="row">
                    <!-- Colonne principale : FAQ -->
                    <div class="col-lg-8">
                        <div class="faq-container">
                            <asp:ListView ID="listFAQ" runat="server">
                                <EmptyItemTemplate>
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i>
                                        Aucune FAQ disponible pour le moment.
                                    </div>
                                </EmptyItemTemplate>
                                <ItemTemplate>
                                    <div class="faq-item mb-4">
                                        <div class="card shadow-sm">
                                            <div class="card-header bg-primary text-white">
                                                <h5 class="mb-0">
                                                    <button class="btn btn-link text-white text-decoration-none w-100 text-start" type="button" 
                                                            data-bs-toggle="collapse" data-bs-target="#faq<%# Eval("id") %>" 
                                                            aria-expanded="false" aria-controls="faq<%# Eval("id") %>">
                                                        <i class="bi bi-question-circle me-2"></i>
                                                        <%# HttpUtility.HtmlEncode(Eval("Question")) %>
                                                        <i class="bi bi-chevron-down float-end"></i>
                                                    </button>
                                                </h5>
                                            </div>
                                            <div id="faq<%# Eval("id") %>" class="collapse">
                                                <div class="card-body">
                                                    <div class="faq-answer">
                                                        <i class="bi bi-chat-left-text text-success me-2"></i>
                                                        <%# HttpUtility.HtmlEncode(Eval("Reponse")) %>
                                                    </div>
                                                    <div class="faq-meta mt-3 text-muted small">
                                                        <i class="bi bi-calendar3"></i>
                                                        Publié le <%# Eval("DatePublication", "{0:dd/MM/yyyy}") %>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:ListView>
                        </div>
                    </div>

                    <!-- Colonne droite : Recherche et catégories -->
                    <div class="col-lg-4">
                        <div class="faq-sidebar">
                            <!-- Recherche -->
                            <div class="search-box mb-4">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="bi bi-search"></i> Rechercher dans la FAQ</h6>
                                    </div>
                                    <div class="card-body">
                                        <form id="searchForm" runat="server">
                                            <div class="input-group">
                                                <input type="text" id="txtRecherche" runat="server" class="form-control" placeholder="Tapez votre question...">
                                                <button type="button" id="btnRechercher" runat="server" onserverclick="btnRechercher_ServerClick" class="btn btn-primary">
                                                    <i class="bi bi-search"></i>
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact -->
                            <div class="contact-box">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-headset display-4 text-primary mb-3"></i>
                                        <h6>Besoin d'aide supplémentaire ?</h6>
                                        <p class="small text-muted">Si vous ne trouvez pas la réponse à votre question, n'hésitez pas à nous contacter.</p>
                                        <a href="contact.aspx" class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-envelope"></i> Nous contacter
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <style>
        .faq-item .card-header button {
            font-weight: 500;
        }
        
        .faq-item .card-header button:hover {
            background-color: rgba(255,255,255,0.1);
        }
        
        .faq-answer {
            line-height: 1.6;
            color: #444;
        }
        
        .faq-meta {
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
        
        .search-box .input-group {
            border-radius: 25px;
            overflow: hidden;
        }
        
        .search-box .form-control {
            border-right: none;
        }
        
        .search-box .btn {
            border-left: none;
        }
        
        .contact-box {
            position: sticky;
            top: 20px;
        }
    </style>
</asp:Content>
