﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IProjet
    {
        void AfficherDetails(long projetId, Projet_Class projet);
        int Ajouter(Projet_Class projet);
        int Modifier(Projet_Class projet);
        int Supprimer(long projetId);
        void ChargerGridView(GridView gdv);
        void ChargerEquipeProjet(GridView gdv, long projetId);
        int AjouterMembreEquipe(long projetId, long membreId, string role);
        int RetirerMembreEquipe(long projetId, long membreId);
        void chargerProjet(DropDownList lst);
        
      
        
    }
}
