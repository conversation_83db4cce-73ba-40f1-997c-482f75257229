﻿-- Provinces
CREATE TABLE Provinces (
    ProvinceId INT PRIMARY KEY IDENTITY(1,1),
    Nom NVARCHAR(100) NOT NULL,
    name NVARCHAR(100) NOT NULL,
);

-- Commune
CREATE TABLE Commune (
    CommuneId INT PRIMARY KEY IDENTITY(1,1),
    Nom NVARCHAR(100) NOT NULL,
    ProvinceId INT NOT NULL,
    name NVARCHAR(100) NOT NULL,
   
);
-- TypeOrganisation
CREATE TABLE TypeOrganisation (
    TypeOrganisationId INT PRIMARY KEY IDENTITY(1,1),
    Libelle NVARCHAR(100) NOT NULL,
    name NVARCHAR(100) NOT NULL,
);

-- Organisation
CREATE TABLE [dbo].[Organisation] (
    [OrganisationId]     BIGINT         IDENTITY (1, 1) NOT NULL,
    [Nom]                NVARCHAR (150) NOT NULL,
    [TypeOrganisationId] INT            NOT NULL,
    [ProvinceId]         INT            NOT NULL,
    [CommuneId]          INT            NOT NULL,
    [Email]              NVARCHAR (150) NULL,
    [Telephone]          NVARCHAR (50)  NULL,
    [SiteWeb]            NVARCHAR (150) NULL,
    [Logo]               NVARCHAR (255) NULL,
    [RS]                 VARCHAR (100)  NOT NULL,
    [sigle]              VARCHAR (100)  NULL,
    [Description]        NVARCHAR (MAX) NULL,
    [Vision]             NVARCHAR (MAX) NULL,
    [Mission]            NVARCHAR (MAX) NULL,
    [NbreHomme]          INT            NULL,
    [NbreFemme]          INT            NULL,
    [Enregistre]         VARCHAR (100)  NULL,
    [RC]                 VARCHAR (100)  NULL,
    [RC_doc]             VARCHAR (100)  NULL,
    [NIF]                VARCHAR (100)  NULL,
    [NIF_doc]            VARCHAR (100)  NULL,
    [facebook]           VARCHAR (100)  NULL,
    [twitter]            VARCHAR (100)  NULL,
    [instagramme]        VARCHAR (100)  NULL,
    [linkedin]           VARCHAR (100)  NULL,
    [youtube]            VARCHAR (100)  NULL,
    [province]           NVARCHAR (100) NOT NULL,
    [commune]            NVARCHAR (100) NOT NULL,
    [DateCreation]       DATETIME       NULL,
    [Statut]             NVARCHAR (50)  DEFAULT ('actif') NULL,
    [CreatedAt]          DATETIME2 (7)  DEFAULT (getdate()) NULL,
    [UpdatedAt]          DATETIME2 (7)  DEFAULT (getdate()) NULL,
    [name]               NVARCHAR (100) NOT NULL,
    PRIMARY KEY CLUSTERED ([OrganisationId] ASC)
);


-- DomaineIntervention
CREATE TABLE DomaineIntervention (
    DomaineInterventionId INT PRIMARY KEY IDENTITY(1,1),
    Libelle NVARCHAR(100) NOT NULL,
    name NVARCHAR(100) NOT NULL,
);

-- DomaineInterventionOrganisation
CREATE TABLE DomaineInterventionOrganisation (
    DomaineInterventionOrganisationId INT PRIMARY KEY IDENTITY(1,1),
    DomaineInterventionId INT NOT NULL,
    OrganisationId BIGINT NOT NULL,
    name NVARCHAR(100) NOT NULL,
   
);
-- Membres
CREATE TABLE [dbo].[Membres] (
    [MembreId]      BIGINT         IDENTITY (1, 1) NOT NULL,
    [Nom]           NVARCHAR (100) NOT NULL,
    [Prenom]        NVARCHAR (100) NOT NULL,
    [Email]         NVARCHAR (150) NULL,
    [Telephone]     NVARCHAR (50)  NULL,
    [Sexe]          NVARCHAR (10)  NULL,
    [DateNaissance] DATE           NULL,
    [ProvinceId]    INT            NULL,
    [CommuneId]     INT            NULL,
    [CreatedAt]     DATETIME2 (7)  DEFAULT (getdate()) NULL,
    [name]          NVARCHAR (100) NOT NULL,
    [province]      NVARCHAR (100) NOT NULL,
    [commune]       NVARCHAR (100) NOT NULL,
    [username]       NVARCHAR (150) unique NOT NULL,
    [motpasse]       NVARCHAR (100) NOT NULL,
	 [statut]                NVARCHAR (50)  DEFAULT ('actif') NULL,
	IsActive BIT DEFAULT 1,                -- Pour activer/désactiver un compte
    IsVerified BIT DEFAULT 0,              -- Pour savoir si l'email a été vérifié
    LastLogin DATETIME2 NULL,              -- Dernière connexion
    RoleMembreID INT,     -- Pour attribuer des rôles (admin, user, etc.)
    PRIMARY KEY CLUSTERED ([MembreId] ASC),
    CHECK ([Sexe]='Féminin' OR [Sexe]='Masculin')
);



-- MembreProfil
CREATE TABLE MembreProfil (
    MembreProfilId BIGINT PRIMARY KEY IDENTITY(1,1),
    MembreId BIGINT NOT NULL,
    PhotoProfil NVARCHAR(255),
    [facebook]                VARCHAR (100) NULL,
    [siteweb]                 VARCHAR (100) NULL,
    [twitter]                 VARCHAR (100) NULL,
    [instagramme]             VARCHAR (100) NULL,
    [linkedin]                VARCHAR (100) NULL,
    [youtube]                 VARCHAR (100) NULL,
    Biographie NVARCHAR(MAX),
    DateInscription DATETIME,
    name NVARCHAR(100) NOT NULL,
   
);

-- MembresOrganisation
CREATE TABLE MembresOrganisation (
    MembresOrganisationId BIGINT PRIMARY KEY IDENTITY(1,1),
    MembreId BIGINT NOT NULL,
    OrganisationId BIGINT NOT NULL,
    Poste NVARCHAR(100),
    DateAdhesion DATETIME,
    name NVARCHAR(100) NOT NULL,
    statut NVARCHAR(50) DEFAULT 'actif', --'actif', 'inactif', 'suspendu', 'en_attente' DEFAULT 'actif',
     RoleMembreID INT,     -- Pour attribuer des rôles (admin, user, etc.)
   
);
--Cette table permet de suivre les connexions des membres.
CREATE TABLE [dbo].[Sessions] (
    SessionId UNIQUEIDENTIFIER DEFAULT NEWID() PRIMARY KEY,
    MembreId BIGINT NOT NULL,
    IPAddress NVARCHAR(45) NULL,
    UserAgent NVARCHAR(300) NULL,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    ExpiresAt DATETIME2 NULL,
    IsValid BIT DEFAULT 1,
    FOREIGN KEY (MembreId) REFERENCES [dbo].[Membres](MembreId)
);
--Utile pour générer un lien de réinitialisation sécurisé.
CREATE TABLE [dbo].[PasswordResets] (
    ResetId UNIQUEIDENTIFIER DEFAULT NEWID() PRIMARY KEY,
    MembreId BIGINT NOT NULL,
    ResetToken NVARCHAR(255) NOT NULL,
    ExpiresAt DATETIME2 NOT NULL,
    Used BIT DEFAULT 0,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (MembreId) REFERENCES [dbo].[Membres](MembreId)
);


-- Categories et Postes
CREATE TABLE CategoriePost (
    CategoriePostId INT PRIMARY KEY IDENTITY(1,1),
    Libelle NVARCHAR(100) NOT NULL,
     name NVARCHAR(100) NOT NULL,
);

CREATE TABLE [dbo].[Post] (
    [PostId]              BIGINT         IDENTITY (1, 1) NOT NULL,
    [Titre]               NVARCHAR (150) NOT NULL,
    [Contenu]             NVARCHAR (MAX) NULL,
    [CategoriePostId]     INT            NULL,
    [MembreId]            BIGINT         NULL,
    [DatePublication]     DATETIME       NULL,
    [summery]             NVARCHAR (MAX) NULL,
    [author]              VARCHAR (500)  NULL,
    [photo]               VARCHAR (100)  NULL,
    [video]               VARCHAR (100)  NULL,
    [number_of_view]      INT            DEFAULT ('0') NULL,
    [like]                INT            DEFAULT ('0') NULL,
    [dislike]             INT            DEFAULT ('0') NULL,
    [starttime]           VARCHAR (100)  NULL,
    [eventduration]       VARCHAR (100)  NULL,
    [eventplace]          VARCHAR (100)  NULL,
    [whoattend]           VARCHAR (100)  NULL,
    [qualificationattend] TEXT           NULL,
    [langueevent]         VARCHAR (100)  NULL,
    [externevent]         VARCHAR (100)  NULL,
    [MOIS]                VARCHAR (100)  NULL,
    [ANNEE]               VARCHAR (100)  NULL,
    [lien_isncription]    TEXT           NULL,
    [pdf]                 VARCHAR (100)  NULL,
    [name]                NVARCHAR (100) NOT NULL,
    [DateCreation]        DATETIME       DEFAULT (getdate()) NULL,
    [DateModification]    DATETIME       DEFAULT (getdate()) NULL,
    [EstPublie]           VARCHAR (100)  NULL,
     OrganisationId BIGINT,
    PRIMARY KEY CLUSTERED ([PostId] ASC)
);

   -- DomainePost
CREATE TABLE DomainePost (
    DomainePostId BIGINT PRIMARY KEY IDENTITY(1,1),
    PostId BIGINT NOT NULL,
    DomaineInterventionOrganisationId INT,
    DateCreation DATETIME,
    statut varchar(100),
     MembreId BIGINT,
   OrganisationId BIGINT, 
);



-- CommentPost
CREATE TABLE CommentPost (
    CommentPostId BIGINT PRIMARY KEY IDENTITY(1,1),
    PostId INT NOT NULL,
    MembreId BIGINT NOT NULL,
    Contenu NVARCHAR(MAX),
    DateCommentaire DATETIME,
    EstVisible VARCHAR(100),
    Nbrevue INT,
     name NVARCHAR(100) NOT NULL,
   
);

-- Forum
CREATE TABLE Forum (
    ForumId BIGINT PRIMARY KEY IDENTITY(1,1),
    Nom NVARCHAR(150) NOT NULL,
     name NVARCHAR(100) NOT NULL,
);

-- SujetForum
CREATE TABLE SujetForum (
    SujetForumId BIGINT PRIMARY KEY IDENTITY(1,1),
    Titre NVARCHAR(150) NOT NULL,
    ForumId BIGINT NOT NULL,
    MembreId BIGINT NOT NULL,
    DateCreation DATETIME,
    name NVARCHAR(100) NOT NULL,
);

-- RepliesForum
CREATE TABLE RepliesForum (
    RepliesForumId BIGINT PRIMARY KEY IDENTITY(1,1),
    SujetForumId BIGINT NOT NULL,
    MembreId BIGINT NOT NULL,
    Contenu NVARCHAR(MAX),
    DateReply DATETIME,
   name NVARCHAR(100) NOT NULL,
);

-- Formation
CREATE TABLE Formation (
    FormationId BIGINT PRIMARY KEY IDENTITY(1,1),
    Titre NVARCHAR(150) NOT NULL,
    Description NVARCHAR(MAX),
    DateFormation DATETIME,
     name NVARCHAR(100) NOT NULL,
      DateCreation DATETIME,
      statut varchar(100),
      DatePublication varchar(100),

);
-- DomaineFormation
CREATE TABLE DomaineFormation (
    DomaineFormationId BIGINT PRIMARY KEY IDENTITY(1,1),
    FormationId BIGINT NOT NULL,
    DomaineInterventionId INT,
    DateCreation DATETIME,
    statut varchar(100),
     MembreId BIGINT,
     
);

-- SupportFormation
CREATE TABLE SupportFormation (
    SupportFormationId INT PRIMARY KEY IDENTITY(1,1),
    FormationId BIGINT NOT NULL,
    Titre NVARCHAR(150),
    Fichier NVARCHAR(255),
     name NVARCHAR(100) NOT NULL,
      DateCreation DATETIME,
        statut varchar(100),
      DatePublication varchar(100),
   
);

-- Langue
CREATE TABLE Langue (
    LangueId INT PRIMARY KEY IDENTITY(1,1),
    Code NVARCHAR(10) NOT NULL,
    Nom NVARCHAR(50) NOT NULL,
  
);

-- Traduction
CREATE TABLE Traduction (
    TraductionId INT PRIMARY KEY IDENTITY(1,1),
    Cle NVARCHAR(100) NOT NULL,
    LangueId INT NOT NULL,
    Texte NVARCHAR(MAX),
  
);

-- ParamettreApplication
CREATE TABLE ParamettreApplication (
    ParamettreId INT PRIMARY KEY IDENTITY(1,1),
    Cle NVARCHAR(100) NOT NULL,
    Valeur NVARCHAR(MAX)
);

-- Programmes et Initiative
CREATE TABLE ProgrammesEtInitiative (
    ProgrammeId INT PRIMARY KEY IDENTITY(1,1),
    Nom NVARCHAR(150),
    Description NVARCHAR(MAX),
    DateLancement DATETIME,
     name NVARCHAR(100) NOT NULL,
       statut NVARCHAR(100) NOT NULL,
);
-- Ressources
CREATE TABLE Ressources (
    RessourceId BIGINT PRIMARY KEY IDENTITY(1,1),
    Titre NVARCHAR(150),
    Description NVARCHAR(MAX),
    photocouverture varchar(100),
    nombrepage int,
    typeressources varchar(50),
     OrganisationId BIGINT,
     nbrevue int,
     nbrelike int,
    
    Fichier NVARCHAR(255),
    DateAjout DATETIME,
    AuteurId INT,
       DatePublication DATETIME,
     name NVARCHAR(100) NOT NULL,
   
);
-- DomaineFormation
CREATE TABLE DomaineRessources (
    DomaineResourceId BIGINT PRIMARY KEY IDENTITY(1,1),
    RessourceId BIGINT NOT NULL,
    DomaineInterventionId INT,
    DateCreation DATETIME,
    statut varchar(100),
     MembreId BIGINT,
 
     
);


-- TelechargementRessources
CREATE TABLE TelechargementRessources (
    TelechargementId INT PRIMARY KEY IDENTITY(1,1),
    RessourceId BIGINT,
    MembreId BIGINT,
    DateTelechargement DATETIME,
     name NVARCHAR(100) NOT NULL,
   
);

-- AvisRessources
CREATE TABLE AvisRessources (
    AvisId INT PRIMARY KEY IDENTITY(1,1),
    RessourceId BIGINT,
    MembreId BIGINT,
    Note INT,
    Commentaire NVARCHAR(MAX),
    DateAvis DATETIME,
  
);

-- Programmementorat
CREATE TABLE Programmementorat (
    ProgrammeMentoratId INT PRIMARY KEY IDENTITY(1,1),
    Titre NVARCHAR(150),
    Description NVARCHAR(MAX),
    DateDebut DATETIME,
    DateFin DATETIME,
    name NVARCHAR(100) NOT NULL,
    auteur varchar(150),
    Dateenreg Datetime,
    MembreId BIGINT
);

-- Mentor
CREATE TABLE Mentor (
    MentorId INT PRIMARY KEY IDENTITY(1,1),
    MembreId BIGINT,
   
    DomaineExpertise NVARCHAR(150),
     name NVARCHAR(100) NOT NULL,
       ProgrammeMentoratId INT ,
     status varchar(100),
);

-- Mentore
CREATE TABLE Mentore (
    MentoreId INT PRIMARY KEY IDENTITY(1,1),
    MembreId BIGINT,
     ProgrammeMentoratId INT,
     status varchar(100)
   
);

-- SessionMentorat
CREATE TABLE SessionMentorat (
    SessionMentoratId BIGINT PRIMARY KEY IDENTITY(1,1),
    ProgrammeMentoratId INT,
    MentorId INT,
    MentoreId INT,
    DateSession DATETIME,
    Sujet NVARCHAR(150),
    Notes NVARCHAR(MAX),
   
);

-- FeedbackMentor
CREATE TABLE FeedbackMentor (
    FeedbackId INT PRIMARY KEY IDENTITY(1,1),
    SessionMentoratId BIGINT,
    Commentaire NVARCHAR(MAX),
    Note INT,
   
);

-- FeedbackMentoree
CREATE TABLE FeedbackMentoree (
    FeedbackId BIGINT PRIMARY KEY IDENTITY(1,1),
    SessionMentoratId BIGINT,
    Commentaire NVARCHAR(MAX),
    Note INT,
   );

-- Projet
CREATE TABLE Projet (
    ProjetId BIGINT PRIMARY KEY IDENTITY(1,1),
    Titre NVARCHAR(150),
    Description NVARCHAR(MAX),
    MontantProjet FLOAT(53),
    DateDebut DATETIME,
    DateFin DATETIME,
    OrganisationId BIGINT,
     MembreId BIGINT,
     name NVARCHAR(100) NOT NULL,
     status varchar(100),
     etat varchar(100),
     pdf vachar(100)
   );

   -- DomaineFormation
CREATE TABLE DomaineProjet (
    DomaineProjetId BIGINT PRIMARY KEY IDENTITY(1,1),
    ProjetId BIGINT NOT NULL,
    DomaineInterventionId INT,
    DateCreation DATETIME,
    statut varchar(100),
     MembreId BIGINT,
   OrganisationId BIGINT, 
);

-- Financement
CREATE TABLE Financement (
    FinancementId BIGINT PRIMARY KEY IDENTITY(1,1),
    ProjetId BIGINT,
    Montant FLOAT(53),
    Intitulefinancement NVARCHAR(150),
    Source NVARCHAR(150),
      PartenaireId INT,
    DateFinancement DATETIME,
      Dateenreg DATETIME,
     OrganisationId BIGINT, 
       statut varchar(100),
     MembreId BIGINT,
);

-- ActiviteProjet
CREATE TABLE ActiviteProjet (
    ActiviteProjetId BIGINT PRIMARY KEY IDENTITY(1,1),
    ProjetId BIGINT,
    Titre NVARCHAR(150),
    Description NVARCHAR(MAX),
    DateActivite DATETIME,
     montant_prevu FLOAT(53),
    montant_realise FLOAT(53),
    observation TEXT,
    MembreId BIGINT,
     OrganisationId BIGINT,
     photo1 varchar(100),
      photo2 varchar(100),
       statut varchar(100),
      DateActiviteEnreg DATETIME,
    name NVARCHAR(100) NOT NULL,
    
);


-- FAQ
CREATE TABLE FAQ (
    FAQId INT PRIMARY KEY IDENTITY(1,1),
    Question NVARCHAR(255),
    Reponse NVARCHAR(MAX),
    statut varchar(100),
      DatePublication DATETIME,
    name NVARCHAR(100) NOT NULL,
);

-- PolitiqueConfidentialite
CREATE TABLE PolitiqueConfidentialite (
    PolitiqueId INT PRIMARY KEY IDENTITY(1,1),
    Contenu NVARCHAR(MAX),
    DatePublication DATETIME,
    name NVARCHAR(100) NOT NULL,
      CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE(),
);

-- ConditionUtilisation
CREATE TABLE ConditionUtilisation (
    ConditionId INT PRIMARY KEY IDENTITY(1,1),
    Contenu NVARCHAR(MAX),
    DatePublication DATETIME,
     name NVARCHAR(100) NOT NULL,
       CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE(),
);

-- Conversation
CREATE TABLE Conversation (
    ConversationId BIGINT PRIMARY KEY IDENTITY(1,1),
    Sujet NVARCHAR(150),
    name NVARCHAR(100) NOT NULL,
);

-- ParticipantConversation
CREATE TABLE ParticipantConversation (
    ParticipantId BIGINT PRIMARY KEY IDENTITY(1,1),
    ConversationId BIGINT,
    MembreId BIGINT,
   
);

-- Message
CREATE TABLE Message (
    MessageId BIGINT PRIMARY KEY IDENTITY(1,1),
    ConversationId BIGINT,
    AuteurId INT,
    Contenu NVARCHAR(MAX),
    DateEnvoi DATETIME,
   name NVARCHAR(100) NOT NULL,
);

-- FichierMessage
CREATE TABLE FichierMessage (
    FichierId INT PRIMARY KEY IDENTITY(1,1),
    MessageId INT,
    NomFichier NVARCHAR(255),
    UrlFichier NVARCHAR(255),
     name NVARCHAR(100) NOT NULL,
   
);

-- Notification
CREATE TABLE Notification (
    NotificationId BIGINT PRIMARY KEY IDENTITY(1,1),
    MembreId BIGINT,
    Titre NVARCHAR(150),
    Message NVARCHAR(MAX),
    DateNotification DATETIME,
    Lu BIT DEFAULT 0,
     name NVARCHAR(100) NOT NULL,
  
);
-- PreferenceCookies
CREATE TABLE PreferenceCookies (
    PreferenceId INT PRIMARY KEY IDENTITY(1,1),
    MembreId BIGINT,
    Acceptation BIT,
    DateChoix DATETIME,
     name NVARCHAR(100) NOT NULL,
  
);

-- Subscription
CREATE TABLE Subscription (
    SubscriptionId BIGINT PRIMARY KEY IDENTITY(1,1),
    MembreId BIGINT,
    Email NVARCHAR(150),
    DateAbonnement DATETIME,
  
);

-- StatutUtilisateur
CREATE TABLE StatutUtilisateur (
    StatutId INT PRIMARY KEY IDENTITY(1,1),
    MembreId BIGINT,
    Libelle NVARCHAR(50)
);


-- rolemembre
CREATE TABLE rolemembre (
    RoleMembreID INT PRIMARY KEY IDENTITY(1,1),
    NomRole NVARCHAR(100) NOT NULL,
   
    name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255),
     [TYPE]     VARCHAR (100) NULL,
    [CDROLE]   VARCHAR (100) NULL,
     OrganisationId BIGINT, 
    statut varchar(100)
);
insert into rolemembre(NomRole,name,Description,TYPE,CDROLE,OrganisationId,statut) values('Administrateur','administrateur','','administrateur','1',-1,'actif')
insert into rolemembre(NomRole,name,Description,TYPE,CDROLE,OrganisationId,statut) values('Membre','membre','','membre','2',-1,'actif')
insert into rolemembre(NomRole,name,Description,TYPE,CDROLE,OrganisationId,statut) values('Organisation','organisation','','organisation','3',-1,'actif')

-- permission
CREATE TABLE permission (
    PermissionID INT PRIMARY KEY IDENTITY(1,1),
    CodePermission NVARCHAR(50) NOT NULL,
     [id_role]   INT           NOT NULL,
    [Code_Menu] BIGINT        NOT NULL,
    [Access]    VARCHAR (100) NULL,
     OrganisationId BIGINT, 
    Description NVARCHAR(255)
);

-- permission

CREATE TABLE menu (
    MenuID INT PRIMARY KEY IDENTITY(1,1),
    NomMenu NVARCHAR(100) NOT NULL,
    UrlMenu NVARCHAR(255),
    Icone NVARCHAR(50),
    ParentID INT NULL,
    Ordre INT NOT NULL,
   
);

-- menupermission
CREATE TABLE menupermission (
    MenuPermissionID INT PRIMARY KEY IDENTITY(1,1),
    MenuID INT NOT NULL,
    PermissionID INT NOT NULL,
  
);
