﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IDomaineInterventionOrganisation
    {
        void AfficherDetails(int dom, DomaineInterventionOrganisation_Class domaineInterventionOrganisationClass, long org, int cd); int Ajouter(DomaineInterventionOrganisation_Class domaineInterventionOrganisationClass);
        void ChargerDomainesInterventionOrganisation(GridView gdv, long organisationId);
        int Modifier(int id,DomaineInterventionOrganisation_Class domaineInterventionOrganisationClass);
        int Supprimer(int domaineInterventionOrganisationId);
        void AfficherDetails(string code, DomaineInterventionOrganisation_Class domaineInterventionOrganisationClass);
        void chargerDomaineInterventionOrganisation(DropDownList ddw, long idorg);
    }
}
