<%@ Page Title="Question - Forum LinCom" Language="C#" MasterPageFile="~/MasterPage.Master" AutoEventWireup="true" CodeBehind="question-detail.aspx.cs" Inherits="LinCom.question_detail" ValidateRequest="true" ViewStateEncryptionMode="Always" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="Content/forum-lincom.css" rel="stylesheet" />
    <link href="Content/prism.css" rel="stylesheet" />
    <style>
        .question-header {
            border-bottom: 1px solid #e1e4e8;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }
        
        .question-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #24292e;
            margin-bottom: 0.5rem;
        }
        
        .question-meta {
            color: #586069;
            font-size: 0.9rem;
        }
        
        .post-container {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid var(--lincom-border);
            border-radius: 8px;
            background: var(--lincom-white);
            border-left: 4px solid var(--lincom-secondary);
            transition: all 0.3s ease;
        }

        .post-container:hover {
            box-shadow: var(--lincom-shadow-hover);
            border-left-color: var(--lincom-accent);
        }
        
        .vote-controls {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 60px;
        }
        
        .vote-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #586069;
            cursor: pointer;
            padding: 0.25rem;
            transition: color 0.3s ease;
        }
        
        .vote-btn:hover {
            color: var(--lincom-secondary);
        }

        .vote-btn.voted {
            color: var(--lincom-success);
        }
        
        .vote-score {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0.5rem 0;
            color: #24292e;
        }
        
        .favorite-btn {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #586069;
            cursor: pointer;
            margin-top: 0.5rem;
        }
        
        .favorite-btn.favorited {
            color: #ffd700;
        }
        
        .post-content {
            flex: 1;
        }
        
        .post-body {
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .post-body h1, .post-body h2, .post-body h3 {
            margin-top: 1.5rem;
            margin-bottom: 0.5rem;
        }
        
        .post-body pre {
            background: #f6f8fa;
            border-radius: 6px;
            padding: 1rem;
            overflow-x: auto;
        }
        
        .post-body blockquote {
            border-left: 4px solid #dfe2e5;
            padding-left: 1rem;
            margin: 1rem 0;
            color: #586069;
        }
        
        .post-tags {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .tag {
            background: #e1ecf4;
            color: #39739d;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.85rem;
            text-decoration: none;
        }
        
        .post-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .action-btn {
            background: none;
            border: none;
            color: #586069;
            font-size: 0.9rem;
            cursor: pointer;
            padding: 0.25rem 0.5rem;
        }
        
        .action-btn:hover {
            color: #0366d6;
        }
        
        .post-signature {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-top: 1rem;
        }
        
        .user-card {
            background: #f6f8fa;
            border-radius: 6px;
            padding: 0.75rem;
            max-width: 200px;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #0366d6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .user-name {
            font-weight: 600;
            color: #0366d6;
            text-decoration: none;
        }
        
        .user-reputation {
            color: #586069;
            font-size: 0.9rem;
        }
        
        .comments-section {
            border-top: 1px solid #e1e4e8;
            padding-top: 1rem;
            margin-top: 1rem;
        }
        
        .comment-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f1f3f4;
            font-size: 0.9rem;
        }
        
        .comment-content {
            margin-bottom: 0.25rem;
            line-height: 1.4;
        }
        
        .comment-meta {
            color: #586069;
            font-size: 0.8rem;
        }
        
        .add-comment {
            margin-top: 1rem;
        }
        
        .comment-input {
            width: 100%;
            min-height: 60px;
            padding: 0.5rem;
            border: 1px solid #d1d5da;
            border-radius: 4px;
            resize: vertical;
        }
        
        .answers-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 2rem 0 1rem 0;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e1e4e8;
        }
        
        .answer-container {
            position: relative;
        }
        
        .accepted-answer {
            border-left: 4px solid #28a745;
        }
        
        .accepted-badge {
            position: absolute;
            top: -10px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .answer-form {
            margin-top: 2rem;
            padding: 1.5rem;
            border: 1px solid #e1e4e8;
            border-radius: 8px;
            background: #f6f8fa;
        }
        
        .editor-toolbar {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: white;
            border: 1px solid #d1d5da;
            border-bottom: none;
            border-radius: 6px 6px 0 0;
        }
        
        .editor-btn {
            background: none;
            border: none;
            padding: 0.25rem 0.5rem;
            cursor: pointer;
            border-radius: 3px;
        }
        
        .editor-btn:hover {
            background: #f1f3f4;
        }
        
        .answer-editor {
            width: 100%;
            min-height: 200px;
            padding: 1rem;
            border: 1px solid #d1d5da;
            border-radius: 0 0 6px 6px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            resize: vertical;
        }
        
        .submit-answer-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 1rem;
        }
        
        .submit-answer-btn:hover {
            background: #218838;
        }
        
        .related-questions {
            background: #f6f8fa;
            border-radius: 6px;
            padding: 1rem;
            margin-top: 2rem;
        }
        
        .related-question {
            display: block;
            color: #0366d6;
            text-decoration: none;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e1e4e8;
        }
        
        .related-question:hover {
            text-decoration: underline;
        }
        
        .breadcrumb {
            background: none;
            padding: 0;
            margin-bottom: 1rem;
        }
        
        .breadcrumb-item a {
            color: #0366d6;
            text-decoration: none;
        }
        
        .breadcrumb-item a:hover {
            text-decoration: underline;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="forum-questions.aspx">Forum</a></li>
                <li class="breadcrumb-item"><a href="forum-questions.aspx">Questions</a></li>
                <li class="breadcrumb-item active" aria-current="page">Question</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Contenu Principal -->
            <div class="col-md-9">
                <!-- En-tête de la Question -->
                <div class="question-header">
                    <h1 class="question-title">
                        <asp:Label ID="lblQuestionTitle" runat="server"></asp:Label>
                    </h1>
                    <div class="question-meta">
                        Posée <asp:Label ID="lblQuestionDate" runat="server"></asp:Label> •
                        Modifiée <asp:Label ID="lblLastActivity" runat="server"></asp:Label> •
                        Vue <asp:Label ID="lblViewCount" runat="server"></asp:Label> fois
                    </div>
                </div>

                <!-- Question -->
                <div class="post-container">
                    <div class="vote-controls">
                        <asp:Button ID="btnUpvoteQuestion" runat="server" CssClass="vote-btn" Text="▲" OnClick="btnUpvoteQuestion_Click" />
                        <div class="vote-score">
                            <asp:Label ID="lblQuestionScore" runat="server"></asp:Label>
                        </div>
                        <asp:Button ID="btnDownvoteQuestion" runat="server" CssClass="vote-btn" Text="▼" OnClick="btnDownvoteQuestion_Click" />
                        <asp:Button ID="btnFavoriteQuestion" runat="server" CssClass="favorite-btn" Text="★" OnClick="btnFavoriteQuestion_Click" />
                        <small><asp:Label ID="lblFavoriteCount" runat="server"></asp:Label></small>
                    </div>

                    <div class="post-content">
                        <div class="post-body">
                            <asp:Literal ID="litQuestionBody" runat="server"></asp:Literal>
                        </div>

                        <div class="post-tags">
                            <asp:Literal ID="litQuestionTags" runat="server"></asp:Literal>
                        </div>

                        <div class="post-actions">
                            <button type="button" class="action-btn">
                                <i class="fas fa-share"></i> Partager
                            </button>
                            <button type="button" class="action-btn">
                                <i class="fas fa-edit"></i> Modifier
                            </button>
                            <button type="button" class="action-btn">
                                <i class="fas fa-flag"></i> Signaler
                            </button>
                        </div>

                        <div class="post-signature">
                            <div class="post-time">
                                <small class="text-muted">
                                    posée <asp:Label ID="lblQuestionTimeAgo" runat="server"></asp:Label>
                                </small>
                            </div>
                            <div class="user-card">
                                <div class="user-avatar">
                                    <asp:Label ID="lblQuestionAuthorInitials" runat="server"></asp:Label>
                                </div>
                                <div>
                                    <a href="#" class="user-name">
                                        <asp:Label ID="lblQuestionAuthor" runat="server"></asp:Label>
                                    </a>
                                    <div class="user-reputation">
                                        <i class="fas fa-trophy"></i> <asp:Label ID="lblQuestionAuthorReputation" runat="server"></asp:Label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Commentaires de la Question -->
                        <div class="comments-section">
                            <asp:ListView ID="lvQuestionComments" runat="server">
                                <LayoutTemplate>
                                    <div id="itemPlaceholder" runat="server"></div>
                                </LayoutTemplate>
                                <ItemTemplate>
                                    <div class="comment-item">
                                        <div class="comment-content">
                                            <%# Eval("Text") %>
                                        </div>
                                        <div class="comment-meta">
                                            <a href="#" class="text-muted"><%# Eval("UserName") %></a>
                                            <span class="text-muted"><%# GetTimeAgo(Convert.ToDateTime(Eval("CreationDate"))) %></span>
                                            <span class="comment-score">
                                                <i class="fas fa-arrow-up"></i> <%# Eval("Score") %>
                                            </span>
                                        </div>
                                    </div>
                                </ItemTemplate>
                            </asp:ListView>

                            <div class="add-comment">
                                <asp:TextBox ID="txtQuestionComment" runat="server" CssClass="comment-input" 
                                    placeholder="Ajouter un commentaire..." TextMode="MultiLine" Rows="2"></asp:TextBox>
                                <asp:Button ID="btnAddQuestionComment" runat="server" Text="Ajouter un commentaire" 
                                    CssClass="btn btn-sm btn-outline-primary mt-2" OnClick="btnAddQuestionComment_Click" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- En-tête des Réponses -->
                <div class="answers-header">
                    <h3>
                        <asp:Label ID="lblAnswerCount" runat="server"></asp:Label> Réponses
                    </h3>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-toggle="dropdown">
                            Trier par
                        </button>
                        <div class="dropdown-menu">
                            <asp:LinkButton ID="lnkSortVotes" runat="server" CssClass="dropdown-item" OnClick="SortAnswers_Click" CommandArgument="votes">Plus de votes</asp:LinkButton>
                            <asp:LinkButton ID="lnkSortNewest" runat="server" CssClass="dropdown-item" OnClick="SortAnswers_Click" CommandArgument="newest">Plus récentes</asp:LinkButton>
                            <asp:LinkButton ID="lnkSortOldest" runat="server" CssClass="dropdown-item" OnClick="SortAnswers_Click" CommandArgument="oldest">Plus anciennes</asp:LinkButton>
                        </div>
                    </div>
                </div>

                <!-- Liste des Réponses -->
                <asp:ListView ID="lvAnswers" runat="server" OnItemCommand="lvAnswers_ItemCommand">
                    <LayoutTemplate>
                        <div id="itemPlaceholder" runat="server"></div>
                    </LayoutTemplate>
                    <ItemTemplate>
                        <div class="post-container answer-container <%# Convert.ToBoolean(Eval("IsAccepted")) ? "accepted-answer" : "" %>">
                            <%# Convert.ToBoolean(Eval("IsAccepted")) ? "<div class='accepted-badge'><i class='fas fa-check'></i> Acceptée</div>" : "" %>
                            
                            <div class="vote-controls">
                                <asp:Button ID="btnUpvoteAnswer" runat="server" CssClass="vote-btn" Text="▲" 
                                    CommandName="UpvoteAnswer" CommandArgument='<%# Eval("SujetForumId") %>' />
                                <div class="vote-score"><%# Eval("Score") %></div>
                                <asp:Button ID="btnDownvoteAnswer" runat="server" CssClass="vote-btn" Text="▼" 
                                    CommandName="DownvoteAnswer" CommandArgument='<%# Eval("SujetForumId") %>' />
                                
                                <asp:Button ID="btnAcceptAnswer" runat="server" CssClass="vote-btn" Text="✓" 
                                    CommandName="AcceptAnswer" CommandArgument='<%# Eval("SujetForumId") %>'
                                    Visible='<%# CanAcceptAnswer() && !Convert.ToBoolean(Eval("IsAccepted")) %>' />
                            </div>

                            <div class="post-content">
                                <div class="post-body">
                                    <%# Eval("Body") %>
                                </div>

                                <div class="post-actions">
                                    <button type="button" class="action-btn">
                                        <i class="fas fa-share"></i> Partager
                                    </button>
                                    <button type="button" class="action-btn">
                                        <i class="fas fa-edit"></i> Modifier
                                    </button>
                                    <button type="button" class="action-btn">
                                        <i class="fas fa-flag"></i> Signaler
                                    </button>
                                </div>

                                <div class="post-signature">
                                    <div class="post-time">
                                        <small class="text-muted">
                                            répondu <%# GetTimeAgo(Convert.ToDateTime(Eval("CreationDate"))) %>
                                        </small>
                                    </div>
                                    <div class="user-card">
                                        <div class="user-avatar">
                                            <%# GetUserInitials(Eval("OwnerName").ToString()) %>
                                        </div>
                                        <div>
                                            <a href="#" class="user-name"><%# Eval("OwnerName") %></a>
                                            <div class="user-reputation">
                                                <i class="fas fa-trophy"></i> <%# Eval("OwnerReputation") %>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Commentaires de la Réponse -->
                                <div class="comments-section">
                                    <!-- Les commentaires seront chargés dynamiquement -->
                                </div>
                            </div>
                        </div>
                    </ItemTemplate>
                    <EmptyDataTemplate>
                        <div class="text-center py-4">
                            <i class="fas fa-comment-slash fa-2x text-muted mb-3"></i>
                            <p class="text-muted">Aucune réponse pour le moment. Soyez le premier à répondre !</p>
                        </div>
                    </EmptyDataTemplate>
                </asp:ListView>

                <!-- Formulaire de Réponse -->
                <div class="answer-form" id="answerForm" runat="server">
                    <h4>Votre Réponse</h4>
                    <p class="text-muted">Merci de contribuer à la communauté LinCom ! Assurez-vous que votre réponse apporte une valeur ajoutée.</p>
                    
                    <div class="editor-toolbar">
                        <button type="button" class="editor-btn" title="Gras"><i class="fas fa-bold"></i></button>
                        <button type="button" class="editor-btn" title="Italique"><i class="fas fa-italic"></i></button>
                        <button type="button" class="editor-btn" title="Code"><i class="fas fa-code"></i></button>
                        <button type="button" class="editor-btn" title="Lien"><i class="fas fa-link"></i></button>
                        <button type="button" class="editor-btn" title="Image"><i class="fas fa-image"></i></button>
                        <button type="button" class="editor-btn" title="Liste"><i class="fas fa-list"></i></button>
                    </div>
                    
                    <asp:TextBox ID="txtAnswerContent" runat="server" CssClass="answer-editor" 
                        placeholder="Écrivez votre réponse ici..." TextMode="MultiLine"></asp:TextBox>
                    
                    <asp:Button ID="btnSubmitAnswer" runat="server" Text="Publier votre réponse" 
                        CssClass="submit-answer-btn" OnClick="btnSubmitAnswer_Click" />
                </div>
            </div>

            <!-- Sidebar Droite -->
            <div class="col-md-3">
                <!-- Statistiques de la Question -->
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-chart-bar"></i> Statistiques</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Posée</span>
                            <strong><asp:Label ID="lblStatsAsked" runat="server"></asp:Label></strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Vues</span>
                            <strong><asp:Label ID="lblStatsViews" runat="server"></asp:Label></strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Dernière activité</span>
                            <strong><asp:Label ID="lblStatsActivity" runat="server"></asp:Label></strong>
                        </div>
                    </div>
                </div>

                <!-- Questions Similaires -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-link"></i> Questions Similaires</h6>
                    </div>
                    <div class="card-body">
                        <asp:ListView ID="lvSimilarQuestions" runat="server">
                            <LayoutTemplate>
                                <div id="itemPlaceholder" runat="server"></div>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <a href="question-detail.aspx?id=<%# Eval("SujetForumId") %>" class="related-question">
                                    <div class="d-flex justify-content-between">
                                        <span><%# TruncateText(Eval("Title").ToString(), 60) %></span>
                                        <small class="text-muted"><%# Eval("Score") %></small>
                                    </div>
                                </a>
                            </ItemTemplate>
                        </asp:ListView>
                    </div>
                </div>

                <!-- Tags de la Question -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-tags"></i> Tags</h6>
                    </div>
                    <div class="card-body">
                        <asp:Literal ID="litSidebarTags" runat="server"></asp:Literal>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="Content/prism.js"></script>
    <script>
        // Coloration syntaxique du code
        Prism.highlightAll();

        // Gestion de l'éditeur de réponse
        document.querySelectorAll('.editor-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const editor = document.getElementById('<%=txtAnswerContent.ClientID%>');
                const command = this.title.toLowerCase();
                
                // Logique d'édition basique (à améliorer avec un vrai éditeur)
                switch(command) {
                    case 'gras':
                        insertText(editor, '**', '**');
                        break;
                    case 'italique':
                        insertText(editor, '*', '*');
                        break;
                    case 'code':
                        insertText(editor, '`', '`');
                        break;
                }
            });
        });

        function insertText(textarea, before, after) {
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const selectedText = textarea.value.substring(start, end);
            const newText = before + selectedText + after;
            
            textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + before.length, start + before.length + selectedText.length);
        }

        // Auto-save du brouillon
        let autoSaveTimer;
        const answerTextarea = document.getElementById('<%=txtAnswerContent.ClientID%>');
        if (answerTextarea) {
            answerTextarea.addEventListener('input', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(() => {
                    localStorage.setItem('answer_draft_' + window.location.search, this.value);
                }, 1000);
            });

            // Restaurer le brouillon
            window.addEventListener('load', function() {
                const draft = localStorage.getItem('answer_draft_' + window.location.search);
                if (draft && answerTextarea.value === '') {
                    answerTextarea.value = draft;
                }
            });
        }
    </script>
</asp:Content>
