using LinCom.Class;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class listsessionmentorat : System.Web.UI.Page
    {
        private int info;
        SessionMentorat_Class sessionObj = new SessionMentorat_Class();
        ISessionMentorat objSession = new SessionMentoratImp();
        ICommonCode co = new CommonCode();

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();

        static long ide, idorg;
        static int rolid;
        static string nameorg;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {
                    ide = Convert.ToInt64(idperso.Value);
                    rolid = Convert.ToInt32(role.Value);
                }
                else
                {
                    Response.Redirect("~/login.aspx");
                }

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                getDataGDV();
            }
        }

        public void getDataGDV()
        {
            try
            {
                // Charger toutes les sessions de mentorat
                objSession.ChargerSessions(gdv);
            }
            catch (Exception ex)
            {
                lblMessage.Text = "Erreur lors du chargement des sessions : " + ex.Message;
                lblMessage.CssClass = "alert alert-danger";
                lblMessage.Visible = true;
            }
        }

        protected void gdv_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            
            if (e.CommandName == "view")
            {
                Response.Redirect("~/file/sessionmentorat.aspx?id=" + index);
            }
            else if (e.CommandName == "delete")
            {
                try
                {
                    int sessionId = Convert.ToInt32(index);
                    objSession.AfficherDetails(sessionId, sessionObj);
                    info = objSession.Supprimer(sessionId);

                    if (info == 1)
                    {
                        lblMessage.Text = "Session supprimée avec succès";
                        lblMessage.CssClass = "alert alert-success";
                        lblMessage.Visible = true;
                        getDataGDV(); // Recharger la liste
                    }
                    else
                    {
                        lblMessage.Text = "Erreur lors de la suppression de la session";
                        lblMessage.CssClass = "alert alert-danger";
                        lblMessage.Visible = true;
                    }
                }
                catch (SqlException ex)
                {
                    lblMessage.Text = "Erreur SQL lors de la suppression : " + ex.Message;
                    lblMessage.CssClass = "alert alert-danger";
                    lblMessage.Visible = true;
                    System.Diagnostics.Debug.WriteLine("Erreur SQL dans gdv_RowCommand: " + ex.ToString());
                }
                catch (Exception ex)
                {
                    lblMessage.Text = "Erreur lors de la suppression : " + ex.Message;
                    lblMessage.CssClass = "alert alert-danger";
                    lblMessage.Visible = true;
                    System.Diagnostics.Debug.WriteLine("Erreur dans gdv_RowCommand: " + ex.ToString());
                }
            }
        }
    }
}
