﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="detailsevent.aspx.cs" Inherits="LinCom.detailsevent" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <!-- Facebook / LinkedIn (Open Graph) -->
    <meta property="og:title" content="Titre de l’article ici" />
    <meta property="og:description" content="Résumé de l’article" />
    <meta property="og:image" content="https://tonsite.com/file/post/image.jpg" />
    <meta property="og:url" content="https://tonsite.com/blog-detail.aspx?postid=123" />
    <meta property="og:type" content="article" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Titre de l’article ici" />
    <meta name="twitter:description" content="Résumé de l’article" />
    <meta name="twitter:image" content="https://tonsite.com/file/post/image.jpg" />


</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <main class="main">

        <!-- Page Title -->
        <div class="page-title" runat="server" id="sectblogdetail" style="background-image: url('assets/img/blog/skills.png');">
            <div class="heading">
                <div class="container">
                    <div class="row d-flex justify-content-center text-center">
                        <div class="col-lg-8">
                            <h1 runat="server" id="txttitle">Blog Details</h1>
                            <p class="mb-0">Odio et unde deleniti. Deserunt numquam exercitationem. Officiis quo odio sint voluptas consequatur ut a odio voluptatem. Sit dolorum debitis veritatis natus dolores. Quasi ratione sint. Sit quaerat ipsum dolorem.</p>
                        </div>
                    </div>
                </div>
            </div>
            <nav class="breadcrumbs">
                <div class="container">
                    <ol>
                        <li><a href="home.aspx">Home</a></li>
                        <li><a href="evenement.aspx">Evénement</a></li>
                        <li class="current" runat="server" id="txttitle3">Detail de l'article</li>
                    </ol>
                </div>
            </nav>
        </div>
        <!-- End Page Title -->

        <div class="container">
            <div class="row">

                <div class="col-lg-8">

                    <!-- Blog Details Section -->
                    <section id="blog-details" class="blog-details section">
                        <div class="container">

                            <article class="article">

                                <div class="post-img">
                                    <img src="assets/img/blog/blog-1.jpg" runat="server" id="imgblog" alt="" class="img-fluid">
                                </div>
                                <div class="content">
                                    <blockquote>

                                        <p runat="server" id="txttitle2" class="title">Dolorum optio tempore voluptas dignissimos cumque fuga qui quibusdam quia</p>

                                    </blockquote>

                                    <div class="meta-top">
                                        <ul>
                                            <li class="d-flex align-items-center"><i class="bi bi-person"></i><span runat="server" id="txtauthororganisation">John Doe</span></li>
                                            <li class="d-flex align-items-center"><i class="bi bi-clock"></i>le <span><time runat="server" id="txtdate" datetime="2020-01-01">Jan 1, 2022</time></span></li>
                                            
                                            <li class="d-flex align-items-center"><i class="bi bi-chat-dots"></i><span runat="server" id="txtcomment">12</span></li>
                                            <li class="d-flex align-items-center"><i class="bi bi-eye"></i><span runat="server" id="txtview">12</span><br /></li>
                                            
                                           

                                        </ul>
                                    </div>
                                    <!-- End meta top -->
                                   <!-- Section Informations de l'Événement -->
<section class="event-info section bg-light py-4 px-3 rounded-4 mt-4">
  <h5 class="fw-bold mb-3"><i class="bi bi-info-circle-fill me-2"></i>Informations de l'Événement</h5>
  <ul class="list-unstyled mb-3">
    <li class="mb-2">
      <i class="bi bi-geo-alt-fill text-primary me-2"></i>
      <strong>Lieu de l'événement :</strong>
      <span runat="server" id="txtlieu" class="ms-1">Lieu non précisé</span>
    </li>
    <li class="mb-2">
      <i class="bi bi-clock-history text-success me-2"></i>
      <strong>Heure & Durée :</strong>
      <span runat="server" id="txttemps" class="ms-1">Heure / Durée non précisées</span>
    </li>
    <li class="mb-2">
      <i class="bi bi-translate text-warning me-2"></i>
      <strong>Langue de l'événement :</strong>
      <span runat="server" id="txtlangueevent" class="ms-1">Langue non précisée</span>
    </li>
    <li class="mb-2">
      <i class="bi bi-link-45deg text-danger me-2"></i>
      <strong>Type d'événement :</strong>
      <span runat="server" id="txtexternevent" class="ms-1">Type non précisé</span>
    </li>
    <li class="mb-2">
      <i class="bi bi-people-fill text-info me-2"></i>
      <strong>Participants attendus :</strong>
      <span runat="server" id="txtparticipant" class="ms-1">Non précisés</span>
    </li>
  </ul>

  <!-- Boutons d'action -->
  <div class="d-flex flex-wrap gap-2">
    <a runat="server" id="btninscription" href="#" target="_blank" class="btn btn-outline-primary">
      <i class="bi bi-pencil-square me-1"></i>S'inscrire
    </a>
    <a runat="server" id="btnpdf" href="#" target="_blank" class="btn btn-outline-secondary">
      <i class="bi bi-file-earmark-pdf-fill me-1"></i>Télécharger le programme
    </a>
  </div>
</section>

                                    <p runat="server" id="txtdescription">
                                        Similique neque nam consequuntur ad non maxime aliquam quas. Quibusdam animi praesentium. Aliquam et laboriosam eius aut nostrum quidem aliquid dicta.
                    Et eveniet enim. Qui velit est ea dolorem doloremque deleniti aperiam unde soluta. Est cum et quod quos aut ut et sit sunt. Voluptate porro consequatur assumenda perferendis dolore.
                                    </p>

                                    <div class="social-share mt-4">
                                        <span class="fw-bold me-2">Partager cet article :</span>

                                        <a href='<%= "https://www.facebook.com/sharer/sharer.php?u=" + Server.UrlEncode(Request.Url.AbsoluteUri) %>' target="_blank" title="Partager sur Facebook"><i class="bi bi-facebook"></i></a>

                                        <a href='<%= "https://twitter.com/intent/tweet?url=" + Server.UrlEncode(Request.Url.AbsoluteUri) + "&text=" + Server.UrlEncode(txttitle.InnerText) %>' target="_blank" title="Partager sur Twitter"><i class="bi bi-twitter-x"></i></a>

                                        <a href='<%= "https://www.linkedin.com/sharing/share-offsite/?url=" + Server.UrlEncode(Request.Url.AbsoluteUri) %>' target="_blank" title="Partager sur LinkedIn"><i class="bi bi-linkedin"></i></a>

                                        <a href='<%= "https://api.whatsapp.com/send?text=" + Server.UrlEncode(txttitle.InnerText + " " + Request.Url.AbsoluteUri) %>' target="_blank" title="Partager sur WhatsApp"><i class="bi bi-whatsapp"></i></a>

                                        <a href='<%= "mailto:?subject=" + Server.UrlEncode("Découvrez cet article : " + txttitle.InnerText) + "&body=" + Server.UrlEncode(Request.Url.AbsoluteUri) %>' target="_blank" title="Partager par e-mail"><i class="bi bi-envelope-fill"></i></a>
                                    </div>


                                </div>
                                <!-- End post content -->

                                <div class="meta-bottom">

                                    <i class="bi bi-tags"></i>
                                    <ul class="tags">
                                        <asp:ListView ID="listdompost" runat="server">
                                            <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                            <ItemTemplate>
                                                <li><a href="#"><%# Eval("PostTitre") %> </a></li>
                                            </ItemTemplate>
                                        </asp:ListView>

                                    </ul>
                                </div>
                                <!-- End meta bottom -->

                            </article>

                        </div>
                    </section>
                    <!-- /Blog Details Section -->

                    <!-- Blog Author Organisation Section -->
                    <section id="blog-author" class="blog-author section">

                        <div class="container">
                            <div class="author-container d-flex align-items-center">
                                <img runat="server" id="imgblog1" src="assets/img/blog/skills.png" class="rounded-circle flex-shrink-0" alt="">
                                <div>
                                  
                                    <h4>Résumé</h4>
                                    <div class="social-links">
                                    </div>
                                    <p runat="server" id="txtresume">
                                        Itaque quidem optio quia voluptatibus dolorem dolor. Modi eum sed possimus accusantium. Quas repellat voluptatem officia numquam sint aspernatur voluptas. Esse et accusantium ut unde voluptas.
                                    </p>
                                   

                                </div>
                            </div>
                        </div>

                    </section>
                    <!-- /Blog Author Section -->

                    <!-- Blog Comments Section -->
                    <section id="blog-comments" class="blog-comments section">

                        <div class="container">

                            <h4 class="comments-count"><span runat="server" id="txtcommentpost">8</span></h4>
                            <asp:ListView ID="listcomment" runat="server">
                                <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                <ItemTemplate>
                                    <div id="comment-1" class="comment">
                                        <div class="d-flex">
                                            <div class="comment-img">
                                                <img src='<%# HttpUtility.HtmlEncode( string.Concat("../file/post/",Eval("photo"))) %>' alt="">
                                            </div>
                                            <div>
                                                <h5><a href=""><%#  HttpUtility.HtmlEncode(Eval("commentateur")) %></a> <a href="#" class="reply"><i class="bi bi-reply-fill"></i>Reply</a></h5>
                                                <time datetime="2020-01-01"><%#  HttpUtility.HtmlEncode(Eval("dateComm")) %></time>
                                                <p>
                                                    <%# Eval("contenu") %>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- End comment #1 -->
                                </ItemTemplate>
                            </asp:ListView>

                        </div>

                    </section>
                    <!-- /Blog Comments Section -->

                    <!-- Contact Section -->
                    <section id="contact" class="contact section">

                        <!-- Section Title -->
                        <div class="container section-title" data-aos="fade-up">
                            <h2>Poster votre Commentaire</h2>

                        </div>
                        <!-- End Section Title -->

                        <div class="container" data-aos="fade-up" data-aos-delay="100">

                            <div class="row gx-lg-0 gy-4">


                                <div class="col-lg-12">
                                    <div class="php-email-form" data-aos="fade" data-aos-delay="100">
                                        <div class="row gy-4">


                                            <div class="col-md-12">
                                                <textarea class="form-control" runat="server" id="txtmessage" name="message" rows="8" placeholder="Message" required=""></textarea>
                                            </div>

                                            <div class="col-md-12 text-center">
                                                <div class="loading">Loading</div>
                                                <div class="error-message"></div>
                                                <div class="sent-message">Your message has been sent. Thank you!</div>

                                                <button type="submit" runat="server" id="btnenreg" onserverclick="btnenreg_ServerClick">Poster votre commentaire</button>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <!-- End Contact Form -->

                            </div>

                        </div>

                    </section>
                    <!-- /Contact Section -->



                </div>

                <div class="col-lg-4 sidebar">

                    <div class="widgets-container">

                        <!-- Publicité - Carrousel -->
                        <div class="bg-white shadow-sm p-4 rounded-4">
                            <h5 class="fw-bold mb-3 text-center">Publicité</h5>
                            <div id="carouselPub" class="carousel slide" data-bs-ride="carousel">
                                <div class="carousel-inner rounded">
                                    <div class="carousel-item active">
                                        <img src="assets/img/blog/skills.png" class="d-block w-100" alt="Ad 1">
                                    </div>
                                    <div class="carousel-item">
                                        <img src="assets/img/blog/skills.png" class="d-block w-100" alt="Ad 2">
                                    </div>
                                    <div class="carousel-item">
                                        <img src="assets/img/blog/skills.png" class="d-block w-100" alt="Ad 3">
                                    </div>
                                </div>
                                <button class="carousel-control-prev" type="button" data-bs-target="#carouselPub" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon bg-dark rounded-circle" aria-hidden="true"></span>
                                    <span class="visually-hidden">Précédent</span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#carouselPub" data-bs-slide="next">
                                    <span class="carousel-control-next-icon bg-dark rounded-circle" aria-hidden="true"></span>
                                    <span class="visually-hidden">Suivant</span>
                                </button>
                            </div>
                        </div>


                        <!-- Categories Widget -->
                        <div class="categories-widget widget-item">

                            <h3 class="widget-title">Domaines d'Intervention</h3>
                            <ul class="mt-3">
                                <asp:ListView ID="listcategorie" runat="server">
                                    <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                    <ItemTemplate>
                                        <li><a href="#"><%#  HttpUtility.HtmlEncode(Eval("Libelle")) %> <span>(<b><%#  HttpUtility.HtmlEncode(Eval("NombrePosts")) %> </b>)</span></a></li>

                                    </ItemTemplate>
                                </asp:ListView>

                            </ul>

                        </div>
                        <!--/Categories Widget -->

                        <!-- Recent Posts Widget -->
                        <div class="recent-posts-widget widget-item">

                            <h3 class="widget-title">Recent Posts</h3>
                            <asp:ListView ID="listpost" runat="server" OnItemCommand="listpost_ItemCommand">
                                <EmptyItemTemplate>Aucune Donnée</EmptyItemTemplate>
                                <ItemTemplate>
                                    <div class="post-item">
                                        <img src='<%# HttpUtility.HtmlEncode( string.Concat("../file/post/",Eval("photo"))) %>' alt="" class="flex-shrink-0">

                                        <div>
                                            <h4>
                                                <asp:LinkButton runat="server" CommandName="view" CommandArgument='<%# Eval("name") %>'><%# HttpUtility.HtmlEncode( Eval("Titre")) %><i class="fa fa-arrow-right"></i></asp:LinkButton>


                                            </h4>
                                            <time datetime="2020-01-01"><%# HttpUtility.HtmlEncode( Eval("DatePublication")) %></time>
                                        </div>
                                    </div>
                                    <!-- End recent post item-->
                                </ItemTemplate>
                            </asp:ListView>

                        </div>
                        <!--/Recent Posts Widget -->

                    </div>

                </div>

            </div>
        </div>

    </main>






















</asp:Content>
