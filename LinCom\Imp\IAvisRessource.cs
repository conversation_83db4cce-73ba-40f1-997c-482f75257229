﻿using LinCom.Class;
using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IAvisRessource
    {
        int Ajout(AvisRessource_Class add);
        void Chargement_GDV_Membre(GridView GV_apv, int code);
        void Chargement_GDV(GridView gv);
        void search(GridView GV_apv, string code);
        void afficherDetails(int code, AvisRessource_Class pr);
        int edit(AvisRessource_Class cl);
        int supprimer(int id);
        void chargerAvis_Membre(DropDownList lst, int code);
        void chargerAvis(DropDownList ddw);
        int count();
    }
}
