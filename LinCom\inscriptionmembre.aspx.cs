﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.file;
using LinCom.Imp;
using LinCom.Model;
using Microsoft.Ajax.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Web;
using System.Web.Script.Serialization;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class inscriptionmembre : System.Web.UI.Page
    {
        private int info;
        string nscno; static string pht;
        TypeOrganisation_Class cat = new TypeOrganisation_Class();
        ITypeOrganisation objcat = new TypeOrganisationImp();
        Organisation_Class co = new Organisation_Class();
        Organisation_Class org = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        Province_Class prov = new Province_Class();
        IProvince objp = new ProvinceImp();
        CommuneClass znc = new CommuneClass();
        ICommune objco = new CommuneImp();
        ICommonCode com = new CommonCode();
        DomaineInterventionOrganisation_Class actco = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objactco = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class domai = new DomaineIntervention_Class();
        IDomaineIntervention objdom = new DomaineInterventionImp();
        Membre_Class mem = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        IMembre obj = new MembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        RoleMembre_Class rl=new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        DataTable dat = new DataTable();
        long index;

        protected void Page_Load(object sender, EventArgs e)
        {
            nscno = Request.QueryString["IDCOOP"];
            if (!IsPostBack)
            {
                afficher();
                objp.chargerProvince(drpdprov);
                objorg.chargerOrganisation(drpdorg);
            }
        }
        void vider()
        {
            txtphone.Value = "";
            txtemail.Value = "";
            txtnm.Value = "";
            txtprenom.Value = "";
            txtConfirmEmail.Value = "";
            txtphone.Value = "";
            txtdatecreation.Value = "";
            drpdcom.SelectedValue = "-1";
            txt_adress.Value = "";
            drpdprov.SelectedValue = "-1";

        }

     
        private void ShowAlert(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "alert", $"alert('{message}');", true);
        }

        private bool IsCaptchaValid()
        {
            string captchaResponse = Request.Form["g-recaptcha-response"];
            string secretKey = "6LcJKC0rAAAAAOkxp8DV-yFfpwrG_1tRUpfwq-gJ";

            using (WebClient client = new WebClient())
            {
                string apiUrl = $"https://www.google.com/recaptcha/api/siteverify?secret={secretKey}&response={captchaResponse}";
                string jsonResult = client.DownloadString(apiUrl);
                JavaScriptSerializer js = new JavaScriptSerializer();
                dynamic result = js.Deserialize<dynamic>(jsonResult);
                return result["success"] == true;
            }
        }

       
        private void SendConfirmationEmail(string toEmail, string name)
        {
          
            SmtpClient smtpClient = new SmtpClient("send.one.com", 587)
            {
                UseDefaultCredentials = true,
                Credentials = new System.Net.NetworkCredential("<EMAIL>", "Newpass1980"),
                DeliveryMethod = SmtpDeliveryMethod.Network,
                EnableSsl = true
            };

            MailMessage mail = new MailMessage
            {
                IsBodyHtml = true,
                From = new MailAddress("<EMAIL>"),
                Subject = "Inscription dans Linked Community Burundi - LinCom Burundi",
                Body = $"Bonjour cher  <strong>{name}</strong>,<br/>" +
                       "Bienvenue dans la plateforme Linked Community Burundi - LinCom Burundi... nous sommes heureux de vous avoir comme membre. <br/><br/><br/> " +
                       " Si vous avez des questions, vous pouvez nous <NAME_EMAIL> <br/><br/><br/><br/>" +
                       " Avec LinCom, vous avez accès à des fonctionnalités qui vous aiderons à mieux organiser dans l'interne vos activités et cooperateurs.<br/><br/><br/>" +
                       " Vous gerez votre espace de travail numérique et profitez la collaboration avec d'autres acteurs et les opportunités des projets." +
                       
                       "<br/><br/><br/>" +
                       "Merci Beaucoup"

            };

            mail.To.Add(new MailAddress(toEmail));
            smtpClient.Send(mail);
        }

        void inscript()
        {
            //if(radbtnassoc.Checked==true)
            //    inscrptassoc.Visible= true;
            //else inscrptpart.Visible= false;
        }
        protected void radbtnassoc_CheckedChanged(object sender, EventArgs e)
        {
            //if (radbtnassoc.Checked == true)
            //{
            //    inscrptassoc.Visible = true;
            //    inscrptpart.Visible= false;
            //    radbtnparte.Checked = false;
            //}
        }
        protected void radbtnparte_CheckedChanged(object sender, EventArgs e)
        {
            //if (radbtnparte.Checked == true)
            //{
            //    inscrptpart.Visible = true;
            //    inscrptassoc.Visible= false;
            //    radbtnassoc.Checked = false;
            //}
        }
        protected void btn_enreg_Click(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx");
        }

        protected void afficher()
        {
            //if (nscno != null)
            //{
            //    obj.afficherDetails(Convert.ToInt32(nscno), co);
            //    //txt_nm.Value = prov.NMCAT;
            //    //txt_descript.Value = prov.DESCRPITCAT;

            //}

        }
        protected void drpd_prov_SelectedIndexChanged(object sender, EventArgs e)
        {
            objco.chargerCommune(drpdcom, Convert.ToInt32(drpdprov.SelectedValue));
        }

        protected void aci_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#hero");
        }

        protected void prop_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#about");
        }

        protected void serv_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#services");
        }

        protected void inst_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#team");
        }

        protected void cont_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/home.aspx" + "#contact");
        }


        public static string GenererMotDePasseParDefaut(int longueur = 10)
        {
            const string caracteres = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%&*!";

            var motDePasse = new StringBuilder();
            var random = new Random();

            for (int i = 0; i < longueur; i++)
            {
                var index = random.Next(caracteres.Length);
                motDePasse.Append(caracteres[index]);
            }

            return motDePasse.ToString();
        }


        void NouveauMembre()
        {
            try
            {
                if (txtnm.Value == "" || txtprenom.Value == "" || txtemail.Value == "" || txtConfirmEmail.Value == "" || txtdatecreation.Value == "" || drpdprov.SelectedValue == "-1" || drpdcom.SelectedValue == "-1")
                {
                    ShowAlert("Remplissez tous les champs");
                    return;

                }
                // Vérification de l'égalité des emails
                if (txtemail.Value.Trim() != txtConfirmEmail.Value.Trim())
                {
                    ShowAlert("L’adresse e-mail et sa confirmation doivent être identiques.");
                    return;
                }

                // Vérification de l'égalité des emails
                if (txtpswd.Value.Trim() != txtconfirmpswd.Value.Trim())
                {
                    ShowAlert("Le mot de passe et sa confirmation doivent être identiques.");
                    return;
                }

                obj.AfficherDetails(txtConfirmEmail.Value, memb,1);
                if (memb.Email == txtConfirmEmail.Value)
                {
                    ShowAlert("Votre email existe déjà. Veuillez consulter votre email pour avoir plus d'informations.");
                    return;
                }
              
                // Vérification des conditions
                if (!chkConditions.Checked)
                {
                    ShowAlert("Vous devez accepter les politiques et conditions pour continuer.");
                    return;
                }

                // Vérification du CAPTCHA
                if (!IsCaptchaValid())
                {
                    ShowAlert("Erreur CAPTCHA. Veuillez cocher la case Je ne suis pas un robot.");
                    return;
                }
              
                mem.Nom = txtnm.Value;
                mem.Prenom = txtprenom.Value;
                mem.Email = txtConfirmEmail.Value;
                mem.Telephone = txtphone.Value;
                mem.Sexe = drpdsexe.SelectedValue;
                mem.DateNaissance = Convert.ToDateTime(txtdatecreation.Value); ;
                mem.ProvinceId = Convert.ToInt32(drpdprov.SelectedValue);
                mem.CommuneId = Convert.ToInt32(drpdcom.SelectedValue);
                mem.name = txtConfirmEmail.Value;
                mem.province = drpdprov.SelectedItem.ToString();
                mem.commune = drpdprov.SelectedItem.ToString();
                mem.Adresse = txt_adress.Value;
                mem.username = txtConfirmEmail.Value;
                mem.motpasse = BCrypt.Net.BCrypt.HashPassword(txtconfirmpswd.Value);
                mem.statut = "actif";
                mem.IsActive = 1;
                mem.IsVerified = 1;
                mem.LastLogin = DateTime.Now;
                mem.ResetToken = null;
                mem.ResetTokenExpiry = null;
                objrl.AfficherDetails("membre",rl);
                mem.RoleMembreID = rl.RoleMembreID;

                mem.PhotoProfil = "emptyuser.png";
                mem.facebook = "";
                mem.siteweb = "";
                mem.twitter = "";
                mem.instagramme = "";
                mem.linkedin = "";
                mem.youtube = "";
                mem.Biographie = "";
                mem.DateInscription = DateTime.Now;

                info =obj.Ajouter(mem);
                if (info==1)
                {
                    SendConfirmationEmail(txtConfirmEmail.Value, txtnm.Value+" "+txtprenom.Value);

                    if (drpdexiste.SelectedValue=="oui" && drpdorg.SelectedValue!="-1") NouveauMembreOrganisation();
                    
                    ShowAlert("Merci, votre inscription a été envoyée avec succès. Consultez votre e-mail de bienvenue.");
                    vider();
                }
                else
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert(' Verifiez bien vos Informations')</script>");

                }
                Response.Redirect("~/login.aspx");

            }
            catch (Exception ex)
            {
                // logguer l'exception pour le debug
                System.Diagnostics.Debug.WriteLine("Erreur : " + ex.Message);
            }
        }

        void NouveauMembreOrganisation()
        {
            try
            {
               
                obj.AfficherDetails(txtConfirmEmail.Value, mem,1);
                memorg.MembreId = mem.MembreId;
                memorg.OrganisationId =Convert.ToInt64( drpdorg.SelectedValue);
                memorg.Poste = "Membre";
                memorg.DateAdhesion = DateTime.Now;
                memorg.Name = txtConfirmEmail.Value;
                memorg.Statut = "en attente";

                objrl.AfficherDetails("membre", rl);
                memorg.RoleMembreID = rl.RoleMembreID;
                
                objmemorg.Ajouter(memorg);

            }
            catch (Exception ex)
            {
                // logguer ou afficher les erreurs pour le débogage
                System.Diagnostics.Debug.WriteLine("Erreur dans MembreOrganisation : " + ex.Message);
            }
        }

        protected void drpdexiste_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (drpdexiste.SelectedValue=="oui")
            {
                divorg.Visible = true;
            }
            else
            {
                divorg.Visible = false;
            }
        }

        protected void btnreng_ServerClick(object sender, EventArgs e)
        {
            NouveauMembre();
        }
    }
}