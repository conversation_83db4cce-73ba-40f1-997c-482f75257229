﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IStatutUtilisateur
    {
        void AfficherDetails(int statutId, StatutUtilisateur_Class statutUtilisateurClass);
        int Ajouter(StatutUtilisateur_Class statutUtilisateurClass);
        void ChargerStatuts(GridView gdv);
        void ChargerStatutsParMembre(GridView gdv, long membreId);
        int Modifier(StatutUtilisateur_Class statutUtilisateurClass);
        void chargerStatutUtilisateur(DropDownList lst);
        int Supprimer(int statutId);
    }
}
