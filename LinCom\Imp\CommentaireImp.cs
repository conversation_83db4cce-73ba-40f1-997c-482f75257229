﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
  
    public class CommentaireImp : ICommentairePoste
    {
        int msg;
        CommentPost p = new CommentPost();
        public int Ajout(CommentairePoste_Class add)
        {
            using (Connection con = new Connection())
            {
                p.PostId = add.PostId;
                p.MembreId = add.MembreId;
                p.Contenu = add.Contenu;
                p.DateCommentaire = add.DateCommentaire;
                p.EstVisible = add.EstVisible;
                p.Nbrevue = add.Nbrevue;
                p.name = add.name;

                try
                {
                    con.CommentPosts.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.CommentPosts.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void afficherDetails(int code, CommentairePoste_Class pr)
        {
            using (Connection con = new Connection())
            {
                p = con.CommentPosts.Where(x => x.CommentPostId == code).FirstOrDefault();

                if (p != null)
                {
                    pr.PostId = p.PostId;
                    pr.MembreId = p.MembreId;
                    pr.Contenu = p.Contenu;
                    pr.DateCommentaire = p.DateCommentaire;
                    pr.EstVisible = p.EstVisible;
                    pr.Nbrevue = p.Nbrevue;
                    pr.name = p.name;
                }

            }
        }

        public void afficherDetails(string code, CommentairePoste_Class pr)
        {
            throw new NotImplementedException();
        }

        public void Chargement_GDV(GridView GV_apv)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.CommentPosts
                           select new
                           {


                               idcom = ep.CommentPostId,
                               postId = ep.PostId,
                               member = ep.MembreId,
                               contenu = ep.Contenu,
                               dateComm = ep.DateCommentaire,
                               visible = ep.EstVisible,
                               nbre = ep.Nbrevue,

                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }
        public void ChargementListview(ListView GV_apv,long id,long idorg,long idmem,string statut, int cd)
        {
            using (Connection con = new Connection())
            {

                if (cd==0)
                {
                    var obj = (from ep in con.CommentPosts
                               join po in con.Posts on ep.PostId equals po.PostId
                               join me in con.Membres on ep.MembreId equals me.MembreId
                               join o in con.Organisations on po.OrganisationId equals o.OrganisationId
                               where ep.PostId==id && po.OrganisationId==idorg && ep.EstVisible==statut
                               select new
                               {


                                   id = ep.CommentPostId,
                                   postId = ep.PostId,
                                   member = ep.MembreId,
                                   contenu = ep.Contenu,
                                   dateComm = ep.DateCommentaire,
                                   visible = ep.EstVisible,
                                   nbre = ep.Nbrevue,
                                   commentateur=me.Nom+" "+me.Prenom,
                                   photocommentateur=me.PhotoProfil,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();

                }
                else if (cd==1)
                {
                    var obj = (from ep in con.CommentPosts
                               join po in con.Posts on ep.PostId equals po.PostId
                               join me in con.Membres on ep.MembreId equals me.MembreId
                               join o in con.Organisations on po.OrganisationId equals o.OrganisationId
                               where ep.PostId == id && ep.EstVisible == statut
                               select new
                               {


                                   id = ep.CommentPostId,
                                   postId = ep.PostId,
                                   member = ep.MembreId,
                                   contenu = ep.Contenu,
                                   dateComm = ep.DateCommentaire,
                                   visible = ep.EstVisible,
                                   nbre = ep.Nbrevue,
                                   commentateur = me.Nom + " " + me.Prenom,
                                   photocommentateur = me.PhotoProfil,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();


                }

            }
        }

        public void chargerCommentairePost(DropDownList lst)
        {
            throw new NotImplementedException();
        }

        public int count(long id,long idpos,int cd)
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var b = (from l in con.CommentPosts
                             select l).Count();
                    n = b;
                }
                else if (cd==1)
                {
                    var b = (from l in con.CommentPosts
                             where l.PostId==idpos
                             select l).Count();
                    n = b;
                }
              
            }
            return n;
        }

        public int edit(CommentairePoste_Class cl, int id)
        {
            using (Connection con = new Connection())
            {
                p = con.CommentPosts.Where(x => x.CommentPostId == id).FirstOrDefault();

                try
                {
                    p.PostId = cl.PostId;
                    p.MembreId = cl.MembreId;
                    p.Contenu = cl.Contenu;
                    p.DateCommentaire = cl.DateCommentaire;
                    p.EstVisible = cl.EstVisible;
                    p.Nbrevue = cl.Nbrevue;
                    p.name = cl.name;

                    if (con.SaveChanges() == 1)
                    {
                        con.CommentPosts.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }

        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {



                var obj = (from ep in con.CommentPosts
                           join a in con.MembreProfils on ep.MembreId equals a.MembreId
                           where (ep.PostId.ToString().Contains(code) && ep.MembreId.ToString().Contains(code))
                           select new
                           {
                               idcom = ep.CommentPostId,
                               postId = ep.PostId,
                               member = ep.MembreId,
                               contenu = ep.Contenu,
                               dateComm = ep.DateCommentaire,
                               visible = ep.EstVisible,
                               nbre = ep.Nbrevue,
                           }).ToList();


                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public int supprimer(int id)
        {
            using (Connection con = new Connection())
            {

                p = con.CommentPosts.Where(x => x.CommentPostId == id).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.CommentPosts.Attach(p);

                con.CommentPosts.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }
    }
}