using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Text;
using System.Text.RegularExpressions;

namespace LinCom
{
    public partial class forum_questions : System.Web.UI.Page
    {
        private SujetForumImp sujetImp = new SujetForumImp();
        private CategoriesImp categoriesImp = new CategoriesImp();
        private TagsImp tagsImp = new TagsImp();
        private PostViewsImp viewsImp = new PostViewsImp();

        public int CurrentPage { get; set; } = 1;
        private int PageSize = 15;
        private string CurrentFilter = "recent";
        private long? SelectedCategoryId = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // Récupérer les paramètres de l'URL
                if (Request.QueryString["filter"] != null)
                    CurrentFilter = Request.QueryString["filter"];
                
                if (Request.QueryString["category"] != null)
                    long.TryParse(Request.QueryString["category"], out long catId);
                
                if (Request.QueryString["page"] != null)
                    int.TryParse(Request.QueryString["page"], out CurrentPage);

                LoadData();
            }
        }

        private void LoadData()
        {
            try
            {
                // Charger les catégories
                categoriesImp.ChargerCategoriesAvecStatistiques(lvCategories);

                // Charger les tags populaires
                tagsImp.ChargerTagsPopulaires(lvTagsPopulaires, 10);

                // Charger les questions selon le filtre
                LoadQuestions();

                // Mettre à jour l'interface
                UpdateFilterButtons();
                UpdateTitle();
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des données : " + ex.Message);
            }
        }

        private void LoadQuestions()
        {
            try
            {
                // Charger les questions selon le filtre actuel
                sujetImp.ChargerQuestions(lvQuestions, CurrentFilter, SelectedCategoryId);

                // Compter le total pour la pagination
                int totalQuestions = sujetImp.count();
                lblCount.Text = totalQuestions.ToString();

                // Générer la pagination
                GeneratePagination(totalQuestions);
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des questions : " + ex.Message);
            }
        }

        protected void btnFilter_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            CurrentFilter = btn.CommandArgument;
            CurrentPage = 1;
            
            LoadQuestions();
            UpdateFilterButtons();
            UpdateTitle();
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtSearch.Text.Trim()))
            {
                // Rediriger vers la page de recherche
                Response.Redirect($"forum-search.aspx?q={HttpUtility.UrlEncode(txtSearch.Text.Trim())}");
            }
        }

        protected void lvCategories_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (e.CommandName == "SelectCategory")
            {
                long categoryId = Convert.ToInt64(e.CommandArgument);
                Response.Redirect($"forum-questions.aspx?category={categoryId}&filter={CurrentFilter}");
            }
        }

        protected void lvQuestions_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            // Gérer les actions sur les questions si nécessaire
        }

        protected void Pagination_Command(object sender, CommandEventArgs e)
        {
            if (e.CommandName == "Page")
            {
                CurrentPage = Convert.ToInt32(e.CommandArgument);
                LoadQuestions();
                GeneratePagination(sujetImp.count());
            }
        }

        private void UpdateFilterButtons()
        {
            // Réinitialiser tous les boutons
            btnRecent.CssClass = "filter-tab";
            btnPopular.CssClass = "filter-tab";
            btnUnanswered.CssClass = "filter-tab";
            btnFeatured.CssClass = "filter-tab";

            // Activer le bouton correspondant au filtre actuel
            switch (CurrentFilter)
            {
                case "recent":
                    btnRecent.CssClass = "filter-tab active";
                    break;
                case "popular":
                    btnPopular.CssClass = "filter-tab active";
                    break;
                case "unanswered":
                    btnUnanswered.CssClass = "filter-tab active";
                    break;
                case "featured":
                    btnFeatured.CssClass = "filter-tab active";
                    break;
            }
        }

        private void UpdateTitle()
        {
            string title = "Toutes les Questions";
            
            switch (CurrentFilter)
            {
                case "recent":
                    title = "Questions Récentes";
                    break;
                case "popular":
                    title = "Questions Populaires";
                    break;
                case "unanswered":
                    title = "Questions Non Répondues";
                    break;
                case "featured":
                    title = "Questions en Vedette";
                    break;
            }

            if (SelectedCategoryId.HasValue)
            {
                // Obtenir le nom de la catégorie
                var category = new Categories_Class();
                categoriesImp.AfficherDetails(SelectedCategoryId.Value, category);
                title += $" - {category.Name}";
            }

            lblTitre.Text = title;
            Page.Title = title + " - Forum LinCom";
        }

        private void GeneratePagination(int totalItems)
        {
            int totalPages = (int)Math.Ceiling((double)totalItems / PageSize);
            
            if (totalPages <= 1)
            {
                rptPagination.Visible = false;
                return;
            }

            var pages = new List<object>();
            
            // Logique de pagination (afficher 5 pages autour de la page actuelle)
            int startPage = Math.Max(1, CurrentPage - 2);
            int endPage = Math.Min(totalPages, CurrentPage + 2);

            // Première page
            if (startPage > 1)
            {
                pages.Add(new { PageNumber = 1 });
                if (startPage > 2)
                    pages.Add(new { PageNumber = "..." });
            }

            // Pages autour de la page actuelle
            for (int i = startPage; i <= endPage; i++)
            {
                pages.Add(new { PageNumber = i });
            }

            // Dernière page
            if (endPage < totalPages)
            {
                if (endPage < totalPages - 1)
                    pages.Add(new { PageNumber = "..." });
                pages.Add(new { PageNumber = totalPages });
            }

            rptPagination.DataSource = pages;
            rptPagination.DataBind();
            rptPagination.Visible = true;
        }

        // Méthodes utilitaires pour l'affichage
        protected string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            // Supprimer les balises HTML
            text = Regex.Replace(text, "<.*?>", "");
            
            if (text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength) + "...";
        }

        protected string GenerateTagsHtml(string tags)
        {
            if (string.IsNullOrEmpty(tags))
                return "";

            var tagList = tags.Split(',');
            var html = new StringBuilder();

            foreach (var tag in tagList)
            {
                var cleanTag = tag.Trim();
                if (!string.IsNullOrEmpty(cleanTag))
                {
                    html.AppendFormat("<a href='questions-tag.aspx?tag={0}' class='tag'>{1}</a>", 
                        HttpUtility.UrlEncode(cleanTag), HttpUtility.HtmlEncode(cleanTag));
                }
            }

            return html.ToString();
        }

        protected string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "à l'instant";
            if (timeSpan.TotalMinutes < 60)
                return $"il y a {(int)timeSpan.TotalMinutes} min";
            if (timeSpan.TotalHours < 24)
                return $"il y a {(int)timeSpan.TotalHours}h";
            if (timeSpan.TotalDays < 30)
                return $"il y a {(int)timeSpan.TotalDays} jours";
            if (timeSpan.TotalDays < 365)
                return $"il y a {(int)(timeSpan.TotalDays / 30)} mois";
            
            return $"il y a {(int)(timeSpan.TotalDays / 365)} ans";
        }

        protected string GetUserInitials(string userName)
        {
            if (string.IsNullOrEmpty(userName))
                return "?";

            var parts = userName.Split(' ');
            if (parts.Length >= 2)
                return (parts[0][0].ToString() + parts[1][0].ToString()).ToUpper();
            
            return userName[0].ToString().ToUpper();
        }

        private void ShowError(string message)
        {
            // Afficher un message d'erreur (vous pouvez utiliser votre système de notification existant)
            ClientScript.RegisterStartupScript(this.GetType(), "error", 
                $"alert('Erreur: {HttpUtility.JavaScriptStringEncode(message)}');", true);
        }

        private void ShowSuccess(string message)
        {
            // Afficher un message de succès
            ClientScript.RegisterStartupScript(this.GetType(), "success", 
                $"toastr.success('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }
    }
}
