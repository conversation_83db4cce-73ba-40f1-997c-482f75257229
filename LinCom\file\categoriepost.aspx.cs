﻿using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class categoriepost : System.Web.UI.Page
    {
        private int info;
        string nscno;
        CategoriePost_Class prov = new CategoriePost_Class();
        CategoriePost_Class pro = new CategoriePost_Class();
        ICategoryPost obj = new CategoriePostImp();
        ICommonCode co = new CommonCode();

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();

        static string imge, imge1, pdfe, nameorg;
        static long ide, idorg;
        static int rolid;
        long index;

        static string typenm; static int id, idpers, rol;
        protected void Page_Load(object sender, EventArgs e)
        {
            nscno = Request.QueryString["name"];
            if (!IsPostBack)
            {

               
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    ide = Convert.ToInt64(idperso.Value);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                initial_msg();
                if (nscno == null)
                {
                    btn_enreg.InnerText = "Enregistrer";
                    // Response.Redirect("~/sima/typeorganisation.aspx/");
                }
                else

                {
                    btn_enreg.InnerText = "Modifier";
                    afficher();
                }

            }
        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;

        }
        public string Generatename(string title)
        {
            return title.ToLower()
                        .Replace("-", "")
                        .Replace(",", "")
                        .Replace("'", "")
                        .Replace("?", "")
                        .Replace("!", "")
                        .Replace(".", "")
                        .Replace(":", "")
                        .Replace(" ", "");
        }
        public void adPrv()
        {
            try
            {
                if (txtnm.Value == "")
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";
                }
                else
                {
                    prov.name = co.GenerateSlug(txtnm.Value);
                    prov.Libelle = txtnm.Value;
                    info = obj.add(prov);
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listcategoriepost.aspx");

                    }
                    else
                    {
                        div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Cette catégorie de post Existe deja";

                    }


                }

            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "cette catégorie de post Existe deja";

            }

            //LabelMsg.InnerText = obj.ajouterProvince(prov).ToString();
        }

        public void upte()
        {
            try
            {
                if (txtnm.Value == "")
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";
                }
                else
                {
                    prov.name = co.GenerateSlug(txtnm.Value);
                    prov.Libelle = txtnm.Value;
                    obj.afficherDetails(nscno, pro);
                    info = obj.edit(prov,Convert.ToInt32(pro.CategoriePostId));
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listcategoriepost.aspx");
                    }
                    else
                    {
                        Response.Redirect("~/file/listcategoriepost.aspx");

                    }
                }

            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "cette catégorie de post Existe deja";

            }
        }

        protected void btn_enreg_ServerClick(object sender, EventArgs e)
        {
            if (nscno == null)
            {
                adPrv();

            }
            else
                upte();
        }


        protected void afficher()
        {
            if (nscno != null)
            {
                obj.afficherDetails(nscno, prov);
                txtnm.Value = prov.Libelle;

            }
        }


    }
}