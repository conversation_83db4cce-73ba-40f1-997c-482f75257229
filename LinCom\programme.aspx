﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="programme.aspx.cs" Inherits="LinCom.programme" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
        <!-- Page Title -->
<div class="page-title">
    <div class="heading">
        <div class="container">
            <div class="row d-flex justify-content-center text-center">
                <div class="col-lg-8">
                    <h2>Organisations des Jeunes d'impact social</h2>
                    <p class="mb-0">Odio et unde deleniti. Deserunt numquam exercitationem. Officiis quo odio sint voluptas consequatur ut a odio voluptatem. Sit dolorum debitis veritatis natus dolores. Quasi ratione sint. Sit quaerat ipsum dolorem.</p>
                </div>
            </div>
        </div>
    </div>
    <nav class="breadcrumbs">
        <div class="container">
            <ol>
                <li><a href="home.aspx">Home</a></li>
                <li><a href="#">ONG</a></li>
                <li class="current"><a href="ong.aspx">Liste des Organisations</a></li>
            </ol>
        </div>
    </nav>
</div>
<!-- End Page Title -->
 <main class="main">

  <!-- Filter Section -->
  <section class="filter-section py-4 bg-light">
    <div class="container d-flex flex-wrap justify-content-center gap-3">
      <select id="categoryFilter" class="form-select form-select-md" style="max-width: 220px;">
        <option value="">Toutes les catégories</option>
        <option value="Education">Éducation</option>
        <option value="Environment">Environnement</option>
        <option value="Health">Santé</option>
      </select>
      <input type="date" id="dateFilter" class="form-control form-control-md" style="max-width: 220px;" placeholder="Date de lancement">
      <button type="submit" class="btn btn-success btn-md px-4">Filtrer</button>
    </div>
  </section>

  <!-- Programs Grid -->
  <section id="programs" class="programs-section py-5">
    <div class="container">
      <div class="row g-4 justify-content-center">

        <!-- Program Card -->
        <div class="col-sm-10 col-md-6 col-lg-4">
          <article class="card shadow-sm h-100 border-0 rounded-4 overflow-hidden">
            <img src="assets/img/blog/skills.png" alt="Programme Education" class="card-img-top" style="height: 220px; object-fit: cover;">
            <div class="card-body d-flex flex-column">
              <span class="badge bg-success mb-2 align-self-start">Éducation</span>
              <h5 class="card-title"><a href="#" class="stretched-link text-decoration-none text-dark fw-bold">Programme d'alphabétisation numérique</a></h5>
              <div class="mt-auto d-flex align-items-center pt-3 border-top">
                <img src="assets/img/blog/skills.png" alt="Mentor Sophie Martin" class="rounded-circle me-3" width="48" height="48" style="object-fit: cover;">
                <div>
                  <p class="mb-0 fw-semibold">Sophie Martin</p>
                  <small class="text-muted">15 Janvier 2024</small>
                </div>
              </div>
            </div>
          </article>
        </div>

        <!-- Program Card -->
        <div class="col-sm-10 col-md-6 col-lg-4">
          <article class="card shadow-sm h-100 border-0 rounded-4 overflow-hidden">
            <img src="assets/img/blog/skills.png" alt="Programme Environnement" class="card-img-top" style="height: 220px; object-fit: cover;">
            <div class="card-body d-flex flex-column">
              <span class="badge bg-success mb-2 align-self-start">Environnement</span>
              <h5 class="card-title"><a href="#" class="stretched-link text-decoration-none text-dark fw-bold">Initiative zéro déchet</a></h5>
              <div class="mt-auto d-flex align-items-center pt-3 border-top">
                <img src="assets/img/blog/skills.png" alt="Mentor Alex Dupont" class="rounded-circle me-3" width="48" height="48" style="object-fit: cover;">
                <div>
                  <p class="mb-0 fw-semibold">Alex Dupont</p>
                  <small class="text-muted">10 Octobre 2023</small>
                </div>
              </div>
            </div>
          </article>
        </div>

        <!-- Program Card -->
        <div class="col-sm-10 col-md-6 col-lg-4">
          <article class="card shadow-sm h-100 border-0 rounded-4 overflow-hidden">
            <img src="assets/img/blog/skills.png" alt="Programme Santé" class="card-img-top" style="height: 220px; object-fit: cover;">
            <div class="card-body d-flex flex-column">
              <span class="badge bg-success mb-2 align-self-start">Santé</span>
              <h5 class="card-title"><a href="#" class="stretched-link text-decoration-none text-dark fw-bold">Campagne de sensibilisation santé mentale</a></h5>
              <div class="mt-auto d-flex align-items-center pt-3 border-top">
                <img src="assets/img/blog/skills.png" alt="Mentor Marie Leclerc" class="rounded-circle me-3" width="48" height="48" style="object-fit: cover;">
                <div>
                  <p class="mb-0 fw-semibold">Marie Leclerc</p>
                  <small class="text-muted">20 Mars 2024</small>
                </div>
              </div>
            </div>
          </article>
        </div>

      </div>
    </div>
  </section>

  <!-- Pagination -->
  <section class="pagination-section py-4">
    <div class="container d-flex justify-content-center">
      <nav aria-label="Page navigation">
        <ul class="pagination pagination-rounded gap-2 mb-0">
          <li class="page-item">
            <a class="page-link" href="#" aria-label="Previous"><i class="bi bi-chevron-left"></i></a>
          </li>
          <li class="page-item"><a class="page-link" href="#">1</a></li>
          <li class="page-item active"><a class="page-link" href="#">2</a></li>
          <li class="page-item"><a class="page-link" href="#">3</a></li>
          <li class="page-item"><a class="page-link" href="#">4</a></li>
          <li class="page-item disabled"><span class="page-link">...</span></li>
          <li class="page-item"><a class="page-link" href="#">10</a></li>
          <li class="page-item">
            <a class="page-link" href="#" aria-label="Next"><i class="bi bi-chevron-right"></i></a>
          </li>
        </ul>
      </nav>
    </div>
  </section>

</main>

<style>
  /* Reset some basics */
  .main a {
    transition: color 0.3s ease;
  }
  .main a:hover {
    color: #28a745 !important;
  }

  .page-title h1 {
    font-weight: 700;
    font-size: 2.8rem;
  }

  .filter-section .form-select,
  .filter-section .form-control {
    border-radius: 50px;
    box-shadow: 0 2px 6px rgb(0 0 0 / 0.1);
  }

  .filter-section .btn {
    border-radius: 50px;
    font-weight: 600;
  }

  /* Cards */
  .card {
    box-shadow: 0 6px 20px rgb(0 0 0 / 0.1);
  }
  .card:hover {
    box-shadow: 0 10px 30px rgb(0 0 0 / 0.15);
    transform: translateY(-6px);
  }

  .card .badge {
    font-size: 0.8rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }

  .card-title a {
    font-size: 1.25rem;
  }

  .pagination .page-link {
    border-radius: 50% !important;
    width: 38px;
    height: 38px;
    text-align: center;
    line-height: 38px;
  }

  .pagination .page-item.active .page-link {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
  }
</style>

</asp:Content>
