using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class ReputationHistoryImp : IReputationHistory
    {
        int msg;
        ReputationHistory p = new ReputationHistory();

        public int Add(ReputationHistory_Class reputation)
        {
            using (Connection con = new Connection())
            {
                p.UserId = reputation.UserId;
                p.PostId = reputation.PostId;
                p.ReputationChange = reputation.ReputationChange;
                p.ReputationHistoryTypeId = reputation.ReputationHistoryTypeId;
                p.CreationDate = DateTime.Now;
                p.Description = reputation.Description;

                try
                {
                    con.ReputationHistories.Add(p);
                    if (con.SaveChanges() == 1)
                    {
                        // Mettre à jour la réputation totale de l'utilisateur
                        MettreAJourReputationUtilisateur(reputation.UserId);
                        
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public void AjouterReputation(long userId, int points, int typeAction, long? postId = null, string description = null)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si l'utilisateur peut gagner de la réputation
                if (PeutGagnerReputation(userId, typeAction))
                {
                    var reputation = new ReputationHistory_Class
                    {
                        UserId = userId,
                        PostId = postId,
                        ReputationChange = points,
                        ReputationHistoryTypeId = typeAction,
                        Description = description ?? ObtenirDescriptionAction(typeAction)
                    };

                    Add(reputation);

                    // Vérifier les badges basés sur la réputation
                    VerifierBadgesReputation(userId);
                }
            }
        }

        public void SupprimerReputation(long userId, int points, int typeAction, long? postId = null, string description = null)
        {
            var reputation = new ReputationHistory_Class
            {
                UserId = userId,
                PostId = postId,
                ReputationChange = -Math.Abs(points), // S'assurer que c'est négatif
                ReputationHistoryTypeId = typeAction,
                Description = description ?? $"Perte: {ObtenirDescriptionAction(typeAction)}"
            };

            Add(reputation);
        }

        public int CalculerReputationTotale(long userId)
        {
            using (Connection con = new Connection())
            {
                int total = con.ReputationHistories.Where(r => r.UserId == userId)
                                                   .Sum(r => (int?)r.ReputationChange) ?? 0;
                
                // Assurer un minimum de 1 point de réputation
                return Math.Max(1, total);
            }
        }

        public void MettreAJourReputationUtilisateur(long userId)
        {
            using (Connection con = new Connection())
            {
                var membre = con.Membres.Where(m => m.MembreId == userId).FirstOrDefault();
                if (membre != null)
                {
                    membre.Reputation = CalculerReputationTotale(userId);
                    con.SaveChanges();
                }
            }
        }

        public void ChargerHistoriqueUtilisateur(ListView lv, long userId, int limite = 50)
        {
            using (Connection con = new Connection())
            {
                var historique = from r in con.ReputationHistories
                                join rt in con.ReputationHistoryTypes on r.ReputationHistoryTypeId equals rt.ReputationHistoryTypeId into rtJoin
                                from rt in rtJoin.DefaultIfEmpty()
                                join p in con.SujetForums on r.PostId equals p.SujetForumId into postJoin
                                from p in postJoin.DefaultIfEmpty()
                                where r.UserId == userId
                                orderby r.CreationDate descending
                                select new
                                {
                                    r.ReputationId,
                                    r.ReputationChange,
                                    r.CreationDate,
                                    r.Description,
                                    ActionType = rt != null ? rt.Name : "Unknown",
                                    PostTitle = p != null ? p.Title : "",
                                    PostType = p != null ? (p.PostTypeId == 1 ? "Question" : "Réponse") : "",
                                    IsPositive = r.ReputationChange > 0,
                                    ChangeDisplay = (r.ReputationChange > 0 ? "+" : "") + r.ReputationChange.ToString(),
                                    IconClass = rt != null ? rt.IconClass : "fa-question",
                                    Color = r.ReputationChange > 0 ? "#28a745" : "#dc3545"
                                };

                lv.DataSource = historique.Take(limite).ToList();
                lv.DataBind();
            }
        }

        public void ChargerStatistiquesUtilisateur(long userId, out int total, out int aujourdhui, out int semaine, out int mois)
        {
            using (Connection con = new Connection())
            {
                total = CalculerReputationTotale(userId);
                
                aujourdhui = con.ReputationHistories.Where(r => r.UserId == userId && r.CreationDate >= DateTime.Today)
                                                    .Sum(r => (int?)r.ReputationChange) ?? 0;
                
                semaine = con.ReputationHistories.Where(r => r.UserId == userId && r.CreationDate >= DateTime.Now.AddDays(-7))
                                                 .Sum(r => (int?)r.ReputationChange) ?? 0;
                
                mois = con.ReputationHistories.Where(r => r.UserId == userId && r.CreationDate >= DateTime.Now.AddDays(-30))
                                              .Sum(r => (int?)r.ReputationChange) ?? 0;
            }
        }

        public void ChargerTopUtilisateurs(ListView lv, int limite = 20, string periode = "all")
        {
            using (Connection con = new Connection())
            {
                DateTime dateDebut = DateTime.MinValue;
                
                switch (periode.ToLower())
                {
                    case "today":
                        dateDebut = DateTime.Today;
                        break;
                    case "week":
                        dateDebut = DateTime.Now.AddDays(-7);
                        break;
                    case "month":
                        dateDebut = DateTime.Now.AddDays(-30);
                        break;
                    case "year":
                        dateDebut = DateTime.Now.AddYears(-1);
                        break;
                }

                var topUsers = from m in con.Membres
                              let reputationPeriode = periode == "all" ? m.Reputation : 
                                  con.ReputationHistories.Where(r => r.UserId == m.MembreId && r.CreationDate >= dateDebut)
                                                         .Sum(r => (int?)r.ReputationChange) ?? 0
                              where reputationPeriode > 0
                              orderby reputationPeriode descending
                              select new
                              {
                                  m.MembreId,
                                  UserName = m.Nom + " " + m.Prenom,
                                  Reputation = reputationPeriode,
                                  TotalReputation = m.Reputation,
                                  Niveau = CalculerNiveauUtilisateur(m.Reputation),
                                  BadgeCount = con.Badges.Count(b => b.UserId == m.MembreId),
                                  QuestionCount = con.SujetForums.Count(s => s.OwnerUserId == m.MembreId && s.PostTypeId == 1),
                                  AnswerCount = con.SujetForums.Count(s => s.OwnerUserId == m.MembreId && s.PostTypeId == 2)
                              };

                lv.DataSource = topUsers.Take(limite).ToList();
                lv.DataBind();
            }
        }

        public string CalculerNiveauUtilisateur(int reputation)
        {
            if (reputation >= 25000) return "Expert";
            if (reputation >= 10000) return "Avancé";
            if (reputation >= 3000) return "Expérimenté";
            if (reputation >= 1000) return "Intermédiaire";
            if (reputation >= 200) return "Débutant+";
            return "Débutant";
        }

        public List<string> ObtenirPrivileges(int reputation)
        {
            var privileges = new List<string>();
            
            if (reputation >= 15) privileges.Add("Voter positivement");
            if (reputation >= 50) privileges.Add("Commenter partout");
            if (reputation >= 125) privileges.Add("Voter négativement");
            if (reputation >= 250) privileges.Add("Voir moins de publicités");
            if (reputation >= 500) privileges.Add("Accéder aux outils de révision");
            if (reputation >= 1000) privileges.Add("Voir les votes détaillés");
            if (reputation >= 2000) privileges.Add("Modifier les questions et réponses");
            if (reputation >= 3000) privileges.Add("Voter pour fermer/rouvrir");
            if (reputation >= 10000) privileges.Add("Accéder aux outils de modération");
            if (reputation >= 25000) privileges.Add("Protéger les questions");

            return privileges;
        }

        public int ObtenirPointsPourNiveauSuivant(int reputationActuelle)
        {
            int[] niveaux = { 200, 1000, 3000, 10000, 25000 };
            
            foreach (int niveau in niveaux)
            {
                if (reputationActuelle < niveau)
                {
                    return niveau - reputationActuelle;
                }
            }
            
            return 0; // Niveau maximum atteint
        }

        public bool PeutGagnerReputation(long userId, int typeAction)
        {
            using (Connection con = new Connection())
            {
                // Vérifier la limite journalière (200 points par jour)
                if (AAtteintLimiteJournaliere(userId))
                {
                    return false;
                }

                // Certaines actions ne donnent pas de réputation
                int[] actionsNonRemunerees = { 6, 7, 10, 11, 12 }; // Close, Reopen, Deletion, etc.
                if (actionsNonRemunerees.Contains(typeAction))
                {
                    return false;
                }

                return true;
            }
        }

        public bool AAtteintLimiteJournaliere(long userId)
        {
            using (Connection con = new Connection())
            {
                int pointsAujourdhui = con.ReputationHistories.Where(r => r.UserId == userId && 
                                                                    r.CreationDate >= DateTime.Today &&
                                                                    r.ReputationChange > 0)
                                                              .Sum(r => (int?)r.ReputationChange) ?? 0;
                
                return pointsAujourdhui >= 200; // Limite Stack Overflow
            }
        }

        public int ObtenirLimiteJournaliere(long userId)
        {
            return 200; // Limite standard Stack Overflow
        }

        public void GererVotePositif(long userId, long postId, bool estQuestion)
        {
            int points = estQuestion ? 5 : 10;
            int typeAction = estQuestion ? 1 : 2; // QuestionUpvoted : AnswerUpvoted
            
            AjouterReputation(userId, points, typeAction, postId);
        }

        public void GererVoteNegatif(long userId, long postId, bool estQuestion)
        {
            int points = -2;
            int typeAction = estQuestion ? 4 : 5; // QuestionDownvoted : AnswerDownvoted
            
            AjouterReputation(userId, points, typeAction, postId);
        }

        public void GererReponseAcceptee(long userId, long postId)
        {
            int points = 15;
            int typeAction = 3; // AnswerAccepted
            
            AjouterReputation(userId, points, typeAction, postId, "Réponse acceptée");
        }

        public void GererPremierPost(long userId, long postId)
        {
            int points = 1;
            int typeAction = 8; // FirstPost
            
            AjouterReputation(userId, points, typeAction, postId, "Premier post");
        }

        private string ObtenirDescriptionAction(int typeAction)
        {
            var descriptions = new Dictionary<int, string>
            {
                [1] = "Question votée positivement",
                [2] = "Réponse votée positivement",
                [3] = "Réponse acceptée",
                [4] = "Question votée négativement",
                [5] = "Réponse votée négativement",
                [8] = "Premier post",
                [9] = "Première réponse"
            };

            return descriptions.ContainsKey(typeAction) ? descriptions[typeAction] : "Action inconnue";
        }

        private void VerifierBadgesReputation(long userId)
        {
            using (Connection con = new Connection())
            {
                int reputation = CalculerReputationTotale(userId);
                
                // Badges basés sur la réputation
                var badgesImp = new BadgesImp();
                
                if (reputation >= 200 && !badgesImp.PossedeBadge(userId, "Established User"))
                {
                    badgesImp.AttribuerBadge(userId, "Established User");
                }
                
                if (reputation >= 1000 && !badgesImp.PossedeBadge(userId, "Notable Question"))
                {
                    badgesImp.AttribuerBadge(userId, "Notable Question");
                }
                
                if (reputation >= 10000 && !badgesImp.PossedeBadge(userId, "Legendary"))
                {
                    badgesImp.AttribuerBadge(userId, "Legendary");
                }
            }
        }

        public void Chargement_GDV(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var obj = (from r in con.ReputationHistories
                           join u in con.Membres on r.UserId equals u.MembreId
                           join rt in con.ReputationHistoryTypes on r.ReputationHistoryTypeId equals rt.ReputationHistoryTypeId into rtJoin
                           from rt in rtJoin.DefaultIfEmpty()
                           select new
                           {
                               ReputationId = r.ReputationId,
                               UserName = u.Nom + " " + u.Prenom,
                               ReputationChange = r.ReputationChange,
                               ActionType = rt != null ? rt.Name : "Unknown",
                               CreationDate = r.CreationDate,
                               Description = r.Description
                           }).OrderByDescending(r => r.CreationDate).ToList();

                gdv.DataSource = obj;
                gdv.DataBind();
            }
        }

        public void AfficherDetails(long reputationId, ReputationHistory_Class reputation) { throw new NotImplementedException(); }
        public int Edit(ReputationHistory_Class reputation, long reputationId) { throw new NotImplementedException(); }
        public int Supprimer(long reputationId) { throw new NotImplementedException(); }
        public void Search(GridView gdv, string searchTerm) { throw new NotImplementedException(); }
        public int Count() { throw new NotImplementedException(); }

        // Méthodes supplémentaires à implémenter...
        public void ChargerHistoriquePost(ListView lv, long postId) { throw new NotImplementedException(); }
        public void ChargerActiviteRecente(ListView lv, int limite = 20) { throw new NotImplementedException(); }
        public void ChargerChangementsJour(ListView lv, long userId, DateTime date) { throw new NotImplementedException(); }
        public void ChargerProgressionReputation(ListView lv, long userId, int jours = 30) { throw new NotImplementedException(); }
        public void ChargerRepartitionReputation(long userId, out Dictionary<string, int> repartition) { throw new NotImplementedException(); }
        public int CalculerPointsAction(int typeAction, bool estAuteur = false) { throw new NotImplementedException(); }
        public void AppliquerReglesCap(long userId) { throw new NotImplementedException(); }
        public void ChargerTypesActions(DropDownList ddl) { throw new NotImplementedException(); }
        public void AjouterTypeAction(ReputationHistoryTypes_Class type) { throw new NotImplementedException(); }
        public void ModifierTypeAction(ReputationHistoryTypes_Class type) { throw new NotImplementedException(); }
        public ReputationHistoryTypes_Class ObtenirTypeAction(int typeId) { throw new NotImplementedException(); }
        public void ChargerTousTypesActions(ListView lv) { throw new NotImplementedException(); }
        public void ChargerNiveauxDisponibles(ListView lv) { throw new NotImplementedException(); }
        public bool APrivilege(long userId, string privilege) { throw new NotImplementedException(); }
        public void GererBountyAttribue(long userId, int montant) { throw new NotImplementedException(); }
        public void AjusterReputation(long userId, int ajustement, string raison) { throw new NotImplementedException(); }
        public void AnnulerChangementsReputation(long userId, DateTime dateDebut, DateTime dateFin) { throw new NotImplementedException(); }
        public void RecalculerReputationComplete(long userId) { throw new NotImplementedException(); }
        public void ChargerAjustementsAdmin(ListView lv, int limite = 50) { throw new NotImplementedException(); }
        public void ChargerRapportReputationPeriode(DateTime debut, DateTime fin, ListView lv) { throw new NotImplementedException(); }
        public void ChargerTendancesReputation(ListView lv, int jours = 30) { throw new NotImplementedException(); }
        public void ChargerDistributionReputation(out Dictionary<string, int> distribution) { throw new NotImplementedException(); }
        public void ExporterHistoriqueUtilisateur(long userId, DateTime? debut = null, DateTime? fin = null) { throw new NotImplementedException(); }
        public void NotifierChangementReputation(long userId, int changement, string raison) { throw new NotImplementedException(); }
        public void ChargerNotificationsReputation(ListView lv, long userId) { throw new NotImplementedException(); }
        public void MarquerNotificationLue(long notificationId) { throw new NotImplementedException(); }
        public void ConfigurerAlertes(long userId, bool alertes) { throw new NotImplementedException(); }
        public void NettoierHistoriqueAncien(DateTime dateLimit) { throw new NotImplementedException(); }
        public void CompacterHistorique(long userId) { throw new NotImplementedException(); }
        public void RecalculerToutesReputations() { throw new NotImplementedException(); }
        public void VerifierCoherenceReputation() { throw new NotImplementedException(); }
    }
}
