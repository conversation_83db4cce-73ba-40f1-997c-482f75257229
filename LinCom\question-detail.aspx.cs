using LinCom.Classe;
using LinCom.Imp;
using LinCom.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Text;
using System.Text.RegularExpressions;

namespace LinCom
{
    public partial class question_detail : System.Web.UI.Page
    {
        private SujetForumImp sujetImp = new SujetForumImp();
        private ReplyForumImp replyImp = new ReplyForumImp();
        private VotesImp votesImp = new VotesImp();
        private PostViewsImp viewsImp = new PostViewsImp();
        private TagsImp tagsImp = new TagsImp();
        private NotificationImp notificationImp = new NotificationImp();

        private long QuestionId;
        private SujetForum_Class CurrentQuestion;
        private long? CurrentUserId;

        protected void Page_Load(object sender, EventArgs e)
        {
            // Récupérer l'ID de la question
            if (!long.TryParse(Request.QueryString["id"], out QuestionId))
            {
                Response.Redirect("forum-questions.aspx");
                return;
            }

            // Récupérer l'utilisateur connecté
            if (Session["MembreId"] != null)
            {
                CurrentUserId = Convert.ToInt64(Session["MembreId"]);
            }

            if (!IsPostBack)
            {
                LoadQuestionData();
                LoadAnswers();
                LoadSimilarQuestions();
                
                // Ajouter une vue à la question
                AddView();
            }
        }

        private void LoadQuestionData()
        {
            try
            {
                CurrentQuestion = new SujetForum_Class();
                sujetImp.afficherDetails((int)QuestionId, CurrentQuestion);

                if (CurrentQuestion.SujetForumId == 0)
                {
                    Response.Redirect("forum-questions.aspx");
                    return;
                }

                // Remplir les contrôles avec les données de la question
                lblQuestionTitle.Text = CurrentQuestion.Title;
                Page.Title = CurrentQuestion.Title + " - Forum LinCom";
                
                litQuestionBody.Text = FormatPostContent(CurrentQuestion.Body);
                litQuestionTags.Text = GenerateTagsHtml(CurrentQuestion.Tags);
                litSidebarTags.Text = GenerateTagsHtml(CurrentQuestion.Tags);

                lblQuestionScore.Text = CurrentQuestion.Score.ToString();
                lblQuestionAuthor.Text = CurrentQuestion.OwnerDisplayName;
                lblQuestionAuthorInitials.Text = GetUserInitials(CurrentQuestion.OwnerDisplayName);
                lblQuestionAuthorReputation.Text = CurrentQuestion.OwnerReputation.ToString();

                lblQuestionDate.Text = GetTimeAgo(CurrentQuestion.CreationDate);
                lblQuestionTimeAgo.Text = GetTimeAgo(CurrentQuestion.CreationDate);
                lblLastActivity.Text = GetTimeAgo(CurrentQuestion.LastActivityDate);
                lblViewCount.Text = CurrentQuestion.ViewCount.ToString();

                // Statistiques sidebar
                lblStatsAsked.Text = CurrentQuestion.CreationDate.ToString("dd MMM yyyy");
                lblStatsViews.Text = CurrentQuestion.ViewCount.ToString();
                lblStatsActivity.Text = GetTimeAgo(CurrentQuestion.LastActivityDate);

                // Compter les favoris
                int favoriteCount = votesImp.CompterFavoris(QuestionId);
                lblFavoriteCount.Text = favoriteCount.ToString();

                // Vérifier si l'utilisateur a mis en favori
                if (CurrentUserId.HasValue)
                {
                    bool isFavorited = votesImp.EstDansFavoris(QuestionId, CurrentUserId.Value);
                    btnFavoriteQuestion.CssClass = isFavorited ? "favorite-btn favorited" : "favorite-btn";
                }

                // Charger les commentaires de la question
                replyImp.ChargerCommentaires(lvQuestionComments, QuestionId);

                // Vérifier les permissions
                CheckUserPermissions();
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement de la question : " + ex.Message);
            }
        }

        private void LoadAnswers(string sortBy = "votes")
        {
            try
            {
                replyImp.ChargerReponses(lvAnswers, QuestionId, sortBy);
                
                int answerCount = replyImp.CompterReponses(QuestionId);
                lblAnswerCount.Text = answerCount.ToString();
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des réponses : " + ex.Message);
            }
        }

        private void LoadSimilarQuestions()
        {
            try
            {
                sujetImp.ChargerQuestionsSimilaires(lvSimilarQuestions, QuestionId, 5);
            }
            catch (Exception ex)
            {
                // Erreur non critique, on continue
            }
        }

        private void AddView()
        {
            try
            {
                string ipAddress = GetClientIPAddress();
                string userAgent = Request.UserAgent;
                string referrer = Request.UrlReferrer?.ToString();

                viewsImp.AjouterVue(QuestionId, CurrentUserId, ipAddress, userAgent, referrer);
            }
            catch (Exception ex)
            {
                // Erreur non critique pour les vues
            }
        }

        protected void btnUpvoteQuestion_Click(object sender, EventArgs e)
        {
            if (!CurrentUserId.HasValue)
            {
                ShowError("Vous devez être connecté pour voter.");
                return;
            }

            // SÉCURITÉ : Vérifications de sécurité pour le vote
            if (!ForumSecurityHelper.CanUserPerformAction(CurrentUserId.Value, "vote"))
            {
                ShowError("Vous n'avez pas suffisamment de réputation pour voter (minimum 15 points).");
                return;
            }

            if (ForumSecurityHelper.IsRateLimited(CurrentUserId.Value, "vote"))
            {
                ShowError("Vous votez trop rapidement. Veuillez patienter.");
                ForumSecurityHelper.LogSecurityIncident("Vote rate limit exceeded", CurrentUserId.Value, GetClientIPAddress());
                return;
            }

            if (!ForumSecurityHelper.CanUserVote(CurrentUserId.Value, QuestionId))
            {
                ShowError("Vous avez déjà voté pour cette question.");
                return;
            }

            // Vérifier que l'utilisateur ne vote pas pour sa propre question
            if (CurrentQuestion != null && CurrentQuestion.OwnerUserId == CurrentUserId.Value)
            {
                ShowError("Vous ne pouvez pas voter pour votre propre question.");
                return;
            }

            try
            {
                int result = votesImp.VoterPost(QuestionId, CurrentUserId.Value, 2); // UpVote
                if (result == 1)
                {
                    LoadQuestionData(); // Recharger pour mettre à jour le score
                    ShowSuccess("Vote enregistré !");
                }
                else
                {
                    ShowError("Impossible d'enregistrer le vote.");
                }
            }
            catch (Exception ex)
            {
                ForumSecurityHelper.LogSecurityIncident($"Vote error: {ex.Message}", CurrentUserId.Value, GetClientIPAddress());
                ShowError("Erreur lors du vote.");
            }
        }

        protected void btnDownvoteQuestion_Click(object sender, EventArgs e)
        {
            if (!CurrentUserId.HasValue)
            {
                ShowError("Vous devez être connecté pour voter.");
                return;
            }

            try
            {
                int result = votesImp.VoterPost(QuestionId, CurrentUserId.Value, 3); // DownVote
                if (result == 1)
                {
                    LoadQuestionData(); // Recharger pour mettre à jour le score
                    ShowSuccess("Vote enregistré !");
                }
                else
                {
                    ShowError("Impossible d'enregistrer le vote.");
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du vote : " + ex.Message);
            }
        }

        protected void btnFavoriteQuestion_Click(object sender, EventArgs e)
        {
            if (!CurrentUserId.HasValue)
            {
                ShowError("Vous devez être connecté pour ajouter aux favoris.");
                return;
            }

            try
            {
                bool isFavorited = votesImp.EstDansFavoris(QuestionId, CurrentUserId.Value);
                
                int result;
                if (isFavorited)
                {
                    result = votesImp.SupprimerDesFavoris(QuestionId, CurrentUserId.Value);
                    ShowSuccess("Retiré des favoris !");
                }
                else
                {
                    result = votesImp.AjouterAuxFavoris(QuestionId, CurrentUserId.Value);
                    ShowSuccess("Ajouté aux favoris !");
                }

                if (result == 1)
                {
                    LoadQuestionData(); // Recharger pour mettre à jour l'état
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la gestion des favoris : " + ex.Message);
            }
        }

        protected void btnAddQuestionComment_Click(object sender, EventArgs e)
        {
            if (!CurrentUserId.HasValue)
            {
                ShowError("Vous devez être connecté pour commenter.");
                return;
            }

            // SÉCURITÉ : Vérifications pour les commentaires
            if (!ForumSecurityHelper.CanUserPerformAction(CurrentUserId.Value, "comment"))
            {
                ShowError("Vous n'avez pas suffisamment de réputation pour commenter (minimum 50 points).");
                return;
            }

            if (ForumSecurityHelper.IsRateLimited(CurrentUserId.Value, "comment"))
            {
                ShowError("Vous commentez trop rapidement. Veuillez patienter.");
                ForumSecurityHelper.LogSecurityIncident("Comment rate limit exceeded", CurrentUserId.Value, GetClientIPAddress());
                return;
            }

            // SÉCURITÉ : Validation du contenu du commentaire
            var commentValidation = ForumSecurityHelper.ValidatePostContent(txtQuestionComment.Text);
            if (!commentValidation.IsValid)
            {
                ShowError(commentValidation.ErrorMessage);
                return;
            }

            try
            {
                int result = replyImp.AjouterCommentaire(QuestionId, commentValidation.CleanedValue, CurrentUserId.Value);
                if (result > 0)
                {
                    // Notifier l'auteur de la question
                    if (CurrentQuestion.OwnerUserId != CurrentUserId.Value)
                    {
                        string auteurCommentaire = Session["NomComplet"]?.ToString() ?? "Un utilisateur";
                        notificationImp.NotifierCommentaire(QuestionId, CurrentQuestion.OwnerUserId.Value, auteurCommentaire, "question");
                    }

                    txtQuestionComment.Text = "";
                    replyImp.ChargerCommentaires(lvQuestionComments, QuestionId); // Recharger les commentaires
                    ShowSuccess("Commentaire ajouté !");
                }
                else
                {
                    ShowError("Impossible d'ajouter le commentaire.");
                }
            }
            catch (Exception ex)
            {
                ForumSecurityHelper.LogSecurityIncident($"Comment error: {ex.Message}", CurrentUserId.Value, GetClientIPAddress());
                ShowError("Erreur lors de l'ajout du commentaire.");
            }
        }

        protected void btnSubmitAnswer_Click(object sender, EventArgs e)
        {
            if (!CurrentUserId.HasValue)
            {
                ShowError("Vous devez être connecté pour répondre.");
                return;
            }

            if (string.IsNullOrWhiteSpace(txtAnswerContent.Text))
            {
                ShowError("La réponse ne peut pas être vide.");
                return;
            }

            try
            {
                var reponse = new ReplyForum_Class
                {
                    SujetForumId = QuestionId,
                    MembreId = CurrentUserId.Value,
                    Contenu = txtAnswerContent.Text.Trim(),
                    name = Session["NomComplet"]?.ToString() ?? "Utilisateur"
                };

                int result = replyImp.CreerReponse(reponse);
                if (result > 0)
                {
                    // Notifier l'auteur de la question
                    if (CurrentQuestion.OwnerUserId != CurrentUserId.Value)
                    {
                        string auteurReponse = Session["NomComplet"]?.ToString() ?? "Un utilisateur";
                        notificationImp.NotifierNouvelleReponse(QuestionId, CurrentQuestion.OwnerUserId.Value,
                            CurrentQuestion.Title, auteurReponse);
                    }

                    txtAnswerContent.Text = "";
                    LoadAnswers(); // Recharger les réponses
                    LoadQuestionData(); // Recharger pour mettre à jour le compteur
                    ShowSuccess("Réponse publiée avec succès !");

                    // Supprimer le brouillon
                    ClientScript.RegisterStartupScript(this.GetType(), "clearDraft",
                        "localStorage.removeItem('answer_draft_' + window.location.search);", true);
                }
                else
                {
                    ShowError("Impossible de publier la réponse.");
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la publication de la réponse : " + ex.Message);
            }
        }

        protected void lvAnswers_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (!CurrentUserId.HasValue)
            {
                ShowError("Vous devez être connecté pour effectuer cette action.");
                return;
            }

            long answerId = Convert.ToInt64(e.CommandArgument);

            try
            {
                switch (e.CommandName)
                {
                    case "UpvoteAnswer":
                        int upResult = replyImp.VoterReponse(answerId, CurrentUserId.Value, true);
                        if (upResult == 1)
                        {
                            LoadAnswers();
                            ShowSuccess("Vote enregistré !");
                        }
                        break;

                    case "DownvoteAnswer":
                        int downResult = replyImp.VoterReponse(answerId, CurrentUserId.Value, false);
                        if (downResult == 1)
                        {
                            LoadAnswers();
                            ShowSuccess("Vote enregistré !");
                        }
                        break;

                    case "AcceptAnswer":
                        if (CanAcceptAnswer())
                        {
                            // Récupérer l'auteur de la réponse pour notification
                            using (var con = new LinCom.Model.Connection())
                            {
                                var reponse = con.SujetForums.Where(s => s.SujetForumId == answerId).FirstOrDefault();
                                if (reponse != null && reponse.OwnerUserId.HasValue)
                                {
                                    notificationImp.NotifierReponseAcceptee(answerId, reponse.OwnerUserId.Value, CurrentQuestion.Title);
                                }
                            }

                            replyImp.MarquerCommeMeilleureReponse(answerId, QuestionId, CurrentUserId.Value);
                            LoadAnswers();
                            LoadQuestionData();
                            ShowSuccess("Réponse acceptée !");
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de l'action : " + ex.Message);
            }
        }

        protected void SortAnswers_Click(object sender, EventArgs e)
        {
            LinkButton btn = (LinkButton)sender;
            LoadAnswers(btn.CommandArgument);
        }

        private void CheckUserPermissions()
        {
            // Vérifier si l'utilisateur peut accepter des réponses (auteur de la question)
            bool canAccept = CanAcceptAnswer();
            
            // Masquer le formulaire de réponse si l'utilisateur n'est pas connecté
            if (!CurrentUserId.HasValue)
            {
                answerForm.Visible = false;
            }
        }

        protected bool CanAcceptAnswer()
        {
            return CurrentUserId.HasValue && CurrentQuestion != null && 
                   CurrentQuestion.OwnerUserId == CurrentUserId.Value;
        }

        // Méthodes utilitaires (similaires à forum-questions.aspx.cs)
        protected string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            text = Regex.Replace(text, "<.*?>", "");
            
            if (text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength) + "...";
        }

        protected string GenerateTagsHtml(string tags)
        {
            if (string.IsNullOrEmpty(tags))
                return "";

            var tagList = tags.Split(',');
            var html = new StringBuilder();

            foreach (var tag in tagList)
            {
                var cleanTag = tag.Trim();
                if (!string.IsNullOrEmpty(cleanTag))
                {
                    html.AppendFormat("<a href='questions-tag.aspx?tag={0}' class='tag'>{1}</a> ", 
                        HttpUtility.UrlEncode(cleanTag), HttpUtility.HtmlEncode(cleanTag));
                }
            }

            return html.ToString();
        }

        protected string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "à l'instant";
            if (timeSpan.TotalMinutes < 60)
                return $"il y a {(int)timeSpan.TotalMinutes} min";
            if (timeSpan.TotalHours < 24)
                return $"il y a {(int)timeSpan.TotalHours}h";
            if (timeSpan.TotalDays < 30)
                return $"il y a {(int)timeSpan.TotalDays} jours";
            
            return dateTime.ToString("dd MMM yyyy");
        }

        protected string GetUserInitials(string userName)
        {
            if (string.IsNullOrEmpty(userName))
                return "?";

            var parts = userName.Split(' ');
            if (parts.Length >= 2)
                return (parts[0][0].ToString() + parts[1][0].ToString()).ToUpper();
            
            return userName[0].ToString().ToUpper();
        }

        private string FormatPostContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return "";

            // Conversion basique Markdown vers HTML (à améliorer)
            content = Regex.Replace(content, @"\*\*(.*?)\*\*", "<strong>$1</strong>");
            content = Regex.Replace(content, @"\*(.*?)\*", "<em>$1</em>");
            content = Regex.Replace(content, @"`(.*?)`", "<code>$1</code>");
            content = content.Replace("\n", "<br>");

            return content;
        }

        private string GetClientIPAddress()
        {
            string ipAddress = Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            
            if (string.IsNullOrEmpty(ipAddress) || ipAddress.ToLower() == "unknown")
                ipAddress = Request.ServerVariables["REMOTE_ADDR"];
            
            return ipAddress;
        }

        private void ShowError(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "error", 
                $"toastr.error('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }

        private void ShowSuccess(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "success",
                $"toastr.success('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }

        private void ShowWarning(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "warning",
                $"toastr.warning('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }
    }
}
