using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ICategories
    {
        // Méthodes CRUD de base
        int Add(Categories_Class category);
        void AfficherDetails(long categoryId, Categories_Class category);
        int Edit(Categories_Class category, long categoryId);
        int Supprimer(long categoryId);
        void Chargement_GDV(GridView gdv);
        void Search(GridView gdv, string searchTerm);
        int Count();

        // Méthodes Stack Overflow style
        void ChargerCategoriesActives(ListView lv);
        void ChargerCategoriesAvecStatistiques(ListView lv);
        void ChargerCategoriesParent(DropDownList ddl);
        void ChargerSousCategoriesParent(ListView lv, long parentId);
        void ChargerCategoriesPopulaires(ListView lv, int limite = 10);
        void ChargerCategoriesTendance(ListView lv, int limite = 5);

        // Gestion hiérarchique
        void ChargerArbreCategories(TreeView tv);
        void ChargerCategoriesHierarchiques(ListView lv, long? parentId = null);
        List<Categories_Class> ObtenirCheminCategorie(long categoryId);
        bool ADesSousCategories(long categoryId);
        int CompterSousCategories(long categoryId);

        // Statistiques et analytics
        void ChargerStatistiquesCategorie(long categoryId, out int questions, out int reponses, out int membres);
        void ChargerActiviteRecente(ListView lv, long categoryId, int limite = 10);
        void ChargerMembresActifs(ListView lv, long categoryId, int limite = 5);
        void ChargerQuestionsPopulaires(ListView lv, long categoryId, int limite = 10);

        // Gestion des tags par catégorie
        void ChargerTagsCategorie(ListView lv, long categoryId);
        void AssocierTagCategorie(long categoryId, long tagId);
        void DissocierTagCategorie(long categoryId, long tagId);

        // Recherche et filtrage
        void RechercherCategories(ListView lv, string terme);
        void FiltrerParActivite(ListView lv, DateTime dateDebut, DateTime dateFin);
        void ChargerCategoriesParNiveau(ListView lv, int niveau);

        // Administration
        void ReorganiserOrdre(long categoryId, int nouvelOrdre);
        void ChangerStatut(long categoryId, bool actif);
        void FusionnerCategories(long categorieSource, long categorieDestination);
        void DeplacerSousCategorie(long categorieId, long? nouveauParentId);
    }
}
