<%@ Page Title="Configuration Sécurité - LinCom" Language="C#" MasterPageFile="~/MasterPage.Master" AutoEventWireup="true" CodeBehind="security-config.aspx.cs" Inherits="LinCom.security_config" ValidateRequest="true" ViewStateEncryptionMode="Always" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="Content/forum-lincom.css" rel="stylesheet" />
    <style>
        .security-header {
            background: linear-gradient(135deg, #c0392b 0%, #8e44ad 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-bottom: 4px solid #e74c3c;
        }
        
        .security-section {
            background: var(--lincom-white);
            border: 1px solid var(--lincom-border);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid #e74c3c;
        }
        
        .security-level {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .security-level.high {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .security-level.medium {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .security-level.low {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .security-metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--lincom-light);
        }
        
        .security-metric:last-child {
            border-bottom: none;
        }
        
        .metric-name {
            font-weight: 500;
            color: var(--lincom-primary);
        }
        
        .metric-value {
            font-weight: 600;
            color: var(--lincom-accent);
        }
        
        .security-alert {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }
        
        .security-alert.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .security-alert.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .security-toggle {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 1px solid var(--lincom-border);
            border-radius: 6px;
            margin-bottom: 1rem;
        }
        
        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: #ccc;
            border-radius: 15px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .toggle-switch.active {
            background: var(--lincom-success);
        }
        
        .toggle-slider {
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(30px);
        }
        
        .log-entry {
            background: var(--lincom-light);
            border: 1px solid var(--lincom-border);
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-entry.critical {
            border-left: 4px solid #dc3545;
        }
        
        .log-entry.warning {
            border-left: 4px solid #ffc107;
        }
        
        .log-entry.info {
            border-left: 4px solid #17a2b8;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Header -->
    <div class="security-header">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h1><i class="fas fa-shield-alt"></i> Configuration de Sécurité</h1>
                    <p class="lead">Gérez les paramètres de sécurité du forum LinCom</p>
                </div>
                <div class="col-md-4 text-right">
                    <span class="security-level high">
                        <i class="fas fa-check-circle"></i> Sécurité Élevée
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Alertes de Sécurité -->
        <div class="security-alert success">
            <i class="fas fa-check-circle"></i>
            <strong>Sécurité Active :</strong> Toutes les protections de sécurité sont activées et fonctionnelles.
        </div>

        <div class="row">
            <!-- Métriques de Sécurité -->
            <div class="col-md-6">
                <div class="security-section">
                    <h3><i class="fas fa-chart-bar"></i> Métriques de Sécurité</h3>
                    
                    <div class="security-metric">
                        <span class="metric-name">Tentatives d'injection SQL bloquées</span>
                        <span class="metric-value">
                            <asp:Label ID="lblSqlInjectionBlocked" runat="server" Text="0"></asp:Label>
                        </span>
                    </div>
                    
                    <div class="security-metric">
                        <span class="metric-name">Attaques XSS détectées</span>
                        <span class="metric-value">
                            <asp:Label ID="lblXssAttacksBlocked" runat="server" Text="0"></asp:Label>
                        </span>
                    </div>
                    
                    <div class="security-metric">
                        <span class="metric-name">Tokens CSRF invalides</span>
                        <span class="metric-value">
                            <asp:Label ID="lblCsrfTokensInvalid" runat="server" Text="0"></asp:Label>
                        </span>
                    </div>
                    
                    <div class="security-metric">
                        <span class="metric-name">Rate limiting activé</span>
                        <span class="metric-value">
                            <asp:Label ID="lblRateLimitingActive" runat="server" Text="0"></asp:Label> fois
                        </span>
                    </div>
                    
                    <div class="security-metric">
                        <span class="metric-name">Dernière mise à jour sécurité</span>
                        <span class="metric-value">
                            <asp:Label ID="lblLastSecurityUpdate" runat="server" Text="Aujourd'hui"></asp:Label>
                        </span>
                    </div>
                </div>

                <!-- Paramètres de Sécurité -->
                <div class="security-section">
                    <h3><i class="fas fa-cog"></i> Paramètres de Sécurité</h3>
                    
                    <div class="security-toggle">
                        <div class="toggle-switch active" data-setting="csrf">
                            <div class="toggle-slider"></div>
                        </div>
                        <div>
                            <strong>Protection CSRF</strong><br>
                            <small class="text-muted">Protège contre les attaques Cross-Site Request Forgery</small>
                        </div>
                    </div>
                    
                    <div class="security-toggle">
                        <div class="toggle-switch active" data-setting="xss">
                            <div class="toggle-slider"></div>
                        </div>
                        <div>
                            <strong>Protection XSS</strong><br>
                            <small class="text-muted">Filtre et nettoie le contenu HTML dangereux</small>
                        </div>
                    </div>
                    
                    <div class="security-toggle">
                        <div class="toggle-switch active" data-setting="sql">
                            <div class="toggle-slider"></div>
                        </div>
                        <div>
                            <strong>Protection SQL Injection</strong><br>
                            <small class="text-muted">Valide et sécurise les requêtes de base de données</small>
                        </div>
                    </div>
                    
                    <div class="security-toggle">
                        <div class="toggle-switch active" data-setting="ratelimit">
                            <div class="toggle-slider"></div>
                        </div>
                        <div>
                            <strong>Rate Limiting</strong><br>
                            <small class="text-muted">Limite le nombre de requêtes par utilisateur</small>
                        </div>
                    </div>
                    
                    <div class="security-toggle">
                        <div class="toggle-switch active" data-setting="logging">
                            <div class="toggle-slider"></div>
                        </div>
                        <div>
                            <strong>Logging de Sécurité</strong><br>
                            <small class="text-muted">Enregistre toutes les tentatives d'attaque</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logs de Sécurité -->
            <div class="col-md-6">
                <div class="security-section">
                    <h3><i class="fas fa-file-alt"></i> Logs de Sécurité Récents</h3>
                    
                    <div class="log-entry critical">
                        <strong>[CRITIQUE]</strong> 2024-01-15 14:30:25<br>
                        Tentative d'injection SQL bloquée - IP: ************* - User: 1234
                    </div>
                    
                    <div class="log-entry warning">
                        <strong>[ATTENTION]</strong> 2024-01-15 14:25:10<br>
                        Rate limiting activé - IP: ********* - Trop de votes en peu de temps
                    </div>
                    
                    <div class="log-entry warning">
                        <strong>[ATTENTION]</strong> 2024-01-15 14:20:45<br>
                        Contenu XSS détecté et nettoyé - User: 5678 - Post: 9876
                    </div>
                    
                    <div class="log-entry info">
                        <strong>[INFO]</strong> 2024-01-15 14:15:30<br>
                        Token CSRF invalide - Session expirée - User: 3456
                    </div>
                    
                    <div class="log-entry info">
                        <strong>[INFO]</strong> 2024-01-15 14:10:15<br>
                        Validation de sécurité réussie - User: 7890 - Action: post_question
                    </div>
                    
                    <div class="text-center mt-3">
                        <asp:Button ID="btnViewFullLogs" runat="server" Text="Voir tous les logs" 
                            CssClass="lincom-btn-primary" OnClick="btnViewFullLogs_Click" />
                        <asp:Button ID="btnClearLogs" runat="server" Text="Vider les logs" 
                            CssClass="lincom-btn-accent ml-2" OnClick="btnClearLogs_Click" 
                            OnClientClick="return confirm('Êtes-vous sûr de vouloir vider tous les logs ?');" />
                    </div>
                </div>

                <!-- Actions de Sécurité -->
                <div class="security-section">
                    <h3><i class="fas fa-tools"></i> Actions de Sécurité</h3>
                    
                    <div class="mb-3">
                        <asp:Button ID="btnRunSecurityScan" runat="server" Text="Lancer un scan de sécurité" 
                            CssClass="lincom-btn-primary btn-block" OnClick="btnRunSecurityScan_Click" />
                    </div>
                    
                    <div class="mb-3">
                        <asp:Button ID="btnUpdateSecurityRules" runat="server" Text="Mettre à jour les règles" 
                            CssClass="lincom-btn-secondary btn-block" OnClick="btnUpdateSecurityRules_Click" />
                    </div>
                    
                    <div class="mb-3">
                        <asp:Button ID="btnExportSecurityReport" runat="server" Text="Exporter rapport de sécurité" 
                            CssClass="lincom-btn-outline btn-block" OnClick="btnExportSecurityReport_Click" />
                    </div>
                    
                    <div class="mb-3">
                        <asp:Button ID="btnTestSecurityFeatures" runat="server" Text="Tester les fonctionnalités" 
                            CssClass="lincom-btn-warning btn-block" OnClick="btnTestSecurityFeatures_Click" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommandations de Sécurité -->
        <div class="security-section">
            <h3><i class="fas fa-lightbulb"></i> Recommandations de Sécurité</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-key"></i> Mots de Passe</h5>
                            <p class="card-text">Encouragez les utilisateurs à utiliser des mots de passe forts et activez l'authentification à deux facteurs.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-shield-alt"></i> HTTPS</h5>
                            <p class="card-text">Assurez-vous que toutes les communications utilisent HTTPS pour protéger les données en transit.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-sync-alt"></i> Mises à Jour</h5>
                            <p class="card-text">Maintenez le système à jour avec les derniers correctifs de sécurité.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Gestion des toggles de sécurité
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
                const setting = this.dataset.setting;
                const isActive = this.classList.contains('active');
                
                // Appel AJAX pour sauvegarder le paramètre
                fetch('/api/security/update-setting', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        setting: setting,
                        enabled: isActive
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        toastr.success(`Paramètre ${setting} ${isActive ? 'activé' : 'désactivé'}`);
                    } else {
                        toastr.error('Erreur lors de la sauvegarde');
                        // Revenir à l'état précédent
                        this.classList.toggle('active');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    toastr.error('Erreur de communication');
                    this.classList.toggle('active');
                });
            });
        });

        // Auto-refresh des métriques toutes les 30 secondes
        setInterval(function() {
            fetch('/api/security/metrics')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('<%=lblSqlInjectionBlocked.ClientID%>').textContent = data.sqlInjectionBlocked;
                    document.getElementById('<%=lblXssAttacksBlocked.ClientID%>').textContent = data.xssAttacksBlocked;
                    document.getElementById('<%=lblCsrfTokensInvalid.ClientID%>').textContent = data.csrfTokensInvalid;
                    document.getElementById('<%=lblRateLimitingActive.ClientID%>').textContent = data.rateLimitingActive;
                })
                .catch(error => console.error('Erreur lors du refresh des métriques:', error));
        }, 30000);
    </script>
</asp:Content>
