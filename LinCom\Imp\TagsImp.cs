using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class TagsImp : ITags
    {
        int msg;
        Tags p = new Tags();

        public int Add(Tags_Class tag)
        {
            using (Connection con = new Connection())
            {
                p.TagName = tag.TagName;
                p.Description = tag.Description;
                p.Count = 0;
                p.ExcerptPostId = tag.ExcerptPostId;
                p.WikiPostId = tag.WikiPostId;
                p.IsRequired = tag.IsRequired;
                p.IsModerator = tag.IsModerator;
                p.CreatedDate = DateTime.Now;

                try
                {
                    con.Tags.Add(p);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public void AfficherDetails(long tagId, Tags_Class tag)
        {
            using (Connection con = new Connection())
            {
                p = con.Tags.Where(x => x.TagId == tagId).FirstOrDefault();

                if (p != null)
                {
                    tag.TagId = p.TagId;
                    tag.TagName = p.TagName;
                    tag.Description = p.Description;
                    tag.Count = p.Count;
                    tag.ExcerptPostId = p.ExcerptPostId;
                    tag.WikiPostId = p.WikiPostId;
                    tag.IsRequired = p.IsRequired;
                    tag.IsModerator = p.IsModerator;
                    tag.CreatedDate = p.CreatedDate;

                    // Calculer les statistiques
                    tag.QuestionCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                   .Count(x => x.pt.TagId == tagId && x.s.PostTypeId == 1);
                    
                    tag.AnswerCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                 .Count(x => x.pt.TagId == tagId && x.s.PostTypeId == 2);

                    // Activité récente
                    var recentActivity = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                     .Where(x => x.pt.TagId == tagId)
                                                     .OrderByDescending(x => x.s.CreationDate)
                                                     .FirstOrDefault();

                    if (recentActivity != null)
                    {
                        tag.LastUsedDate = recentActivity.s.CreationDate;
                        tag.LastUsedBy = recentActivity.s.OwnerDisplayName;
                    }

                    // Calculer la popularité
                    tag.TodayCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                 .Count(x => x.pt.TagId == tagId && x.s.CreationDate >= DateTime.Today);

                    tag.WeekCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                               .Count(x => x.pt.TagId == tagId && x.s.CreationDate >= DateTime.Now.AddDays(-7));

                    tag.MonthCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                .Count(x => x.pt.TagId == tagId && x.s.CreationDate >= DateTime.Now.AddDays(-30));
                }
            }
        }

        public void Chargement_GDV(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var obj = (from t in con.Tags
                           select new
                           {
                               TagId = t.TagId,
                               TagName = t.TagName,
                               Description = t.Description,
                               Count = t.Count,
                               IsRequired = t.IsRequired,
                               IsModerator = t.IsModerator,
                               CreatedDate = t.CreatedDate,
                               QuestionCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                           .Count(x => x.pt.TagId == t.TagId && x.s.PostTypeId == 1),
                               TodayCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                        .Count(x => x.pt.TagId == t.TagId && x.s.CreationDate >= DateTime.Today)
                           }).OrderByDescending(t => t.Count).ToList();

                gdv.DataSource = obj;
                gdv.DataBind();
            }
        }

        public void ChargerTagsPopulaires(ListView lv, int limite = 50)
        {
            using (Connection con = new Connection())
            {
                var tags = from t in con.Tags
                          where t.Count > 0
                          orderby t.Count descending
                          select new
                          {
                              t.TagId,
                              t.TagName,
                              t.Description,
                              t.Count,
                              QuestionCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                          .Count(x => x.pt.TagId == t.TagId && x.s.PostTypeId == 1),
                              TodayCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                       .Count(x => x.pt.TagId == t.TagId && x.s.CreationDate >= DateTime.Today),
                              WeekCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                      .Count(x => x.pt.TagId == t.TagId && x.s.CreationDate >= DateTime.Now.AddDays(-7))
                          };

                lv.DataSource = tags.Take(limite).ToList();
                lv.DataBind();
            }
        }

        public void ChargerTagsTendance(ListView lv, int limite = 20)
        {
            using (Connection con = new Connection())
            {
                var tags = from t in con.Tags
                          let weekCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                      .Count(x => x.pt.TagId == t.TagId && x.s.CreationDate >= DateTime.Now.AddDays(-7))
                          let monthCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                       .Count(x => x.pt.TagId == t.TagId && x.s.CreationDate >= DateTime.Now.AddDays(-30))
                          where weekCount > 0
                          let trendScore = monthCount > 0 ? (double)weekCount / monthCount * 4 : weekCount
                          orderby trendScore descending
                          select new
                          {
                              t.TagId,
                              t.TagName,
                              t.Description,
                              t.Count,
                              WeekCount = weekCount,
                              MonthCount = monthCount,
                              TrendScore = trendScore,
                              IsTrending = trendScore > 1.5
                          };

                lv.DataSource = tags.Take(limite).ToList();
                lv.DataBind();
            }
        }

        public void ChargerTagsRecents(ListView lv, int limite = 20)
        {
            using (Connection con = new Connection())
            {
                var tags = from t in con.Tags
                          orderby t.CreatedDate descending
                          select new
                          {
                              t.TagId,
                              t.TagName,
                              t.Description,
                              t.Count,
                              t.CreatedDate,
                              QuestionCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                          .Count(x => x.pt.TagId == t.TagId && x.s.PostTypeId == 1)
                          };

                lv.DataSource = tags.Take(limite).ToList();
                lv.DataBind();
            }
        }

        public List<Tags_Class> SuggererTags(string terme, int limite = 10)
        {
            using (Connection con = new Connection())
            {
                var tags = con.Tags.Where(t => t.TagName.Contains(terme))
                                  .OrderByDescending(t => t.Count)
                                  .Take(limite)
                                  .Select(t => new Tags_Class
                                  {
                                      TagId = t.TagId,
                                      TagName = t.TagName,
                                      Description = t.Description,
                                      Count = t.Count
                                  }).ToList();

                return tags;
            }
        }

        public void RechercherTags(ListView lv, string terme)
        {
            using (Connection con = new Connection())
            {
                var tags = from t in con.Tags
                          where t.TagName.Contains(terme) || t.Description.Contains(terme)
                          orderby t.Count descending
                          select new
                          {
                              t.TagId,
                              t.TagName,
                              t.Description,
                              t.Count,
                              QuestionCount = con.PostTags.Join(con.SujetForums, pt => pt.PostId, s => s.SujetForumId, (pt, s) => new { pt, s })
                                                          .Count(x => x.pt.TagId == t.TagId && x.s.PostTypeId == 1)
                          };

                lv.DataSource = tags.ToList();
                lv.DataBind();
            }
        }

        public void AssocierTagPost(long postId, long tagId)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si l'association existe déjà
                var existant = con.PostTags.Where(pt => pt.PostId == postId && pt.TagId == tagId).FirstOrDefault();
                
                if (existant == null)
                {
                    var postTag = new PostTags
                    {
                        PostId = postId,
                        TagId = tagId
                    };

                    con.PostTags.Add(postTag);
                    con.SaveChanges();

                    // Mettre à jour le compteur du tag
                    MettreAJourCompteur(tagId);
                }
            }
        }

        public void DissocierTagPost(long postId, long tagId)
        {
            using (Connection con = new Connection())
            {
                var postTag = con.PostTags.Where(pt => pt.PostId == postId && pt.TagId == tagId).FirstOrDefault();
                
                if (postTag != null)
                {
                    con.PostTags.Remove(postTag);
                    con.SaveChanges();

                    // Mettre à jour le compteur du tag
                    MettreAJourCompteur(tagId);
                }
            }
        }

        public void MettreAJourTagsPost(long postId, List<string> tagNames)
        {
            using (Connection con = new Connection())
            {
                // Supprimer tous les tags existants du post
                var tagsExistants = con.PostTags.Where(pt => pt.PostId == postId).ToList();
                con.PostTags.RemoveRange(tagsExistants);

                // Ajouter les nouveaux tags
                foreach (var tagName in tagNames)
                {
                    // Chercher ou créer le tag
                    var tag = con.Tags.Where(t => t.TagName == tagName).FirstOrDefault();
                    if (tag == null)
                    {
                        tag = new Tags
                        {
                            TagName = tagName,
                            Count = 0,
                            CreatedDate = DateTime.Now
                        };
                        con.Tags.Add(tag);
                        con.SaveChanges();
                    }

                    // Associer le tag au post
                    var postTag = new PostTags
                    {
                        PostId = postId,
                        TagId = tag.TagId
                    };
                    con.PostTags.Add(postTag);
                }

                con.SaveChanges();

                // Mettre à jour les compteurs de tous les tags affectés
                foreach (var tagName in tagNames)
                {
                    var tag = con.Tags.Where(t => t.TagName == tagName).FirstOrDefault();
                    if (tag != null)
                    {
                        MettreAJourCompteur(tag.TagId);
                    }
                }

                // Mettre à jour les compteurs des anciens tags
                foreach (var ancienTag in tagsExistants)
                {
                    MettreAJourCompteur(ancienTag.TagId);
                }
            }
        }

        public List<Tags_Class> ObtenirTagsPost(long postId)
        {
            using (Connection con = new Connection())
            {
                var tags = from pt in con.PostTags
                          join t in con.Tags on pt.TagId equals t.TagId
                          where pt.PostId == postId
                          select new Tags_Class
                          {
                              TagId = t.TagId,
                              TagName = t.TagName,
                              Description = t.Description,
                              Count = t.Count
                          };

                return tags.ToList();
            }
        }

        public void MettreAJourCompteur(long tagId)
        {
            using (Connection con = new Connection())
            {
                var tag = con.Tags.Where(t => t.TagId == tagId).FirstOrDefault();
                if (tag != null)
                {
                    tag.Count = con.PostTags.Count(pt => pt.TagId == tagId);
                    con.SaveChanges();
                }
            }
        }

        public void MettreAJourTousCompteurs()
        {
            using (Connection con = new Connection())
            {
                var tags = con.Tags.ToList();
                foreach (var tag in tags)
                {
                    tag.Count = con.PostTags.Count(pt => pt.TagId == tag.TagId);
                }
                con.SaveChanges();
            }
        }

        public int Count()
        {
            using (Connection con = new Connection())
            {
                return con.Tags.Count();
            }
        }

        public int Edit(Tags_Class tag, long tagId)
        {
            using (Connection con = new Connection())
            {
                p = con.Tags.Where(x => x.TagId == tagId).FirstOrDefault();

                try
                {
                    p.TagName = tag.TagName;
                    p.Description = tag.Description;
                    p.IsRequired = tag.IsRequired;
                    p.IsModerator = tag.IsModerator;

                    if (con.SaveChanges() == 1)
                    {
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public void Search(GridView gdv, string searchTerm)
        {
            using (Connection con = new Connection())
            {
                var obj = (from t in con.Tags
                           where t.TagName.Contains(searchTerm) || t.Description.Contains(searchTerm)
                           select new
                           {
                               TagId = t.TagId,
                               TagName = t.TagName,
                               Description = t.Description,
                               Count = t.Count,
                               CreatedDate = t.CreatedDate
                           }).ToList();

                gdv.DataSource = obj;
                gdv.DataBind();
            }
        }

        public int Supprimer(long tagId)
        {
            using (Connection con = new Connection())
            {
                // Supprimer d'abord toutes les associations PostTags
                var postTags = con.PostTags.Where(pt => pt.TagId == tagId).ToList();
                con.PostTags.RemoveRange(postTags);

                // Supprimer le tag
                p = con.Tags.Where(x => x.TagId == tagId).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.Tags.Attach(p);

                con.Tags.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }

        // Méthodes supplémentaires à implémenter...
        public void ChargerTagsParCategorie(ListView lv, long categoryId) { throw new NotImplementedException(); }
        public void ChargerTagsUtilisateur(ListView lv, long userId, int limite = 20) { throw new NotImplementedException(); }
        public List<Tags_Class> ObtenirTagsSimilaires(long tagId, int limite = 5) { throw new NotImplementedException(); }
        public List<Tags_Class> ObtenirTagsRelies(string tagName, int limite = 10) { throw new NotImplementedException(); }
        public void ChargerPostsParTag(ListView lv, long tagId, string filtre = "recent") { throw new NotImplementedException(); }
        public void ChargerStatistiquesTag(long tagId, out int questions, out int reponses, out int utilisateurs) { throw new NotImplementedException(); }
        public void ChargerActiviteTag(ListView lv, long tagId, int limite = 10) { throw new NotImplementedException(); }
        public void ChargerExpertsTag(ListView lv, long tagId, int limite = 5) { throw new NotImplementedException(); }
        public void ChargerQuestionsNonRepondues(ListView lv, long tagId, int limite = 10) { throw new NotImplementedException(); }
        public void FusionnerTags(long tagSource, long tagDestination) { throw new NotImplementedException(); }
        public void CreerSynonyme(long tagPrincipal, string synonyme) { throw new NotImplementedException(); }
        public void SupprimerSynonyme(long tagId, string synonyme) { throw new NotImplementedException(); }
        public List<string> ObtenirSynonymes(long tagId) { throw new NotImplementedException(); }
        public void MarquerCommeRequis(long tagId, bool requis) { throw new NotImplementedException(); }
        public void MarquerCommeModerator(long tagId, bool moderator) { throw new NotImplementedException(); }
        public void DefinirWiki(long tagId, string wikiContent) { throw new NotImplementedException(); }
        public void DefinirExtrait(long tagId, string excerpt) { throw new NotImplementedException(); }
        public void CalculerPopularite(long tagId) { throw new NotImplementedException(); }
        public void MarquerCommeTendance(long tagId, bool tendance) { throw new NotImplementedException(); }
        public List<Tags_Class> ExporterTags() { throw new NotImplementedException(); }
        public void ImporterTags(List<Tags_Class> tags) { throw new NotImplementedException(); }
        public void GenererNuageTags(Literal literal, int limite = 100) { throw new NotImplementedException(); }
    }
}
