﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IReplyForum
    {
        // Méthodes CRUD de base (compatibilité LinCom)
        int add(ReplyForum_Class add);
        void Chargement_GDV(GridView GV_apv);
        void search(GridView GV_apv, string code);
        void afficherDetails(int code, ReplyForum_Class pr);
        void afficherDetails(string code, ReplyForum_Class pr);
        int edit(ReplyForum_Class cl, int id);
        int supprimer(int id);
        void chargerReplyFroum(DropDownList lst);
        int count();

        // Méthodes Stack Overflow style - Réponses
        int CreerReponse(ReplyForum_Class reponse);
        int CreerCommentaire(Comments_Class commentaire);
        void ChargerReponses(ListView lv, long questionId, string tri = "votes");
        void ChargerCommentaires(ListView lv, long postId);
        void ChargerCommentairesHierarchiques(ListView lv, long postId);

        // Gestion des réponses
        void MarquerCommeMeilleureReponse(long reponseId, long questionId, long userId);
        void SupprimerMeilleureReponse(long questionId, long userId);
        ReplyForum_Class ObtenirMeilleureReponse(long questionId);
        bool EstMeilleureReponse(long reponseId);
        int CompterReponses(long questionId);

        // Gestion des commentaires
        int AjouterCommentaire(long postId, string texte, long userId);
        int ModifierCommentaire(long commentId, string nouveauTexte, long userId);
        int SupprimerCommentaire(long commentId, long userId);
        void ChargerCommentairesPost(ListView lv, long postId);
        int CompterCommentaires(long postId);

        // Threading et hiérarchie
        void ChargerReponsesHierarchiques(ListView lv, long questionId);
        void ChargerCommentairesAvecReplies(ListView lv, long postId);
        int AjouterReponseCommentaire(long commentaireParentId, string texte, long userId);
        List<Comments_Class> ObtenirFilCommentaires(long commentaireParentId);

        // Votes sur réponses et commentaires
        int VoterReponse(long reponseId, long userId, bool upVote);
        int VoterCommentaire(long commentaireId, long userId, bool upVote);
        int AnnulerVoteReponse(long reponseId, long userId);
        int AnnulerVoteCommentaire(long commentaireId, long userId);
        void ChargerScoresReponses(long questionId, out Dictionary<long, int> scores);

        // Filtrage et tri
        void ChargerReponsesParScore(ListView lv, long questionId, bool descendant = true);
        void ChargerReponsesParDate(ListView lv, long questionId, bool recent = true);
        void ChargerReponsesParActivite(ListView lv, long questionId);
        void ChargerMeilleuresReponses(ListView lv, long userId, int limite = 10);

        // Recherche dans les réponses
        void RechercherReponses(ListView lv, string terme, long? questionId = null);
        void ChargerReponsesUtilisateur(ListView lv, long userId, string filtre = "recent");
        void ChargerReponsesPopulaires(ListView lv, int limite = 20);
        void ChargerReponsesRecentes(ListView lv, int limite = 20);

        // Édition et historique
        void ModifierReponse(long reponseId, string nouveauContenu, long editeurId);
        void ChargerHistoriqueReponse(ListView lv, long reponseId);
        void AnnulerModificationReponse(long reponseId, long revisionId);
        bool PeutModifierReponse(long reponseId, long userId);

        // Modération
        void SupprimerReponse(long reponseId, long moderateurId, string raison);
        void RestaurerReponse(long reponseId, long moderateurId);
        void SupprimerCommentaire(long commentaireId, long moderateurId, string raison);
        void ChargerReponsesSignalees(ListView lv, int limite = 20);
        void ChargerCommentairesSignales(ListView lv, int limite = 20);

        // Statistiques
        void ChargerStatistiquesReponses(long userId, out int totalReponses, out int meilleuresReponses, out int scoreTotal);
        void ChargerActiviteReponses(ListView lv, long userId, int limite = 10);
        void ChargerTopRepondeurs(ListView lv, string periode = "month", int limite = 10);

        // Notifications
        void NotifierNouvelleReponse(long questionId, long reponseId);
        void NotifierNouveauCommentaire(long postId, long commentaireId);
        void NotifierMeilleureReponse(long reponseId, long userId);
        void AbonnerAuxReponses(long questionId, long userId);
        void DesabonnerDesReponses(long questionId, long userId);

        // Qualité et validation
        bool EstReponseValide(string contenu);
        bool EstCommentaireValide(string texte);
        void MarquerCommeSpam(long reponseId, long userId);
        void MarquerCommeOffensant(long reponseId, long userId);
        void ApprouverReponse(long reponseId, long moderateurId);

        // Export et partage
        void ExporterReponse(long reponseId, string format = "html");
        string GenererLienReponse(long reponseId);
        void ChargerReponsesExportees(ListView lv, long userId);
    }
}
