<%@ Page Title="Questions - Forum LinCom" Language="C#" MasterPageFile="~/MasterPage.Master" AutoEventWireup="true" CodeBehind="forum-questions.aspx.cs" Inherits="LinCom.forum_questions" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="Content/forum-styles.css" rel="stylesheet" />
    <style>
        .forum-header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-bottom: 3px solid #e74c3c;
        }

        .question-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: white;
            transition: all 0.3s ease;
            border-left: 4px solid #3498db;
        }

        .question-item:hover {
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
            border-left-color: #e74c3c;
        }
        
        .question-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .stat-box {
            text-align: center;
            padding: 0.5rem;
            border-radius: 4px;
            min-width: 60px;
        }
        
        .votes { background: #f8f9fa; }
        .answers { background: #e8f5e9; }
        .answers.accepted { background: #4caf50; color: white; }
        .views { background: #fff3e0; }
        
        .question-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            text-decoration: none;
            margin-bottom: 0.5rem;
            display: block;
        }

        .question-title:hover {
            color: #e74c3c;
            text-decoration: underline;
        }
        
        .question-excerpt {
            color: #586069;
            margin-bottom: 1rem;
            line-height: 1.5;
        }
        
        .question-tags {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .tag {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.85rem;
            text-decoration: none;
            border: 1px solid #bdc3c7;
        }

        .tag:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .question-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #586069;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #0366d6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }
        
        .filter-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #e1e4e8;
        }
        
        .filter-tab {
            padding: 0.75rem 1rem;
            border: none;
            background: none;
            color: #586069;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            color: #e74c3c;
            border-bottom-color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }

        .filter-tab:hover {
            color: #e74c3c;
            background: rgba(231, 76, 60, 0.05);
        }

        .ask-question-btn {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
        }

        .ask-question-btn:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(231, 76, 60, 0.4);
        }
        
        .search-box {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5da;
            border-radius: 6px;
            font-size: 1rem;
            margin-bottom: 1rem;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        
        .page-btn {
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5da;
            background: white;
            color: #0366d6;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .page-btn:hover, .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .lincom-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }

        .lincom-card-header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 1rem;
            border-radius: 5px 5px 0 0;
            border-bottom: 2px solid #e74c3c;
        }

        .lincom-btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .lincom-btn-primary:hover {
            background: linear-gradient(135deg, #2980b9 0%, #21618c 100%);
            transform: translateY(-1px);
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Header du Forum LinCom -->
    <div class="forum-header">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h1><i class="fas fa-comments"></i> Forum LinCom</h1>
                    <p class="lead">Plateforme d'échange et d'entraide de la communauté LinCom</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="poser-question.aspx" class="ask-question-btn">
                        <i class="fas fa-plus"></i> Poser une Question
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- Sidebar Gauche LinCom -->
            <div class="col-md-3">
                <div class="lincom-card">
                    <div class="lincom-card-header">
                        <h5><i class="fas fa-search"></i> Recherche</h5>
                    </div>
                    <div class="card-body">
                        <asp:TextBox ID="txtSearch" runat="server" CssClass="form-control mb-2" placeholder="Rechercher des questions..."></asp:TextBox>
                        <asp:Button ID="btnSearch" runat="server" Text="Rechercher" CssClass="lincom-btn-primary btn-block" OnClick="btnSearch_Click" />
                    </div>
                </div>

                <div class="lincom-card mt-3">
                    <div class="lincom-card-header">
                        <h5><i class="fas fa-layer-group"></i> Catégories</h5>
                    </div>
                    <div class="card-body">
                        <asp:ListView ID="lvCategories" runat="server" OnItemCommand="lvCategories_ItemCommand">
                            <LayoutTemplate>
                                <div id="itemPlaceholder" runat="server"></div>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <div class="category-item mb-2">
                                    <asp:LinkButton ID="lnkCategory" runat="server" 
                                        CommandName="SelectCategory" 
                                        CommandArgument='<%# Eval("CategoryId") %>'
                                        CssClass="d-block text-decoration-none">
                                        <i class="fas <%# Eval("IconClass") %>" style="color: <%# Eval("Color") %>"></i>
                                        <%# Eval("Name") %>
                                        <span class="badge badge-secondary float-right"><%# Eval("QuestionCount") %></span>
                                    </asp:LinkButton>
                                </div>
                            </ItemTemplate>
                        </asp:ListView>
                    </div>
                </div>

                <div class="lincom-card mt-3">
                    <div class="lincom-card-header">
                        <h5><i class="fas fa-tags"></i> Tags Populaires</h5>
                    </div>
                    <div class="card-body">
                        <asp:ListView ID="lvTagsPopulaires" runat="server">
                            <LayoutTemplate>
                                <div id="itemPlaceholder" runat="server"></div>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <a href="questions-tag.aspx?tag=<%# Eval("TagName") %>" class="tag mb-1">
                                    <%# Eval("TagName") %>
                                    <span class="badge badge-light"><%# Eval("Count") %></span>
                                </a>
                            </ItemTemplate>
                        </asp:ListView>
                    </div>
                </div>
            </div>

            <!-- Contenu Principal -->
            <div class="col-md-9">
                <!-- Filtres -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2>
                        <asp:Label ID="lblTitre" runat="server" Text="Toutes les Questions"></asp:Label>
                        <small class="text-muted">(<asp:Label ID="lblCount" runat="server"></asp:Label> questions)</small>
                    </h2>
                    
                    <div class="filter-tabs">
                        <asp:Button ID="btnRecent" runat="server" Text="Récentes" CssClass="filter-tab active" OnClick="btnFilter_Click" CommandArgument="recent" />
                        <asp:Button ID="btnPopular" runat="server" Text="Populaires" CssClass="filter-tab" OnClick="btnFilter_Click" CommandArgument="popular" />
                        <asp:Button ID="btnUnanswered" runat="server" Text="Non Répondues" CssClass="filter-tab" OnClick="btnFilter_Click" CommandArgument="unanswered" />
                        <asp:Button ID="btnFeatured" runat="server" Text="En Vedette" CssClass="filter-tab" OnClick="btnFilter_Click" CommandArgument="featured" />
                    </div>
                </div>

                <!-- Liste des Questions -->
                <asp:ListView ID="lvQuestions" runat="server" OnItemCommand="lvQuestions_ItemCommand">
                    <LayoutTemplate>
                        <div id="itemPlaceholder" runat="server"></div>
                    </LayoutTemplate>
                    <ItemTemplate>
                        <div class="question-item">
                            <!-- Statistiques -->
                            <div class="question-stats">
                                <div class="stat-box votes">
                                    <div class="font-weight-bold"><%# Eval("Score") %></div>
                                    <small>votes</small>
                                </div>
                                <div class="stat-box answers <%# Convert.ToBoolean(Eval("HasAcceptedAnswer")) ? "accepted" : "" %>">
                                    <div class="font-weight-bold"><%# Eval("AnswerCount") %></div>
                                    <small>réponses</small>
                                </div>
                                <div class="stat-box views">
                                    <div class="font-weight-bold"><%# Eval("ViewCount") %></div>
                                    <small>vues</small>
                                </div>
                            </div>

                            <!-- Titre et Contenu -->
                            <div class="question-content">
                                <a href="question-detail.aspx?id=<%# Eval("SujetForumId") %>" class="question-title">
                                    <%# Eval("Title") %>
                                    <%# Convert.ToBoolean(Eval("IsPinned")) ? "<i class='fas fa-thumbtack text-warning'></i>" : "" %>
                                    <%# Convert.ToBoolean(Eval("IsFeatured")) ? "<i class='fas fa-star text-warning'></i>" : "" %>
                                </a>
                                
                                <div class="question-excerpt">
                                    <%# TruncateText(Eval("Body").ToString(), 200) %>
                                </div>

                                <!-- Tags -->
                                <div class="question-tags">
                                    <%# GenerateTagsHtml(Eval("Tags").ToString()) %>
                                </div>

                                <!-- Métadonnées -->
                                <div class="question-meta">
                                    <div class="question-time">
                                        Posée <%# GetTimeAgo(Convert.ToDateTime(Eval("CreationDate"))) %>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-avatar">
                                            <%# GetUserInitials(Eval("OwnerName").ToString()) %>
                                        </div>
                                        <div>
                                            <strong><%# Eval("OwnerName") %></strong>
                                            <div class="text-muted small">
                                                <i class="fas fa-trophy"></i> <%# Eval("OwnerReputation") %>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ItemTemplate>
                    <EmptyDataTemplate>
                        <div class="text-center py-5">
                            <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                            <h4>Aucune question trouvée</h4>
                            <p class="text-muted">Soyez le premier à poser une question !</p>
                            <a href="poser-question.aspx" class="ask-question-btn">
                                <i class="fas fa-plus"></i> Poser une Question
                            </a>
                        </div>
                    </EmptyDataTemplate>
                </asp:ListView>

                <!-- Pagination -->
                <div class="pagination">
                    <asp:Repeater ID="rptPagination" runat="server">
                        <ItemTemplate>
                            <asp:LinkButton ID="lnkPage" runat="server" 
                                CssClass='<%# "page-btn " + (Convert.ToInt32(Eval("PageNumber")) == CurrentPage ? "active" : "") %>'
                                CommandName="Page" 
                                CommandArgument='<%# Eval("PageNumber") %>'
                                OnCommand="Pagination_Command">
                                <%# Eval("PageNumber") %>
                            </asp:LinkButton>
                        </ItemTemplate>
                    </asp:Repeater>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Gestion des filtres
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Auto-refresh toutes les 30 secondes pour les nouvelles questions
        setInterval(function() {
            if (document.querySelector('.filter-tab.active').textContent === 'Récentes') {
                __doPostBack('<%=btnRecent.UniqueID%>', '');
            }
        }, 30000);
    </script>
</asp:Content>
