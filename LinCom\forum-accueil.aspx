<%@ Page Title="Forum LinCom - Accueil" Language="C#" MasterPageFile="~/MasterPage.Master" AutoEventWireup="true" CodeBehind="forum-accueil.aspx.cs" Inherits="LinCom.forum_accueil" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="Content/forum-lincom.css" rel="stylesheet" />
    <style>
        .hero-section {
            background: var(--lincom-gradient-primary);
            color: white;
            padding: 4rem 0;
            margin-bottom: 3rem;
            border-bottom: 4px solid var(--lincom-accent);
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: var(--lincom-white);
            border: 1px solid var(--lincom-border);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            border-left: 4px solid var(--lincom-secondary);
            box-shadow: var(--lincom-shadow);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--lincom-shadow-hover);
            border-left-color: var(--lincom-accent);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--lincom-gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 1.5rem auto;
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--lincom-primary);
            margin-bottom: 1rem;
        }
        
        .feature-description {
            color: var(--lincom-text-muted);
            line-height: 1.6;
        }
        
        .stats-section {
            background: var(--lincom-light);
            padding: 3rem 0;
            margin: 3rem 0;
            border-radius: 12px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 1.5rem;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: var(--lincom-secondary);
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: var(--lincom-text-muted);
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 1px;
        }
        
        .recent-activity {
            background: var(--lincom-white);
            border: 1px solid var(--lincom-border);
            border-radius: 12px;
            padding: 2rem;
            border-left: 4px solid var(--lincom-secondary);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid var(--lincom-light);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--lincom-gradient-secondary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 500;
            color: var(--lincom-primary);
            margin-bottom: 0.25rem;
        }
        
        .activity-meta {
            color: var(--lincom-text-muted);
            font-size: 0.9rem;
        }
        
        .cta-section {
            background: var(--lincom-gradient-accent);
            color: white;
            padding: 3rem 0;
            border-radius: 12px;
            text-align: center;
            margin: 3rem 0;
        }
        
        .cta-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .cta-description {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .cta-btn {
            background: white;
            color: var(--lincom-accent);
            padding: 1rem 2rem;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 3px 6px rgba(0,0,0,0.2);
        }
        
        .cta-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(0,0,0,0.3);
            color: var(--lincom-accent);
            text-decoration: none;
        }
        
        .cta-btn-outline {
            background: transparent;
            color: white;
            border: 2px solid white;
        }
        
        .cta-btn-outline:hover {
            background: white;
            color: var(--lincom-accent);
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Section Hero -->
    <div class="hero-section">
        <div class="container">
            <div class="hero-content text-center">
                <h1 class="display-4 mb-4">
                    <i class="fas fa-comments"></i> Forum LinCom
                </h1>
                <p class="lead mb-4">
                    La plateforme d'échange et d'entraide de la communauté LinCom.<br>
                    Posez vos questions, partagez vos connaissances et apprenez ensemble.
                </p>
                <div class="cta-buttons">
                    <a href="forum-questions.aspx" class="cta-btn">
                        <i class="fas fa-list"></i> Voir les Questions
                    </a>
                    <a href="poser-question.aspx" class="cta-btn-outline">
                        <i class="fas fa-plus"></i> Poser une Question
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Fonctionnalités -->
        <div class="text-center mb-5">
            <h2 class="lincom-text-primary">Fonctionnalités du Forum</h2>
            <p class="lincom-text-muted">Découvrez tout ce que notre forum peut vous offrir</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <h3 class="feature-title">Questions & Réponses</h3>
                <p class="feature-description">
                    Posez vos questions et obtenez des réponses de qualité de la part de la communauté LinCom.
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-vote-yea"></i>
                </div>
                <h3 class="feature-title">Système de Votes</h3>
                <p class="feature-description">
                    Votez pour les meilleures questions et réponses pour aider la communauté à identifier le contenu de qualité.
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-medal"></i>
                </div>
                <h3 class="feature-title">Badges & Réputation</h3>
                <p class="feature-description">
                    Gagnez des badges et de la réputation en contribuant activement à la communauté.
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <h3 class="feature-title">Organisation par Tags</h3>
                <p class="feature-description">
                    Organisez et trouvez facilement le contenu grâce à un système de tags intelligent.
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="feature-title">Recherche Avancée</h3>
                <p class="feature-description">
                    Trouvez rapidement les informations que vous cherchez avec notre moteur de recherche puissant.
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="feature-title">Communauté Active</h3>
                <p class="feature-description">
                    Rejoignez une communauté dynamique de développeurs, étudiants et professionnels.
                </p>
            </div>
        </div>

        <!-- Statistiques -->
        <div class="stats-section">
            <div class="container">
                <div class="text-center mb-4">
                    <h2 class="lincom-text-primary">Statistiques du Forum</h2>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">
                            <asp:Label ID="lblTotalQuestions" runat="server" Text="0"></asp:Label>
                        </span>
                        <div class="stat-label">Questions</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">
                            <asp:Label ID="lblTotalAnswers" runat="server" Text="0"></asp:Label>
                        </span>
                        <div class="stat-label">Réponses</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">
                            <asp:Label ID="lblTotalUsers" runat="server" Text="0"></asp:Label>
                        </span>
                        <div class="stat-label">Utilisateurs</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">
                            <asp:Label ID="lblTotalViews" runat="server" Text="0"></asp:Label>
                        </span>
                        <div class="stat-label">Vues</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Activité Récente -->
            <div class="col-md-6">
                <div class="recent-activity">
                    <h3 class="lincom-text-primary mb-3">
                        <i class="fas fa-clock"></i> Activité Récente
                    </h3>
                    <asp:ListView ID="lvRecentActivity" runat="server">
                        <LayoutTemplate>
                            <div id="itemPlaceholder" runat="server"></div>
                        </LayoutTemplate>
                        <ItemTemplate>
                            <div class="activity-item">
                                <div class="activity-icon" style='background: <%# GetActivityColor(Eval("Type").ToString()) %>'>
                                    <i class="<%# GetActivityIcon(Eval("Type").ToString()) %>"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title"><%# Eval("Title") %></div>
                                    <div class="activity-meta">
                                        par <%# Eval("UserName") %> • <%# GetTimeAgo(Convert.ToDateTime(Eval("Date"))) %>
                                    </div>
                                </div>
                            </div>
                        </ItemTemplate>
                        <EmptyDataTemplate>
                            <div class="text-center py-4">
                                <i class="fas fa-clock fa-2x lincom-text-muted mb-3"></i>
                                <p class="lincom-text-muted">Aucune activité récente.</p>
                            </div>
                        </EmptyDataTemplate>
                    </asp:ListView>
                </div>
            </div>

            <!-- Questions Populaires -->
            <div class="col-md-6">
                <div class="recent-activity">
                    <h3 class="lincom-text-primary mb-3">
                        <i class="fas fa-fire"></i> Questions Populaires
                    </h3>
                    <asp:ListView ID="lvPopularQuestions" runat="server">
                        <LayoutTemplate>
                            <div id="itemPlaceholder" runat="server"></div>
                        </LayoutTemplate>
                        <ItemTemplate>
                            <div class="activity-item">
                                <div class="activity-icon lincom-bg-accent">
                                    <i class="fas fa-question"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">
                                        <a href="question-detail.aspx?id=<%# Eval("SujetForumId") %>" class="lincom-text-primary">
                                            <%# TruncateText(Eval("Title").ToString(), 50) %>
                                        </a>
                                    </div>
                                    <div class="activity-meta">
                                        <%# Eval("Score") %> votes • <%# Eval("AnswerCount") %> réponses • <%# Eval("ViewCount") %> vues
                                    </div>
                                </div>
                            </div>
                        </ItemTemplate>
                        <EmptyDataTemplate>
                            <div class="text-center py-4">
                                <i class="fas fa-question-circle fa-2x lincom-text-muted mb-3"></i>
                                <p class="lincom-text-muted">Aucune question populaire.</p>
                            </div>
                        </EmptyDataTemplate>
                    </asp:ListView>
                </div>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="cta-section">
        <div class="container">
            <h2 class="cta-title">Rejoignez la Communauté LinCom</h2>
            <p class="cta-description">
                Commencez dès maintenant à poser vos questions et à partager vos connaissances
            </p>
            <div class="cta-buttons">
                <a href="poser-question.aspx" class="cta-btn">
                    <i class="fas fa-plus"></i> Poser ma Première Question
                </a>
                <a href="forum-questions.aspx" class="cta-btn-outline">
                    <i class="fas fa-eye"></i> Explorer le Forum
                </a>
            </div>
        </div>
    </div>

    <script>
        // Animation au scroll
        window.addEventListener('scroll', function() {
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                if (rect.top < window.innerHeight && rect.bottom > 0) {
                    card.classList.add('lincom-fade-in');
                }
            });
        });

        // Animation des compteurs
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');
            counters.forEach(counter => {
                const target = parseInt(counter.textContent);
                const increment = target / 100;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target;
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current);
                    }
                }, 20);
            });
        }

        // Démarrer l'animation des compteurs quand la section est visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        });

        const statsSection = document.querySelector('.stats-section');
        if (statsSection) {
            observer.observe(statsSection);
        }
    </script>
</asp:Content>
