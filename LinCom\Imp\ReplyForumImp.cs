﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
   
    public class ReplyForumImp : IReplyForum
    {
        int msg;
        RepliesForum p = new RepliesForum();
        public int add(ReplyForum_Class add)
        {
            using (Connection con = new Connection())
            {

                p.SujetForumId = add.SujetForumId;
                p.MembreId = add.MembreId;
                p.Contenu = add.Contenu;
                p.DateReply = add.DateReply;
                p.name = add.name;
                try
                {
                    con.RepliesForums.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.RepliesForums.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void afficherDetails(int code, ReplyForum_Class pr)
        {
            using (Connection con = new Connection())
            {
                p = con.RepliesForums.Where(x => x.RepliesForumId == code).FirstOrDefault();

                if (p != null)
                {

                    pr.SujetForumId = p.SujetForumId;
                    pr.MembreId = p.MembreId;
                    pr.Contenu = p.Contenu;
                    pr.DateReply = p.DateReply;
                    pr.name = p.name;

                }

            }
        }

        public void afficherDetails(string code, ReplyForum_Class pr)
        {
            throw new NotImplementedException();
        }

        public void Chargement_GDV(GridView GV_apv)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.RepliesForums
                           join a in con.SujetForums on ep.SujetForumId equals a.SujetForumId
                           join e in con.Membres on ep.MembreId equals e.MembreId
                           select new
                           {


                               SujetForm = ep.SujetForumId,
                               membre = ep.MembreId,
                               contenu = ep.Contenu,

                               date = ep.DateReply,
                               name = ep.name


                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public void chargerReplyFroum(DropDownList lst)
        {
            throw new NotImplementedException();
        }

        public int count()
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                var b = (from l in con.Provinces
                         select l).Count();
                n = b;
            }
            return n;
        }

        public int edit(ReplyForum_Class cl, int id)
        {
            using (Connection con = new Connection())
            {
                p = con.RepliesForums.Where(x => x.RepliesForumId == id).FirstOrDefault();

                try
                {
                    p.SujetForumId = cl.SujetForumId;
                    p.MembreId = cl.MembreId;
                    p.Contenu = cl.Contenu;
                    p.DateReply = cl.DateReply;
                    p.name = cl.name;


                    if (con.SaveChanges() == 1)
                    {
                        con.RepliesForums.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }

        public void search(GridView GV_apv, string code)
        {
            using (Connection con = new Connection())
            {



                var obj = (from ep in con.RepliesForums
                           join a in con.SujetForums on ep.SujetForumId equals a.SujetForumId
                           join e in con.Membres on ep.MembreId equals e.MembreId
                           where (ep.MembreId.ToString().Contains(code) && ep.SujetForumId.ToString().Contains(code))
                           select new
                           {

                               SujetForm = ep.SujetForumId,
                               membre = ep.MembreId,
                               contenu = ep.Contenu,

                               date = ep.DateReply,
                               name = ep.name

                           }).ToList();


                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public int supprimer(int id)
        {
            using (Connection con = new Connection())
            {

                p = con.RepliesForums.Where(x => x.RepliesForumId == id).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.RepliesForums.Attach(p);

                con.RepliesForums.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }

        // MÉTHODES STACK OVERFLOW STYLE COMPLÈTES

        public int CreerReponse(ReplyForum_Class reponse)
        {
            using (Connection con = new Connection())
            {
                var nouvelleReponse = new SujetForum
                {
                    PostTypeId = 2, // Réponse
                    ParentId = reponse.SujetForumId,
                    Body = reponse.Contenu,
                    OwnerUserId = reponse.MembreId,
                    OwnerDisplayName = reponse.name,
                    CreationDate = DateTime.Now,
                    LastActivityDate = DateTime.Now,
                    Score = 0,
                    ViewCount = 0,
                    CommentCount = 0,
                    ContentLicense = "CC BY-SA 4.0"
                };

                try
                {
                    con.SujetForums.Add(nouvelleReponse);
                    if (con.SaveChanges() == 1)
                    {
                        // Mettre à jour le compteur de réponses de la question
                        var question = con.SujetForums.Where(q => q.SujetForumId == reponse.SujetForumId).FirstOrDefault();
                        if (question != null)
                        {
                            question.AnswerCount++;
                            question.LastActivityDate = DateTime.Now;
                            con.SaveChanges();
                        }

                        return (int)nouvelleReponse.SujetForumId;
                    }
                    return 0;
                }
                catch (Exception e)
                {
                    return 0;
                }
            }
        }

        public int CreerCommentaire(Comments_Class commentaire)
        {
            using (Connection con = new Connection())
            {
                var nouveauCommentaire = new Comments
                {
                    PostId = commentaire.PostId,
                    Score = 0,
                    Text = commentaire.Text,
                    CreationDate = DateTime.Now,
                    UserId = commentaire.UserId,
                    UserDisplayName = commentaire.UserDisplayName,
                    ContentLicense = "CC BY-SA 4.0",
                    IsApproved = true,
                    IsFlagged = false,
                    ParentCommentId = commentaire.ParentCommentId
                };

                try
                {
                    con.Comments.Add(nouveauCommentaire);
                    if (con.SaveChanges() == 1)
                    {
                        // Mettre à jour le compteur de commentaires du post
                        var post = con.SujetForums.Where(p => p.SujetForumId == commentaire.PostId).FirstOrDefault();
                        if (post != null)
                        {
                            post.CommentCount++;
                            con.SaveChanges();
                        }

                        return (int)nouveauCommentaire.CommentId;
                    }
                    return 0;
                }
                catch (Exception e)
                {
                    return 0;
                }
            }
        }

        public void ChargerReponses(ListView lv, long questionId, string tri = "votes")
        {
            using (Connection con = new Connection())
            {
                var query = from r in con.SujetForums
                           join m in con.Membres on r.OwnerUserId equals m.MembreId into memberJoin
                           from m in memberJoin.DefaultIfEmpty()
                           where r.PostTypeId == 2 && r.ParentId == questionId
                           select new
                           {
                               r.SujetForumId,
                               r.Body,
                               r.Score,
                               r.CreationDate,
                               r.LastEditDate,
                               OwnerName = m != null ? m.Nom + " " + m.Prenom : r.OwnerDisplayName,
                               OwnerReputation = m != null ? m.Reputation : 0,
                               IsAccepted = con.SujetForums.Any(q => q.SujetForumId == questionId && q.AcceptedAnswerId == r.SujetForumId),
                               CommentCount = r.CommentCount
                           };

                // Appliquer le tri
                switch (tri.ToLower())
                {
                    case "votes":
                        query = query.OrderByDescending(r => r.Score).ThenByDescending(r => r.IsAccepted);
                        break;
                    case "oldest":
                        query = query.OrderBy(r => r.CreationDate);
                        break;
                    case "active":
                        query = query.OrderByDescending(r => r.LastEditDate ?? r.CreationDate);
                        break;
                    default: // newest
                        query = query.OrderByDescending(r => r.CreationDate);
                        break;
                }

                lv.DataSource = query.ToList();
                lv.DataBind();
            }
        }

        public void ChargerCommentaires(ListView lv, long postId)
        {
            using (Connection con = new Connection())
            {
                var commentaires = from c in con.Comments
                                  join u in con.Membres on c.UserId equals u.MembreId into userJoin
                                  from u in userJoin.DefaultIfEmpty()
                                  where c.PostId == postId && c.ParentCommentId == null
                                  orderby c.CreationDate
                                  select new
                                  {
                                      c.CommentId,
                                      c.Text,
                                      c.Score,
                                      c.CreationDate,
                                      UserName = u != null ? u.Nom + " " + u.Prenom : c.UserDisplayName,
                                      UserReputation = u != null ? u.Reputation : 0,
                                      CanEdit = c.UserId == HttpContext.Current.Session["MembreId"],
                                      CanDelete = c.UserId == HttpContext.Current.Session["MembreId"]
                                  };

                lv.DataSource = commentaires.ToList();
                lv.DataBind();
            }
        }

        // Méthodes supplémentaires à implémenter...
        public void ChargerCommentairesHierarchiques(ListView lv, long postId) { throw new NotImplementedException(); }
        public void MarquerCommeMeilleureReponse(long reponseId, long questionId, long userId) { throw new NotImplementedException(); }
        public void SupprimerMeilleureReponse(long questionId, long userId) { throw new NotImplementedException(); }
        public ReplyForum_Class ObtenirMeilleureReponse(long questionId) { throw new NotImplementedException(); }
        public bool EstMeilleureReponse(long reponseId) { throw new NotImplementedException(); }
        public int CompterReponses(long questionId) { throw new NotImplementedException(); }
        public int AjouterCommentaire(long postId, string texte, long userId) { throw new NotImplementedException(); }
        public int ModifierCommentaire(long commentId, string nouveauTexte, long userId) { throw new NotImplementedException(); }
        public int SupprimerCommentaire(long commentId, long userId) { throw new NotImplementedException(); }
        public void ChargerCommentairesPost(ListView lv, long postId) { throw new NotImplementedException(); }
        public int CompterCommentaires(long postId) { throw new NotImplementedException(); }
        public void ChargerReponsesHierarchiques(ListView lv, long questionId) { throw new NotImplementedException(); }
        public void ChargerCommentairesAvecReplies(ListView lv, long postId) { throw new NotImplementedException(); }
        public int AjouterReponseCommentaire(long commentaireParentId, string texte, long userId) { throw new NotImplementedException(); }
        public List<Comments_Class> ObtenirFilCommentaires(long commentaireParentId) { throw new NotImplementedException(); }
        public int VoterReponse(long reponseId, long userId, bool upVote) { throw new NotImplementedException(); }
        public int VoterCommentaire(long commentaireId, long userId, bool upVote) { throw new NotImplementedException(); }
        public int AnnulerVoteReponse(long reponseId, long userId) { throw new NotImplementedException(); }
        public int AnnulerVoteCommentaire(long commentaireId, long userId) { throw new NotImplementedException(); }
        public void ChargerScoresReponses(long questionId, out Dictionary<long, int> scores) { throw new NotImplementedException(); }
        public void ChargerReponsesParScore(ListView lv, long questionId, bool descendant = true) { throw new NotImplementedException(); }
        public void ChargerReponsesParDate(ListView lv, long questionId, bool recent = true) { throw new NotImplementedException(); }
        public void ChargerReponsesParActivite(ListView lv, long questionId) { throw new NotImplementedException(); }
        public void ChargerMeilleuresReponses(ListView lv, long userId, int limite = 10) { throw new NotImplementedException(); }
        public void RechercherReponses(ListView lv, string terme, long? questionId = null) { throw new NotImplementedException(); }
        public void ChargerReponsesUtilisateur(ListView lv, long userId, string filtre = "recent") { throw new NotImplementedException(); }
        public void ChargerReponsesPopulaires(ListView lv, int limite = 20) { throw new NotImplementedException(); }
        public void ChargerReponsesRecentes(ListView lv, int limite = 20) { throw new NotImplementedException(); }
        public void ModifierReponse(long reponseId, string nouveauContenu, long editeurId) { throw new NotImplementedException(); }
        public void ChargerHistoriqueReponse(ListView lv, long reponseId) { throw new NotImplementedException(); }
        public void AnnulerModificationReponse(long reponseId, long revisionId) { throw new NotImplementedException(); }
        public bool PeutModifierReponse(long reponseId, long userId) { throw new NotImplementedException(); }
        public void SupprimerReponse(long reponseId, long moderateurId, string raison) { throw new NotImplementedException(); }
        public void RestaurerReponse(long reponseId, long moderateurId) { throw new NotImplementedException(); }
        public void SupprimerCommentaire(long commentaireId, long moderateurId, string raison) { throw new NotImplementedException(); }
        public void ChargerReponsesSignalees(ListView lv, int limite = 20) { throw new NotImplementedException(); }
        public void ChargerCommentairesSignales(ListView lv, int limite = 20) { throw new NotImplementedException(); }
        public void ChargerStatistiquesReponses(long userId, out int totalReponses, out int meilleuresReponses, out int scoreTotal) { throw new NotImplementedException(); }
        public void ChargerActiviteReponses(ListView lv, long userId, int limite = 10) { throw new NotImplementedException(); }
        public void ChargerTopRepondeurs(ListView lv, string periode = "month", int limite = 10) { throw new NotImplementedException(); }
        public void NotifierNouvelleReponse(long questionId, long reponseId) { throw new NotImplementedException(); }
        public void NotifierNouveauCommentaire(long postId, long commentaireId) { throw new NotImplementedException(); }
        public void NotifierMeilleureReponse(long reponseId, long userId) { throw new NotImplementedException(); }
        public void AbonnerAuxReponses(long questionId, long userId) { throw new NotImplementedException(); }
        public void DesabonnerDesReponses(long questionId, long userId) { throw new NotImplementedException(); }
        public bool EstReponseValide(string contenu) { throw new NotImplementedException(); }
        public bool EstCommentaireValide(string texte) { throw new NotImplementedException(); }
        public void MarquerCommeSpam(long reponseId, long userId) { throw new NotImplementedException(); }
        public void MarquerCommeOffensant(long reponseId, long userId) { throw new NotImplementedException(); }
        public void ApprouverReponse(long reponseId, long moderateurId) { throw new NotImplementedException(); }
        public void ExporterReponse(long reponseId, string format = "html") { throw new NotImplementedException(); }
        public string GenererLienReponse(long reponseId) { throw new NotImplementedException(); }
        public void ChargerReponsesExportees(ListView lv, long userId) { throw new NotImplementedException(); }

    }
}