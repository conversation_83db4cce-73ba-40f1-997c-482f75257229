<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="espaceforum.aspx.cs" Inherits="LinCom.espaceforum" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

       <!-- Titre de la page -->
<div class="page-title">
  <div class="heading">
    <div class="container">
      <div class="row d-flex justify-content-center text-center">
        <div class="col-lg-8">
          <h2 class="display-5 fw-bold">Forum</h2>
          <p class="lead">Rejoignez notre communauté en tant que mentor ou mentoré et développez vos compétences et votre réseau.</p>
        </div>
      </div>
    </div>
  </div>
  <nav class="breadcrumbs">
    <div class="container">
      <ol>
        <li><a href="home.aspx">Accueil</a></li>
        <li class="current">Forum</li>
      </ol>
    </div>
  </nav>
</div>
   <main class="main">

  <!-- Section Forum -->
  <section class="forum-posts section">
    <div class="container">
      <div class="row">

        <!-- Colonne gauche : Sujets de discussion -->
        <div class="col-lg-8">
            <!-- Formulaire de nouvelle question -->
<div class="card mb-4 p-3 shadow-sm">
  <h5 class="mb-3">Poser une nouvelle question</h5>
  <form id="new-question-form">
    <div class="mb-3">
      <label for="question-title" class="form-label">Titre de la question</label>
      <input type="text" class="form-control" id="question-title" placeholder="Ex: Comment lancer un projet pour les jeunes ?">
    </div>
    <div class="mb-3">
      <label for="question-category" class="form-label">Catégorie</label>
      <select class="form-select" id="question-category">
        <option value="">Choisir une catégorie</option>
        <option value="Jeunesse">Jeunesse</option>
        <option value="Santé">Santé</option>
        <option value="Éducation">Éducation</option>
        <option value="Économie">Économie</option>
      </select>
    </div>
    <div class="mb-3">
      <label for="question-content" class="form-label">Contenu</label>
      <textarea class="form-control" id="question-content" rows="4" placeholder="Expliquez votre problème ou votre question..."></textarea>
    </div>
    <div class="mb-3 text-end">
      <button type="submit" class="btn btn-primary">Publier la question</button>
    </div>
  </form>
</div>
            <!-- Filtres -->
<div class="d-flex justify-content-between align-items-center mb-3">
  <h4 class="mb-0">Questions du Forum</h4>
  <div>
    <button class="btn btn-outline-primary btn-sm me-2" onclick="filterQuestions('recent')">Forums récents</button>
    <button class="btn btn-outline-secondary btn-sm" onclick="filterQuestions('unanswered')">Non répondus</button>
  </div>
</div>

          <div class="row gy-4">

            <!-- Sujet de forum -->
            <div class="col-md-12">
              <div class="forum-card">
                <div class="forum-header d-flex align-items-center">
                  <img src="assets/img/skills.png" class="user-avatar" alt="Avatar utilisateur">
                  <div class="user-info ms-2">
                    <h5 class="user-name">Jean K.</h5>
                    <small class="text-muted">Posté le <time datetime="2025-05-29">29 Mai 2025</time></small>
                  </div>
                </div>
                <div class="forum-body mt-2">
                  <p><strong>Sujet :</strong> <a href="#" class="forum-title">Comment impliquer les jeunes dans la gouvernance locale ?</a></p>
                  <p class="forum-snippet">Bonjour à tous ! Quels sont selon vous les meilleurs moyens pour intégrer les jeunes dans les décisions de leur commune ?...</p>
                </div>
                <div class="forum-footer">
                  <span class="text-muted"><i class="bi bi-chat-left-text"></i> 8 réponses</span>
                  <button class="btn btn-light btn-sm"><i class="bi bi-reply"></i> Répondre</button>
                  <button class="btn btn-light btn-sm"><i class="bi bi-share"></i> Partager</button>
                  <button class="btn btn-light btn-sm"><i class="bi bi-bell"></i> Suivre</button>
                </div>
              </div>
            </div>
            <!-- Fin Sujet -->

            <!-- Tu peux dupliquer ce bloc pour chaque sujet -->

          </div>
        </div>

        <!-- Colonne droite : Filtrage -->
        <div class="col-lg-4">
          <div class="search-sidebar p-3 bg-light rounded shadow-sm">
            <h5>Filtrer les discussions</h5>
            <form id="forumFilterForm">
              <div class="mb-3">
                <label for="categoryFilter" class="form-label">Catégorie</label>
                <select id="categoryFilter" class="form-select">
                  <option value="">Toutes</option>
                  <option value="Jeunesse">Jeunesse</option>
                  <option value="Santé">Santé</option>
                  <option value="Éducation">Éducation</option>
                </select>
              </div>
              <div class="mb-3">
                <label for="dateFilter" class="form-label">Date</label>
                <input type="date" id="dateFilter" class="form-control">
              </div>
              <div class="mb-3">
                <label for="authorFilter" class="form-label">Auteur</label>
                <input type="text" id="authorFilter" class="form-control" placeholder="Nom de l’auteur">
              </div>
              <button type="submit" class="btn btn-success w-100">Appliquer</button>
            </form>
          </div>
        </div>

      </div>
    </div>
  </section>

  <!-- Pagination -->
  <section class="forum-pagination section">
    <div class="container">
      <div class="d-flex justify-content-center">
        <ul>
          <li><a href="#"><i class="bi bi-chevron-left"></i></a></li>
          <li><a href="#">1</a></li>
          <li><a href="#" class="active">2</a></li>
          <li><a href="#">3</a></li>
          <li><a href="#"><i class="bi bi-chevron-right"></i></a></li>
        </ul>
      </div>
    </div>
  </section>

</main>

<style>
.forum-card {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  font-size: 0.9rem;
}

.forum-header .user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-info .user-name {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.forum-title {
  font-weight: 600;
  color: #0a66c2;
  text-decoration: none;
}

.forum-title:hover {
  text-decoration: underline;
}

.forum-snippet {
  color: #444;
  font-size: 0.88rem;
  margin-top: 5px;
}

.forum-footer {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 10px;
  font-size: 0.85rem;
}

.forum-footer .btn {
  padding: 4px 10px;
  border: 1px solid #ddd;
}

.search-sidebar {
  background: #f9f9f9;
  max-height: 80vh;
  overflow-y: auto;
}

.search-sidebar h5 {
  margin-bottom: 15px;
}
</style>
</asp:Content>
