using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IBadges
    {
        // Méthodes CRUD de base
        int Add(Badges_Class badge);
        void AfficherDetails(long badgeId, Badges_Class badge);
        int Edit(Badges_Class badge, long badgeId);
        int Supprimer(long badgeId);
        void Chargement_GDV(GridView gdv);
        void Search(GridView gdv, string searchTerm);
        int Count();

        // Attribution automatique de badges (Stack Overflow style)
        void VerifierEtAttribuerBadges(long userId);
        void VerifierBadgesPourAction(long userId, string action, long? postId = null);
        void AttribuerBadge(long userId, string badgeName, long? postId = null);
        bool PossedeBadge(long userId, string badgeName);
        int CompterBadgesUtilisateur(long userId, int classe = 0);

        // Gestion des badges par classe
        void ChargerBadgesOr(ListView lv, long? userId = null);
        void ChargerBadgesArgent(ListView lv, long? userId = null);
        void ChargerBadgesBronze(ListView lv, long? userId = null);
        void ChargerTousBadges(ListView lv, long? userId = null);
        void ChargerBadgesUtilisateur(ListView lv, long userId);

        // Badges basés sur les tags
        void VerifierBadgesTags(long userId);
        void AttribuerBadgeTag(long userId, string tagName, int niveau); // Bronze, Silver, Gold
        List<Badges_Class> ObtenirBadgesTags(long userId);
        void ChargerProgressionBadgesTags(ListView lv, long userId);

        // Statistiques et classements
        void ChargerTopBadges(ListView lv, int limite = 20);
        void ChargerBadgesRares(ListView lv, int limite = 10);
        void ChargerBadgesRecents(ListView lv, int limite = 20);
        void ChargerStatistiquesBadges(out int total, out int or, out int argent, out int bronze);

        // Définitions et critères de badges
        void ChargerDefinitionsBadges(ListView lv);
        void AjouterDefinitionBadge(BadgeDefinitions_Class definition);
        void ModifierDefinitionBadge(BadgeDefinitions_Class definition);
        void SupprimerDefinitionBadge(int definitionId);
        BadgeDefinitions_Class ObtenirDefinitionBadge(string badgeName);

        // Progression et critères
        void ChargerProgressionUtilisateur(ListView lv, long userId);
        void CalculerProgressionBadge(long userId, string badgeName, out int actuel, out int requis);
        List<string> ObtenirBadgesProches(long userId, int limite = 5);
        void ChargerCriteresManquants(ListView lv, long userId, string badgeName);

        // Badges spéciaux Stack Overflow
        void VerifierBadgesPremiereFois(long userId, string action); // First Post, First Answer, etc.
        void VerifierBadgesActivite(long userId); // Enthusiast, Fanatic, etc.
        void VerifierBadgesQualite(long userId); // Nice Answer, Good Answer, Great Answer
        void VerifierBadgesModeration(long userId); // Civic Duty, Strunk & White, etc.
        void VerifierBadgesCommunaute(long userId); // Supporter, Teacher, etc.

        // Notifications et affichage
        void NotifierNouveauBadge(long userId, string badgeName);
        void ChargerBadgesNonLus(ListView lv, long userId);
        void MarquerBadgeCommeLu(long badgeId);
        void GenererAffichageBadges(Literal literal, long userId);

        // Administration et maintenance
        void RecalculerTousBadges();
        void RecalculerBadgesUtilisateur(long userId);
        void SupprimerBadgesInvalides();
        void MettreAJourCompteursBadges();
        void ChargerRapportBadges(DateTime dateDebut, DateTime dateFin);

        // Import/Export
        void ExporterBadgesUtilisateur(long userId);
        void ImporterDefinitionsBadges(List<BadgeDefinitions_Class> definitions);
        void SauvegarderConfigurationBadges();
    }
}
