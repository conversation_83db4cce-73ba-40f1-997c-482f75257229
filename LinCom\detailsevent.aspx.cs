﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class detailsevent : System.Web.UI.Page
    {
        private int info;
        Membre_Class membreClass = new Membre_Class();
        Membre_Class memb = new Membre_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objMembre = new MembreImp();
        IProvince objProvince = new ProvinceImp();
        ICommune objCommune = new CommuneImp();
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        IPoste obj = new PosteImp();
        Post_Class pos = new Post_Class();
        Post_Class post = new Post_Class();
        Post_Class p = new Post_Class();
        CommentairePoste_Class com = new CommentairePoste_Class();
        ICommentairePoste objcom = new CommentaireImp();

        DomainePost_Class actco = new DomainePost_Class();
        IDomainePost objactco = new DomainePostImp();

        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();

        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();

        CategoriePost_Class catpost = new CategoriePost_Class();
        ICategoryPost objcatpost = new CategoriePostImp();

        static string imge, imge1, pdfe, nameorg;
        static long ide, idorg;
        static int rolid;
        long index;

        static string slug;
        protected void Page_Load(object sender, EventArgs e)
        {

            if (!IsPostBack)
            {
                slug = Request.QueryString["name"];

                if (slug == null)
                {
                    Response.Redirect("~/evenement.aspx");
                }
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    ide = Convert.ToInt64(idperso.Value);//idconnecte

                }


                //if (slug != null)
                //{
                initial_msg();
                sharefacebook();
                //LoadString();

                //}
                //else Response.Redirect("~/news.aspx");



            }
        }
        public void sharefacebook()
        {
            obj.AfficherDetailsname(slug, -1, 1, post);//appel du post

            string titre = HttpUtility.HtmlEncode(post.Titre);
            string description = HttpUtility.HtmlEncode(post.Contenu);
            string image = ResolveUrl("~/file/post/" + post.photo);
            string fullImageUrl = $"{Request.Url.Scheme}://{Request.Url.Authority}{image}";
            string url = Request.Url.AbsoluteUri;

            LiteralControl ogTags = new LiteralControl($@"
    <meta property='og:title' content='{titre}' />
    <meta property='og:description' content='{description}' />
    <meta property='og:image' content='{fullImageUrl}' />
    <meta property='og:url' content='{url}' />
    <meta property='og:type' content='article' />
    <meta name='twitter:card' content='summary_large_image' />
    <meta name='twitter:title' content='{titre}' />
    <meta name='twitter:description' content='{description}' />
    <meta name='twitter:image' content='{fullImageUrl}' />
");

            Page.Header.Controls.Add(ogTags);

        }
        void commentaire()
        {

            try
            {
                if (txtmessage.Value == "")
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Veuillez renseigner  commentaire.!!!')</script>");

                }
                else
                {


                    obj.AfficherDetailsname(slug, -1, 1, p);//appel du post

                    com.PostId = Convert.ToInt64(p.PostId);
                    com.MembreId = ide;
                    com.Contenu = txtmessage.Value;
                    com.DateCommentaire = DateTime.Now;
                    com.EstVisible = "vu";
                    com.Nbrevue = 0;
                    com.name = com.CommentPostId.ToString();


                    info = objcom.Ajout(com);

                    if (info > 0)
                    {
                        objcom.ChargementListview(listcomment, p.PostId, -1, -1, "vu", 1);
                        txtcomment.InnerText = objcom.count(-1, Convert.ToInt64(p.PostId), 0) + " Commentaire(s)";
                        txtcommentpost.InnerText = objcom.count(-1, Convert.ToInt64(p.PostId), 0) + " Commentaire(s)";

                        txtmessage.Value = "";
                        Response.Write("<script LANGUAGE=JavaScript>alert('Votre commentaire a été bien envoyé')</script>");

                    }
                    else
                    {
                        Response.Write("<script LANGUAGE=JavaScript>alert('Problème Technique.!!!')</script>");

                    }

                }
            }
            catch (Exception e)
            {

            }
        }
        //protected void OnPagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        //{
        //    (listblog.FindControl("DataPager1") as DataPager).SetPageProperties(e.StartRowIndex, e.MaximumRows, false);
        //    obj.Chargement_GDV(listblog, 0, "blog", 7);
        //}
        protected void listpost_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            string index = e.CommandArgument.ToString();
            if (e.CommandName == "view")
            {

                obj.AfficherDetailsname(index, -1, 1, pos);
                post.number_of_view = pos.number_of_view + 1;
                obj.MiseajourData(post, pos.PostId, -1, 0);

                Response.Redirect("~/detailsevent.aspx?name=" + index);



            }
        }

        void initial_msg()
        {

            //blog
            obj.Chargement_GDV(listpost, -1, -1, "evenement", "publié", 1);
            //blog
            //  obj.Chargement_GDV(listblog, 0, "blog", 3);
            //event
            // obj.Chargement_GDV(listevent, 0, "blog", 4);
            if (!string.IsNullOrEmpty(slug))
            {

                // obj.afficherDetails(Convert.ToInt32(nscno), "blog", pc);
                obj.AfficherDetailsname(slug, -1, 1, p);//appel du post


                // objcom.Chargement_GDV(listcomment, 0, "", Convert.ToInt64(nscno), 0);
                // obj.Chargement_GDV(listv1, (int)pc.IDCAT, 1);
                txttitle.InnerText = p.Titre;
                //   txttitle1.InnerText = pc.titre;
                txttitle2.InnerText = p.Titre;
                txttitle3.InnerText = p.Titre;
                imgblog.Src = "~/file/post/" + p.photo;
                imgblog1.Src = "~/file/post/" + p.video;
                txtdescription.InnerHtml = p.Contenu;
                // img1.Src = "~/files/blog/" + pc.photo;
                // resumepost.InnerHtml = pc.description;
                txtdate.InnerText = Convert.ToDateTime(p.DatePublication).ToString("dd/MM/yyyy");
                txtauthororganisation.InnerText = p.author;
                txtresume.InnerHtml = p.summery;
                txtlieu.InnerHtml = p.eventplace;
                txttemps.InnerHtml = p.eventduration;
                txtlangueevent.InnerHtml = p.langueevent;
                txtexternevent.InnerHtml = p.externevent;
                txtparticipant.InnerHtml = p.whoattend;

                txtview.InnerText = p.number_of_view + " vu(s)";
                txtcomment.InnerText = objcom.count(-1, Convert.ToInt64(p.PostId), 0) + " Commentaire(s)";
                txtcommentpost.InnerText = objcom.count(-1, Convert.ToInt64(p.PostId), 0) + " Commentaire(s)";
                // sectblogdetail.Attributes.Add("style", "background-image(~/file/site/)"+ pc.video);
                // sectblogdetail.Style["background-image"] = "~/file/site/"+p.video;
                //sectblogdetail.Style["background-image"] = "url(../file/post/"+p.photo+")";

                sectblogdetail.Style["background-image"] = Page.ResolveUrl("~/file/post/" + p.photo);

                objOrganisation.AfficherDetails(Convert.ToInt64(p.OrganisationId), org);
                btninscription.Attributes["href"] = p.lien_isncription;
                btnpdf.Attributes["href"] = "../file/post/" + post.pdf;



                // nbrecomment.InnerText = objcom.count("", Convert.ToInt32(nscno), 0) + " Commentaire(s)";

                objactdom.ChargerDomaines(listcategorie);
                objactco.ChargerListView(listdompost, p.PostId, -1, 1, "actif");
                objcom.ChargementListview(listcomment, p.PostId, -1, -1, "vu", 1);
                // objtabl.Chargement_GDV(gdvtag, Convert.ToInt64(nscno), 0);

            }
        }
        protected void btn_srch_Click(object sender, EventArgs e)
        {
            //if (txt_srch.Value == "")
            //    obj.Chargement_GDV(listv, 0, "blog", 5);
            //else obj.searchlist(listv, "blog", 0, 0, txt_srch.Value);
        }

        protected void voirtouract_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/news.aspx");
        }
        protected void voirtouractblog_ServerClick(object sender, EventArgs e)
        {
            Response.Redirect("~/newsupcome.aspx");
        }
        protected void bntcomment_Click(object sender, EventArgs e)
        {
            commentaire();
            //nbrecomment.InnerText = objcom.count("", Convert.ToInt32(nscno), 0) + " Commentaire(s)";

        }


        protected void listcategorie_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            //int index = Convert.ToInt32(e.CommandArgument);
            //if (e.CommandName == "viewcat")
            //{
            //    princip.Visible = false;
            //    second.Visible = true;

            //    obj.Chargement_GDV(listblog, index, "blog", 1);

            //}
        }

        protected void btnenreg_ServerClick(object sender, EventArgs e)
        {
            if (slug != null && ide > 0)
            {
                commentaire();
            }
            else Response.Redirect("~/login.aspx");
        }
    }
}