﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class ForumImp : IForum
    {
        private Forum forum = new Forum();

        public void AfficherDetails(long forumId, Forum_Class forumClass)
        {
            using (var context = new Connection())
            {
                var f = context.Forums.FirstOrDefault(x => x.ForumId == forumId);
                if (f != null)
                {
                    forumClass.ForumId = f.ForumId;
                    forumClass.Nom = f.Nom;
                    forumClass.name = f.name;
                }
            }
        }

        public int Ajouter(Forum_Class forumClass)
        {
            using (var context = new Connection())
            {
                forum.ForumId = forumClass.ForumId;
                forum.Nom = forumClass.Nom;
                forum.name = forumClass.name;

                try
                {
                    context.Forums.Add(forum);
                    return context.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public void ChargerGridView(GridView gdv, string name = "")
        {
            using (var context = new Connection())
            {
                var query = from f in context.Forums
                            select new
                            {
                                f.ForumId,
                                f.Nom,
                                f.name
                            };

                if (!string.IsNullOrEmpty(name))
                {
                    query = query.Where(x => x.name == name);
                }

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        public void ChargerSujetsRecents(Repeater rpt, long forumId)
        {
            using (var context = new Connection())
            {
                var sujets = (from s in context.SujetForums
                              where s.ForumId == forumId
                              orderby s.DateCreation descending
                              select new
                              {
                                  s.SujetForumId,
                                  s.Titre,
                                  s.DateCreation,
                                  s.name,
                                  NombreReponses = context.RepliesForums.Count(r => r.SujetForumId == s.SujetForumId)
                              }).Take(5).ToList();

                rpt.DataSource = sujets;
                rpt.DataBind();
            }
        }

        public int CompterParticipants(long forumId)
        {
            using (var context = new Connection())
            {
                // Récupérer les sujets du forum
                var sujets = context.SujetForums.Where(s => s.ForumId == forumId).Select(s => s.SujetForumId).ToList();

                if (sujets.Any())
                {
                    // Compter les membres distincts qui ont répondu aux sujets de ce forum
                    return context.RepliesForums
                        .Where(r => sujets.Contains(r.SujetForumId))
                        .Select(r => r.MembreId)
                        .Distinct()
                        .Count();
                }

                return 0;
            }
        }

        public int Modifier(Forum_Class forumClass)
        {
            using (var context = new Connection())
            {
                var f = context.Forums.FirstOrDefault(x => x.ForumId == forumClass.ForumId);
                if (f != null)
                {
                    f.Nom = forumClass.Nom;
                    f.name = forumClass.name;

                    try
                    {
                        return context.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int Supprimer(long forumId)
        {
            using (var context = new Connection())
            {
                var f = context.Forums.FirstOrDefault(x => x.ForumId == forumId);
                if (f != null)
                {
                    context.Forums.Remove(f);
                    try
                    {
                        return context.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int ChangerStatut(long forumId, string nouveauStatut)
        {
            using (var context = new Connection())
            {
                var f = context.Forums.FirstOrDefault(x => x.ForumId == forumId);
                if (f != null)
                {
                    f.name = nouveauStatut;
                    try
                    {
                        return context.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public void ChargerStatistiques(Repeater rpt, long forumId)
        {
            using (var context = new Connection())
            {
                var forum = context.Forums.FirstOrDefault(f => f.ForumId == forumId);
                if (forum != null)
                {
                    // Récupérer les sujets du forum
                    var sujets = context.SujetForums.Where(s => s.ForumId == forumId).ToList();
                    var sujetIds = sujets.Select(s => s.SujetForumId).ToList();

                    // Calculer les statistiques
                    var stats = new
                    {
                        ForumId = forum.ForumId,
                        Nom = forum.Nom,
                        NombreSujets = sujets.Count,
                        NombreReplies = context.RepliesForums.Count(r => sujetIds.Contains(r.SujetForumId)),
                        NombreParticipants = CompterParticipants(forumId),
                        DernierSujet = sujets.OrderByDescending(s => s.DateCreation).FirstOrDefault()?.Titre ?? "Aucun sujet",
                        DateDernierSujet = sujets.OrderByDescending(s => s.DateCreation).FirstOrDefault()?.DateCreation,
                        SujetLePlusActif = (from s in sujets
                                            let nbReplies = context.RepliesForums.Count(r => r.SujetForumId == s.SujetForumId)
                                            orderby nbReplies descending
                                            select new { s.Titre, NbReplies = nbReplies }).FirstOrDefault()?.Titre ?? "Aucun sujet"
                    };

                    rpt.DataSource = new[] { stats };
                    rpt.DataBind();
                }
            }
        }

        public void ChargerForumsPopulaires(Repeater rpt, int nombreForums = 5)
        {
            using (var context = new Connection())
            {
                var forums = from f in context.Forums
                             select new
                             {
                                 f.ForumId,
                                 f.Nom,
                                 NombreSujets = context.SujetForums.Count(s => s.ForumId == f.ForumId),
                                 NombreReplies = (from s in context.SujetForums
                                                  where s.ForumId == f.ForumId
                                                  join r in context.RepliesForums on s.SujetForumId equals r.SujetForumId
                                                  select r).Count(),
                                 DernierSujet = (from s in context.SujetForums
                                                 where s.ForumId == f.ForumId
                                                 orderby s.DateCreation descending
                                                 select s.Titre).FirstOrDefault() ?? "Aucun sujet",
                                 DateDernierSujet = (from s in context.SujetForums
                                                     where s.ForumId == f.ForumId
                                                     orderby s.DateCreation descending
                                                     select s.DateCreation).FirstOrDefault()
                             };

                rpt.DataSource = forums.OrderByDescending(f => f.NombreReplies).Take(nombreForums).ToList();
                rpt.DataBind();
            }
        }
    }
}