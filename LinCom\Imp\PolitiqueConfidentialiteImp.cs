﻿using LinCom.Class;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace LinCom.Imp
{
    public class PolitiqueConfidentialiteImp : IPolitiqueConfidentialite
    {

        int msg, id;
        PolitiqueConfidentialite condition = new PolitiqueConfidentialite();

        public void AfficherDetails(int idCondition, PolitiqueConfidentialite_Class conditionClass)
        {
            using (Connection con = new Connection())
            {
                var c = con.PolitiqueConfidentialites.FirstOrDefault(x => x.PolitiqueId == idCondition);
                if (c != null)
                {
                    conditionClass.PolitiqueId = c.PolitiqueId;
                    conditionClass.Contenu = c.Contenu;
                    conditionClass.name = c.name;
                    conditionClass.DatePublication = c.DatePublication;

                }
            }
        }
        public void AfficherDetails(string code, PolitiqueConfidentialite_Class conditionClass)
        {
            using (Connection con = new Connection())
            {
                var c = con.PolitiqueConfidentialites.FirstOrDefault(x => x.name == code);
                if (c != null)
                {
                    conditionClass.PolitiqueId = c.PolitiqueId;
                    conditionClass.Contenu = c.Contenu;
                    conditionClass.name = c.name;
                    conditionClass.DatePublication = c.DatePublication;

                }
            }
        }

        public int Ajouter(PolitiqueConfidentialite_Class conditionClass)
        {
            using (Connection con = new Connection())
            {

                condition.Contenu = conditionClass.Contenu;
                condition.DatePublication = DateTime.Now;
                condition.name = conditionClass.name;


                try
                {
                    con.PolitiqueConfidentialites.Add(condition);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }
        public int ID()
        {
            using (Connection con = new Connection())
            {
                PolitiqueConfidentialite tp = new PolitiqueConfidentialite();

                tp = con.PolitiqueConfidentialites.OrderByDescending(x => x.PolitiqueId).FirstOrDefault();
                if (tp != null)
                    id = Convert.ToInt32(tp.PolitiqueId);
                else id = -1;
            }
            return id;
        }
        public int countID()
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                var b = (from l in con.PolitiqueConfidentialites
                         select l).Count();
                n = b;
            }
            return n;
        }
        public int Modifier(PolitiqueConfidentialite_Class conditionClass, int idCondition)
        {
            using (Connection con = new Connection())
            {
                try
                {
                    // Recherche de la condition d'utilisation à modifier
                    var conditionToUpdate = con.PolitiqueConfidentialites.FirstOrDefault(x => x.PolitiqueId == idCondition);

                    if (conditionToUpdate != null)
                    {
                        // Mise à jour des propriétés
                        conditionToUpdate.Contenu = conditionClass.Contenu;


                        // Sauvegarde des modifications
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                        {
                            return msg = 0;
                        }
                    }
                    else
                    {
                        // La condition d'utilisation n'a pas été trouvée
                        return msg = 0;
                    }
                }
                catch
                {
                    return msg = 0;
                }
            }
        }
    }

}