<%@ Page Title="Poser une Question - Forum LinCom" Language="C#" MasterPageFile="~/MasterPage.Master" AutoEventWireup="true" CodeBehind="poser-question.aspx.cs" Inherits="LinCom.poser_question" ValidateRequest="true" ViewStateEncryptionMode="Always" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="Content/forum-lincom.css" rel="stylesheet" />
    <link href="Content/tagsinput.css" rel="stylesheet" />
    <style>
        .ask-header {
            background: var(--lincom-gradient-primary);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
            border-bottom: 4px solid var(--lincom-accent);
            position: relative;
            overflow: hidden;
        }

        .ask-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .form-section {
            background: var(--lincom-white);
            border: 1px solid var(--lincom-border);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 4px solid var(--lincom-secondary);
            box-shadow: var(--lincom-shadow);
        }
        
        .form-section h4 {
            color: #24292e;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .form-section .help-text {
            color: #586069;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .form-control {
            border: 1px solid #d1d5da;
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 1rem;
        }
        
        .form-control:focus {
            border-color: #0366d6;
            box-shadow: 0 0 0 3px rgba(3, 102, 214, 0.1);
        }
        
        .editor-container {
            border: 1px solid #d1d5da;
            border-radius: 6px;
            background: white;
        }
        
        .editor-toolbar {
            display: flex;
            gap: 0.5rem;
            padding: 0.75rem;
            background: #f6f8fa;
            border-bottom: 1px solid #d1d5da;
            border-radius: 6px 6px 0 0;
        }
        
        .editor-btn {
            background: none;
            border: 1px solid #d1d5da;
            padding: 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            color: #586069;
            transition: all 0.2s ease;
        }
        
        .editor-btn:hover {
            background: #e1e4e8;
            color: #24292e;
        }
        
        .editor-btn.active {
            background: #0366d6;
            color: white;
            border-color: #0366d6;
        }
        
        .editor-content {
            min-height: 300px;
            padding: 1rem;
            border: none;
            border-radius: 0 0 6px 6px;
            resize: vertical;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.5;
        }
        
        .editor-content:focus {
            outline: none;
        }
        
        .preview-container {
            min-height: 300px;
            padding: 1rem;
            border-radius: 0 0 6px 6px;
            background: #f8f9fa;
            display: none;
        }
        
        .tags-input-container {
            position: relative;
        }
        
        .tags-input {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            padding: 0.5rem;
            border: 1px solid #d1d5da;
            border-radius: 6px;
            min-height: 50px;
            cursor: text;
        }
        
        .tag-item {
            background: #e1ecf4;
            color: #39739d;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .tag-remove {
            background: none;
            border: none;
            color: #39739d;
            cursor: pointer;
            font-size: 0.8rem;
            padding: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .tag-remove:hover {
            background: #39739d;
            color: white;
        }
        
        .tag-input {
            border: none;
            outline: none;
            flex: 1;
            min-width: 100px;
            padding: 0.25rem;
        }
        
        .tag-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5da;
            border-top: none;
            border-radius: 0 0 6px 6px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        
        .tag-suggestion {
            padding: 0.5rem;
            cursor: pointer;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .tag-suggestion:hover {
            background: #f6f8fa;
        }
        
        .tag-suggestion.selected {
            background: #0366d6;
            color: white;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .category-option {
            border: 1px solid #d1d5da;
            border-radius: 6px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }
        
        .category-option:hover {
            border-color: #0366d6;
            background: #f6f8fa;
        }
        
        .category-option.selected {
            border-color: #0366d6;
            background: #e6f3ff;
        }
        
        .category-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .category-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .category-description {
            font-size: 0.9rem;
            color: #586069;
        }
        
        .submit-section {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }
        
        .submit-btn {
            background: var(--lincom-gradient-success);
            color: white;
            border: none;
            padding: 1rem 2.5rem;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 6px rgba(39, 174, 96, 0.3);
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #229954 0%, #1e8449 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(39, 174, 96, 0.4);
        }
        
        .submit-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .draft-save {
            color: #586069;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .tips-sidebar {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .tips-title {
            font-weight: 600;
            color: #856404;
            margin-bottom: 0.5rem;
        }
        
        .tips-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .tips-list li {
            color: #856404;
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
        }
        
        .tips-list li:before {
            content: "💡 ";
            margin-right: 0.25rem;
        }
        
        .character-count {
            text-align: right;
            font-size: 0.8rem;
            color: #586069;
            margin-top: 0.25rem;
        }
        
        .character-count.warning {
            color: #e36209;
        }
        
        .character-count.error {
            color: #d73a49;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Header -->
    <div class="ask-header">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h1><i class="fas fa-question-circle"></i> Poser une Question</h1>
                    <p class="lead">Obtenez de l'aide de la communauté LinCom</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="forum-questions.aspx" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left"></i> Retour aux Questions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- Formulaire Principal -->
            <div class="col-md-8">
                <form id="questionForm" runat="server">
                    <!-- Titre de la Question -->
                    <div class="form-section">
                        <h4>Titre de votre question</h4>
                        <p class="help-text">
                            Soyez précis et décrivez votre problème en une phrase claire.
                        </p>
                        <asp:TextBox ID="txtTitle" runat="server" CssClass="form-control" 
                            placeholder="ex: Comment implémenter un système de votes en C# ?" 
                            MaxLength="250"></asp:TextBox>
                        <div class="character-count">
                            <span id="titleCount">0</span>/250 caractères
                        </div>
                        <asp:RequiredFieldValidator ID="rfvTitle" runat="server" 
                            ControlToValidate="txtTitle" 
                            ErrorMessage="Le titre est obligatoire" 
                            CssClass="text-danger" Display="Dynamic" />
                    </div>

                    <!-- Catégorie -->
                    <div class="form-section">
                        <h4>Catégorie</h4>
                        <p class="help-text">
                            Choisissez la catégorie qui correspond le mieux à votre question.
                        </p>
                        <div class="category-grid">
                            <asp:ListView ID="lvCategories" runat="server" OnItemCommand="lvCategories_ItemCommand">
                                <LayoutTemplate>
                                    <div id="itemPlaceholder" runat="server"></div>
                                </LayoutTemplate>
                                <ItemTemplate>
                                    <div class="category-option" data-category-id="<%# Eval("CategoryId") %>">
                                        <div class="category-icon" style="color: <%# Eval("Color") %>">
                                            <i class="<%# Eval("IconClass") %>"></i>
                                        </div>
                                        <div class="category-name"><%# Eval("Name") %></div>
                                        <div class="category-description"><%# Eval("Description") %></div>
                                    </div>
                                </ItemTemplate>
                            </asp:ListView>
                        </div>
                        <asp:HiddenField ID="hfSelectedCategory" runat="server" />
                        <asp:RequiredFieldValidator ID="rfvCategory" runat="server" 
                            ControlToValidate="hfSelectedCategory" 
                            ErrorMessage="Veuillez sélectionner une catégorie" 
                            CssClass="text-danger" Display="Dynamic" />
                    </div>

                    <!-- Contenu de la Question -->
                    <div class="form-section">
                        <h4>Détails de votre question</h4>
                        <p class="help-text">
                            Expliquez votre problème en détail. Incluez le code, les messages d'erreur, et ce que vous avez déjà essayé.
                        </p>
                        
                        <div class="editor-container">
                            <div class="editor-toolbar">
                                <button type="button" class="editor-btn" data-action="bold" title="Gras">
                                    <i class="fas fa-bold"></i>
                                </button>
                                <button type="button" class="editor-btn" data-action="italic" title="Italique">
                                    <i class="fas fa-italic"></i>
                                </button>
                                <button type="button" class="editor-btn" data-action="code" title="Code">
                                    <i class="fas fa-code"></i>
                                </button>
                                <button type="button" class="editor-btn" data-action="link" title="Lien">
                                    <i class="fas fa-link"></i>
                                </button>
                                <button type="button" class="editor-btn" data-action="image" title="Image">
                                    <i class="fas fa-image"></i>
                                </button>
                                <button type="button" class="editor-btn" data-action="list" title="Liste">
                                    <i class="fas fa-list"></i>
                                </button>
                                <div style="margin-left: auto;">
                                    <button type="button" class="editor-btn" id="btnPreview" title="Aperçu">
                                        <i class="fas fa-eye"></i> Aperçu
                                    </button>
                                </div>
                            </div>
                            
                            <asp:TextBox ID="txtContent" runat="server" CssClass="editor-content" 
                                TextMode="MultiLine" 
                                placeholder="Décrivez votre problème ici...&#10;&#10;Incluez :&#10;- Le code que vous avez essayé&#10;- Les messages d'erreur&#10;- Ce que vous attendez comme résultat"></asp:TextBox>
                            
                            <div id="previewContainer" class="preview-container"></div>
                        </div>
                        
                        <div class="character-count">
                            <span id="contentCount">0</span> caractères (minimum 30)
                        </div>
                        
                        <asp:RequiredFieldValidator ID="rfvContent" runat="server" 
                            ControlToValidate="txtContent" 
                            ErrorMessage="Le contenu de la question est obligatoire" 
                            CssClass="text-danger" Display="Dynamic" />
                    </div>

                    <!-- Tags -->
                    <div class="form-section">
                        <h4>Tags</h4>
                        <p class="help-text">
                            Ajoutez jusqu'à 5 tags pour aider les autres à trouver votre question.
                        </p>
                        
                        <div class="tags-input-container">
                            <div class="tags-input" id="tagsInput">
                                <input type="text" class="tag-input" placeholder="Tapez un tag et appuyez sur Entrée" />
                            </div>
                            <div class="tag-suggestions" id="tagSuggestions"></div>
                        </div>
                        
                        <asp:HiddenField ID="hfTags" runat="server" />
                        <small class="text-muted">
                            Exemples : c#, asp.net, javascript, sql, html, css
                        </small>
                    </div>

                    <!-- Bouton de Soumission -->
                    <div class="submit-section">
                        <asp:Button ID="btnSubmit" runat="server" Text="Publier votre question" 
                            CssClass="submit-btn" OnClick="btnSubmit_Click" />
                        
                        <div class="draft-save">
                            <i class="fas fa-save"></i> Votre brouillon est automatiquement sauvegardé
                        </div>
                    </div>
                </form>
            </div>

            <!-- Sidebar Conseils -->
            <div class="col-md-4">
                <div class="tips-sidebar">
                    <div class="tips-title">
                        <i class="fas fa-lightbulb"></i> Conseils pour une bonne question
                    </div>
                    <ul class="tips-list">
                        <li>Utilisez un titre clair et descriptif</li>
                        <li>Expliquez le contexte de votre problème</li>
                        <li>Incluez le code minimal qui reproduit le problème</li>
                        <li>Mentionnez ce que vous avez déjà essayé</li>
                        <li>Utilisez des tags pertinents</li>
                        <li>Relisez votre question avant de la publier</li>
                    </ul>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-question-circle"></i> Questions Similaires</h6>
                    </div>
                    <div class="card-body">
                        <div id="similarQuestions">
                            <p class="text-muted">Tapez votre titre pour voir les questions similaires...</p>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-markdown"></i> Aide Markdown</h6>
                    </div>
                    <div class="card-body">
                        <small>
                            <strong>**gras**</strong><br>
                            <em>*italique*</em><br>
                            <code>`code`</code><br>
                            [lien](url)<br>
                            ![image](url)<br>
                            - liste<br>
                            > citation
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="Content/tagsinput.js"></script>
    <script>
        // Gestion des catégories
        document.querySelectorAll('.category-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.category-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
                document.getElementById('<%=hfSelectedCategory.ClientID%>').value = this.dataset.categoryId;
            });
        });

        // Gestion de l'éditeur
        const editor = document.getElementById('<%=txtContent.ClientID%>');
        const previewBtn = document.getElementById('btnPreview');
        const previewContainer = document.getElementById('previewContainer');
        let isPreviewMode = false;

        // Boutons de l'éditeur
        document.querySelectorAll('.editor-btn[data-action]').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.dataset.action;
                insertMarkdown(action);
            });
        });

        // Aperçu
        previewBtn.addEventListener('click', function() {
            isPreviewMode = !isPreviewMode;
            if (isPreviewMode) {
                editor.style.display = 'none';
                previewContainer.style.display = 'block';
                previewContainer.innerHTML = convertMarkdownToHtml(editor.value);
                this.innerHTML = '<i class="fas fa-edit"></i> Éditer';
                this.classList.add('active');
            } else {
                editor.style.display = 'block';
                previewContainer.style.display = 'none';
                this.innerHTML = '<i class="fas fa-eye"></i> Aperçu';
                this.classList.remove('active');
            }
        });

        // Compteurs de caractères
        const titleInput = document.getElementById('<%=txtTitle.ClientID%>');
        const titleCount = document.getElementById('titleCount');
        const contentCount = document.getElementById('contentCount');

        titleInput.addEventListener('input', function() {
            titleCount.textContent = this.value.length;
            titleCount.parentElement.className = this.value.length > 200 ? 'character-count warning' : 'character-count';
            
            // Rechercher des questions similaires
            if (this.value.length > 10) {
                searchSimilarQuestions(this.value);
            }
        });

        editor.addEventListener('input', function() {
            contentCount.textContent = this.value.length;
            contentCount.parentElement.className = this.value.length < 30 ? 'character-count error' : 'character-count';
            
            // Auto-save
            localStorage.setItem('question_draft', JSON.stringify({
                title: titleInput.value,
                content: this.value,
                category: document.getElementById('<%=hfSelectedCategory.ClientID%>').value,
                tags: document.getElementById('<%=hfTags.ClientID%>').value
            }));
        });

        // Gestion des tags
        initializeTagsInput();

        // Restaurer le brouillon
        window.addEventListener('load', function() {
            const draft = localStorage.getItem('question_draft');
            if (draft) {
                try {
                    const data = JSON.parse(draft);
                    if (titleInput.value === '') titleInput.value = data.title || '';
                    if (editor.value === '') editor.value = data.content || '';
                    if (data.category) {
                        document.getElementById('<%=hfSelectedCategory.ClientID%>').value = data.category;
                        document.querySelector(`[data-category-id="${data.category}"]`)?.classList.add('selected');
                    }
                    if (data.tags) {
                        document.getElementById('<%=hfTags.ClientID%>').value = data.tags;
                        // Restaurer les tags visuellement
                    }
                } catch (e) {
                    console.error('Erreur lors de la restauration du brouillon:', e);
                }
            }
        });

        function insertMarkdown(action) {
            const start = editor.selectionStart;
            const end = editor.selectionEnd;
            const selectedText = editor.value.substring(start, end);
            let replacement = '';

            switch (action) {
                case 'bold':
                    replacement = `**${selectedText}**`;
                    break;
                case 'italic':
                    replacement = `*${selectedText}*`;
                    break;
                case 'code':
                    replacement = selectedText.includes('\n') ? `\`\`\`\n${selectedText}\n\`\`\`` : `\`${selectedText}\``;
                    break;
                case 'link':
                    replacement = `[${selectedText || 'texte du lien'}](url)`;
                    break;
                case 'image':
                    replacement = `![${selectedText || 'alt text'}](url)`;
                    break;
                case 'list':
                    replacement = selectedText.split('\n').map(line => `- ${line}`).join('\n');
                    break;
            }

            editor.value = editor.value.substring(0, start) + replacement + editor.value.substring(end);
            editor.focus();
            editor.setSelectionRange(start + replacement.length, start + replacement.length);
        }

        function convertMarkdownToHtml(markdown) {
            // Conversion basique Markdown vers HTML
            return markdown
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/`(.*?)`/g, '<code>$1</code>')
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
                .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1">')
                .replace(/^- (.+)$/gm, '<li>$1</li>')
                .replace(/\n/g, '<br>');
        }

        function initializeTagsInput() {
            const tagsContainer = document.getElementById('tagsInput');
            const tagInput = tagsContainer.querySelector('.tag-input');
            const suggestionsContainer = document.getElementById('tagSuggestions');
            const hiddenField = document.getElementById('<%=hfTags.ClientID%>');
            let tags = [];

            tagInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ',') {
                    e.preventDefault();
                    addTag(this.value.trim());
                    this.value = '';
                } else if (e.key === 'Backspace' && this.value === '' && tags.length > 0) {
                    removeTag(tags.length - 1);
                }
            });

            tagInput.addEventListener('input', function() {
                if (this.value.length > 1) {
                    searchTags(this.value);
                } else {
                    hideSuggestions();
                }
            });

            function addTag(tagName) {
                if (tagName && tags.length < 5 && !tags.includes(tagName)) {
                    tags.push(tagName);
                    renderTags();
                    updateHiddenField();
                }
            }

            function removeTag(index) {
                tags.splice(index, 1);
                renderTags();
                updateHiddenField();
            }

            function renderTags() {
                const existingTags = tagsContainer.querySelectorAll('.tag-item');
                existingTags.forEach(tag => tag.remove());

                tags.forEach((tag, index) => {
                    const tagElement = document.createElement('div');
                    tagElement.className = 'tag-item';
                    tagElement.innerHTML = `
                        ${tag}
                        <button type="button" class="tag-remove" onclick="removeTag(${index})">×</button>
                    `;
                    tagsContainer.insertBefore(tagElement, tagInput);
                });
            }

            function updateHiddenField() {
                hiddenField.value = tags.join(',');
            }

            function searchTags(query) {
                // Simulation de recherche de tags (à remplacer par un appel AJAX)
                const suggestions = ['c#', 'asp.net', 'javascript', 'sql', 'html', 'css', 'python', 'java']
                    .filter(tag => tag.toLowerCase().includes(query.toLowerCase()) && !tags.includes(tag));

                if (suggestions.length > 0) {
                    showSuggestions(suggestions);
                } else {
                    hideSuggestions();
                }
            }

            function showSuggestions(suggestions) {
                suggestionsContainer.innerHTML = suggestions
                    .map(tag => `<div class="tag-suggestion" onclick="selectSuggestion('${tag}')">${tag}</div>`)
                    .join('');
                suggestionsContainer.style.display = 'block';
            }

            function hideSuggestions() {
                suggestionsContainer.style.display = 'none';
            }

            window.selectSuggestion = function(tag) {
                addTag(tag);
                tagInput.value = '';
                hideSuggestions();
                tagInput.focus();
            };

            window.removeTag = removeTag;
        }

        function searchSimilarQuestions(title) {
            // Simulation de recherche (à remplacer par un appel AJAX)
            const similarContainer = document.getElementById('similarQuestions');
            similarContainer.innerHTML = '<p class="text-muted">Recherche en cours...</p>';
            
            // Simuler un délai de recherche
            setTimeout(() => {
                similarContainer.innerHTML = `
                    <div class="mb-2">
                        <a href="#" class="text-primary">Question similaire 1</a>
                        <small class="text-muted d-block">2 réponses</small>
                    </div>
                    <div class="mb-2">
                        <a href="#" class="text-primary">Question similaire 2</a>
                        <small class="text-muted d-block">5 réponses</small>
                    </div>
                `;
            }, 500);
        }
    </script>
</asp:Content>
