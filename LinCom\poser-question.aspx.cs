using LinCom.Classe;
using LinCom.Imp;
using LinCom.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Text.RegularExpressions;

namespace LinCom
{
    public partial class poser_question : System.Web.UI.Page
    {
        private SujetForumImp sujetImp = new SujetForumImp();
        private CategoriesImp categoriesImp = new CategoriesImp();
        private TagsImp tagsImp = new TagsImp();
        private BadgesImp badgesImp = new BadgesImp();
        private ReputationHistoryImp reputationImp = new ReputationHistoryImp();

        private long? CurrentUserId;

        protected void Page_Load(object sender, EventArgs e)
        {
            // Vérifier si l'utilisateur est connecté
            if (Session["MembreId"] == null)
            {
                Response.Redirect("login.aspx?returnUrl=" + HttpUtility.UrlEncode(Request.Url.ToString()));
                return;
            }

            CurrentUserId = Convert.ToInt64(Session["MembreId"]);

            if (!IsPostBack)
            {
                LoadCategories();
                CheckUserPermissions();
            }
        }

        private void LoadCategories()
        {
            try
            {
                categoriesImp.ChargerCategoriesActives(lvCategories);
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des catégories : " + ex.Message);
            }
        }

        private void CheckUserPermissions()
        {
            // Vérifier si l'utilisateur a la réputation suffisante pour poser des questions
            // (Dans Stack Overflow, il faut généralement 1 point de réputation minimum)
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var membre = con.Membres.Where(m => m.MembreId == CurrentUserId.Value).FirstOrDefault();
                    if (membre != null && membre.Reputation < 1)
                    {
                        ShowWarning("Vous avez besoin d'au moins 1 point de réputation pour poser une question. Participez d'abord à la communauté en répondant aux questions existantes.");
                    }
                }
            }
            catch (Exception ex)
            {
                // Erreur non critique, on continue
            }
        }

        protected void lvCategories_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            // Géré côté client JavaScript
        }

        protected void btnSubmit_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid)
                return;

            if (!CurrentUserId.HasValue)
            {
                ShowError("Vous devez être connecté pour poser une question.");
                return;
            }

            try
            {
                // Validation côté serveur
                if (!ValidateQuestion())
                    return;

                // Créer la question
                var question = new SujetForum_Class
                {
                    Title = txtTitle.Text.Trim(),
                    Body = txtContent.Text.Trim(),
                    Tags = hfTags.Value,
                    CategoryId = string.IsNullOrEmpty(hfSelectedCategory.Value) ? (long?)null : Convert.ToInt64(hfSelectedCategory.Value),
                    OwnerUserId = CurrentUserId.Value,
                    OwnerDisplayName = Session["NomComplet"]?.ToString() ?? "Utilisateur",
                    Difficulty = DetermineQuestionDifficulty(),
                    Language = "fr",
                    Priority = 1
                };

                int questionId = sujetImp.CreerQuestion(question);

                if (questionId > 0)
                {
                    // Traiter les tags
                    ProcessQuestionTags(questionId, hfTags.Value);

                    // Attribuer des badges pour la première question
                    badgesImp.VerifierBadgesPourAction(CurrentUserId.Value, "FirstPost", questionId);

                    // Ajouter des points de réputation
                    reputationImp.AjouterReputation(CurrentUserId.Value, 1, 8, questionId, "Première question");

                    // Supprimer le brouillon
                    ClientScript.RegisterStartupScript(this.GetType(), "clearDraft", 
                        "localStorage.removeItem('question_draft');", true);

                    // Rediriger vers la question créée
                    ShowSuccess("Question publiée avec succès !");
                    Response.Redirect($"question-detail.aspx?id={questionId}");
                }
                else
                {
                    ShowError("Erreur lors de la publication de la question. Veuillez réessayer.");
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la publication : " + ex.Message);
            }
        }

        private bool ValidateQuestion()
        {
            bool isValid = true;

            // SÉCURITÉ : Vérifier le rate limiting
            if (ForumSecurityHelper.IsRateLimited(CurrentUserId.Value, "post"))
            {
                ShowError("Vous publiez trop rapidement. Veuillez patienter avant de poster une nouvelle question.");
                ForumSecurityHelper.LogSecurityIncident("Rate limit exceeded for posting", CurrentUserId.Value, GetClientIPAddress());
                return false;
            }

            // SÉCURITÉ : Validation sécurisée du titre
            var titleValidation = ForumSecurityHelper.ValidateQuestionTitle(txtTitle.Text);
            if (!titleValidation.IsValid)
            {
                ShowError(titleValidation.ErrorMessage);
                isValid = false;
            }
            else
            {
                txtTitle.Text = titleValidation.CleanedValue;
            }

            // SÉCURITÉ : Validation sécurisée du contenu
            var contentValidation = ForumSecurityHelper.ValidatePostContent(txtContent.Text);
            if (!contentValidation.IsValid)
            {
                ShowError(contentValidation.ErrorMessage);
                isValid = false;
            }
            else
            {
                txtContent.Text = contentValidation.CleanedValue;
            }

            // SÉCURITÉ : Validation de la catégorie
            if (string.IsNullOrEmpty(hfSelectedCategory.Value))
            {
                ShowError("Veuillez sélectionner une catégorie.");
                isValid = false;
            }
            else
            {
                // Vérifier que la catégorie existe réellement
                if (!long.TryParse(hfSelectedCategory.Value, out long categoryId) || !CategoryExists(categoryId))
                {
                    ShowError("Catégorie invalide sélectionnée.");
                    ForumSecurityHelper.LogSecurityIncident("Invalid category manipulation", CurrentUserId.Value, GetClientIPAddress());
                    isValid = false;
                }
            }

            // SÉCURITÉ : Validation sécurisée des tags
            var tagsValidation = ForumSecurityHelper.ValidateTags(hfTags.Value);
            if (!tagsValidation.IsValid)
            {
                ShowError(tagsValidation.ErrorMessage);
                isValid = false;
            }
            else
            {
                hfTags.Value = tagsValidation.CleanedValue;
            }

            // Vérifier les questions similaires (prévention des doublons)
            if (isValid && CheckForSimilarQuestions(txtTitle.Text.Trim()))
            {
                ShowWarning("Des questions similaires existent déjà. Vérifiez qu'elles ne répondent pas déjà à votre problème.");
                // On n'empêche pas la publication, juste un avertissement
            }

            return isValid;
        }

        private bool CategoryExists(long categoryId)
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    return con.Categories.Any(c => c.CategoryId == categoryId);
                }
            }
            catch
            {
                return false;
            }
        }

        private string GetClientIPAddress()
        {
            string ipAddress = Request.ServerVariables["HTTP_X_FORWARDED_FOR"];

            if (string.IsNullOrEmpty(ipAddress) || ipAddress.ToLower() == "unknown")
                ipAddress = Request.ServerVariables["REMOTE_ADDR"];

            return ipAddress ?? "Unknown";
        }

        private void ProcessQuestionTags(long questionId, string tagsString)
        {
            if (string.IsNullOrEmpty(tagsString))
                return;

            try
            {
                var tagNames = tagsString.Split(',').Select(t => t.Trim()).Where(t => !string.IsNullOrEmpty(t)).ToList();
                tagsImp.MettreAJourTagsPost(questionId, tagNames);
            }
            catch (Exception ex)
            {
                // Erreur non critique pour les tags
                ShowWarning("Les tags n'ont pas pu être associés à la question.");
            }
        }

        private string DetermineQuestionDifficulty()
        {
            // Logique simple pour déterminer la difficulté basée sur le contenu
            string content = (txtTitle.Text + " " + txtContent.Text).ToLower();
            
            if (content.Contains("débutant") || content.Contains("commencer") || content.Contains("premier") || content.Contains("basic"))
                return "Beginner";
            
            if (content.Contains("avancé") || content.Contains("complexe") || content.Contains("optimisation") || content.Contains("performance"))
                return "Advanced";
            
            return "Intermediate";
        }

        private bool CheckForSimilarQuestions(string title)
        {
            try
            {
                // Recherche simple de questions similaires
                using (var con = new LinCom.Model.Connection())
                {
                    var similarQuestions = con.SujetForums
                        .Where(s => s.PostTypeId == 1 && s.Title.Contains(title.Substring(0, Math.Min(title.Length, 20))))
                        .Take(3)
                        .ToList();

                    return similarQuestions.Any();
                }
            }
            catch
            {
                return false;
            }
        }

        // Méthodes AJAX pour les fonctionnalités dynamiques
        [System.Web.Services.WebMethod]
        public static List<object> SearchTags(string query)
        {
            try
            {
                var tagsImp = new TagsImp();
                var tags = tagsImp.SuggererTags(query, 10);
                
                return tags.Select(t => new { 
                    TagName = t.TagName, 
                    Count = t.Count 
                }).Cast<object>().ToList();
            }
            catch
            {
                return new List<object>();
            }
        }

        [System.Web.Services.WebMethod]
        public static List<object> SearchSimilarQuestions(string title)
        {
            try
            {
                if (string.IsNullOrEmpty(title) || title.Length < 10)
                    return new List<object>();

                using (var con = new LinCom.Model.Connection())
                {
                    var questions = con.SujetForums
                        .Where(s => s.PostTypeId == 1 && s.Title.Contains(title))
                        .OrderByDescending(s => s.Score)
                        .Take(5)
                        .Select(s => new {
                            SujetForumId = s.SujetForumId,
                            Title = s.Title,
                            Score = s.Score,
                            AnswerCount = s.AnswerCount,
                            HasAcceptedAnswer = s.AcceptedAnswerId != null
                        })
                        .ToList();

                    return questions.Cast<object>().ToList();
                }
            }
            catch
            {
                return new List<object>();
            }
        }

        private void ShowError(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "error", 
                $"toastr.error('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }

        private void ShowSuccess(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "success", 
                $"toastr.success('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }

        private void ShowWarning(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "warning", 
                $"toastr.warning('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }
    }
}
