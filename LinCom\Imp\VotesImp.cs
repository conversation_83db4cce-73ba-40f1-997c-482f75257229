using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class VotesImp : IVotes
    {
        int msg;
        Votes p = new Votes();

        public int Add(Votes_Class vote)
        {
            using (Connection con = new Connection())
            {
                p.PostId = vote.PostId;
                p.VoteTypeId = vote.VoteTypeId;
                p.UserId = vote.UserId;
                p.CreationDate = DateTime.Now;
                p.BountyAmount = vote.BountyAmount;
                p.IPAddress = vote.IPAddress;
                p.UserAgent = vote.UserAgent;

                try
                {
                    con.Votes.Add(p);
                    if (con.SaveChanges() == 1)
                    {
                        // Mettre à jour le score du post
                        MettreAJourScorePost(vote.PostId);
                        
                        // Appliquer les points de réputation
                        AppliquerPointsReputation(vote.UserId.Value, vote.PostId, vote.VoteTypeId);
                        
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public int VoterPost(long postId, long userId, int voteType, string ipAddress = null)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si l'utilisateur a déjà voté
                var voteExistant = con.Votes.Where(v => v.PostId == postId && v.UserId == userId && 
                                                  (v.VoteTypeId == 2 || v.VoteTypeId == 3)).FirstOrDefault();

                if (voteExistant != null)
                {
                    // Si c'est le même type de vote, annuler
                    if (voteExistant.VoteTypeId == voteType)
                    {
                        return AnnulerVotePost(postId, userId);
                    }
                    else
                    {
                        // Changer le type de vote
                        voteExistant.VoteTypeId = voteType;
                        voteExistant.CreationDate = DateTime.Now;
                        con.SaveChanges();
                        
                        MettreAJourScorePost(postId);
                        AppliquerPointsReputation(userId, postId, voteType);
                        
                        return 1;
                    }
                }
                else
                {
                    // Nouveau vote
                    var nouveauVote = new Votes_Class
                    {
                        PostId = postId,
                        UserId = userId,
                        VoteTypeId = voteType,
                        IPAddress = ipAddress
                    };
                    
                    return Add(nouveauVote);
                }
            }
        }

        public int AnnulerVotePost(long postId, long userId)
        {
            using (Connection con = new Connection())
            {
                var vote = con.Votes.Where(v => v.PostId == postId && v.UserId == userId && 
                                          (v.VoteTypeId == 2 || v.VoteTypeId == 3)).FirstOrDefault();

                if (vote != null)
                {
                    con.Votes.Remove(vote);
                    con.SaveChanges();
                    
                    MettreAJourScorePost(postId);
                    
                    return 1;
                }
                return 0;
            }
        }

        public bool ADejaVote(long postId, long userId)
        {
            using (Connection con = new Connection())
            {
                return con.Votes.Any(v => v.PostId == postId && v.UserId == userId && 
                                    (v.VoteTypeId == 2 || v.VoteTypeId == 3));
            }
        }

        public int ObtenirTypeVoteUtilisateur(long postId, long userId)
        {
            using (Connection con = new Connection())
            {
                var vote = con.Votes.Where(v => v.PostId == postId && v.UserId == userId && 
                                          (v.VoteTypeId == 2 || v.VoteTypeId == 3)).FirstOrDefault();
                return vote?.VoteTypeId ?? 0;
            }
        }

        public void ChargerVotesPost(long postId, out int upVotes, out int downVotes, out int score)
        {
            using (Connection con = new Connection())
            {
                upVotes = con.Votes.Count(v => v.PostId == postId && v.VoteTypeId == 2);
                downVotes = con.Votes.Count(v => v.PostId == postId && v.VoteTypeId == 3);
                score = upVotes - downVotes;
            }
        }

        public int AjouterAuxFavoris(long postId, long userId)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si déjà en favoris
                var favoriExistant = con.Votes.Where(v => v.PostId == postId && v.UserId == userId && v.VoteTypeId == 5).FirstOrDefault();
                
                if (favoriExistant == null)
                {
                    var favori = new Votes_Class
                    {
                        PostId = postId,
                        UserId = userId,
                        VoteTypeId = 5 // Favorite
                    };
                    
                    return Add(favori);
                }
                return 0;
            }
        }

        public int SupprimerDesFavoris(long postId, long userId)
        {
            using (Connection con = new Connection())
            {
                var favori = con.Votes.Where(v => v.PostId == postId && v.UserId == userId && v.VoteTypeId == 5).FirstOrDefault();
                
                if (favori != null)
                {
                    con.Votes.Remove(favori);
                    con.SaveChanges();
                    return 1;
                }
                return 0;
            }
        }

        public bool EstDansFavoris(long postId, long userId)
        {
            using (Connection con = new Connection())
            {
                return con.Votes.Any(v => v.PostId == postId && v.UserId == userId && v.VoteTypeId == 5);
            }
        }

        public void ChargerFavorisUtilisateur(ListView lv, long userId, int limite = 20)
        {
            using (Connection con = new Connection())
            {
                var favoris = from v in con.Votes
                             join p in con.SujetForums on v.PostId equals p.SujetForumId
                             where v.UserId == userId && v.VoteTypeId == 5
                             orderby v.CreationDate descending
                             select new
                             {
                                 p.SujetForumId,
                                 p.Title,
                                 p.Body,
                                 p.Score,
                                 p.ViewCount,
                                 p.AnswerCount,
                                 p.CreationDate,
                                 p.OwnerDisplayName,
                                 DateFavori = v.CreationDate,
                                 p.Tags
                             };

                lv.DataSource = favoris.Take(limite).ToList();
                lv.DataBind();
            }
        }

        public int CompterFavoris(long postId)
        {
            using (Connection con = new Connection())
            {
                return con.Votes.Count(v => v.PostId == postId && v.VoteTypeId == 5);
            }
        }

        private void MettreAJourScorePost(long postId)
        {
            using (Connection con = new Connection())
            {
                var post = con.SujetForums.Where(p => p.SujetForumId == postId).FirstOrDefault();
                if (post != null)
                {
                    int upVotes = con.Votes.Count(v => v.PostId == postId && v.VoteTypeId == 2);
                    int downVotes = con.Votes.Count(v => v.PostId == postId && v.VoteTypeId == 3);
                    post.Score = upVotes - downVotes;
                    con.SaveChanges();
                }
            }
        }

        private void AppliquerPointsReputation(long userId, long postId, int voteType)
        {
            using (Connection con = new Connection())
            {
                var post = con.SujetForums.Where(p => p.SujetForumId == postId).FirstOrDefault();
                if (post != null && post.OwnerUserId.HasValue)
                {
                    int points = 0;
                    string description = "";

                    switch (voteType)
                    {
                        case 2: // UpVote
                            points = post.PostTypeId == 1 ? 5 : 10; // Question: 5, Answer: 10
                            description = post.PostTypeId == 1 ? "Question upvoted" : "Answer upvoted";
                            break;
                        case 3: // DownVote
                            points = -2;
                            description = post.PostTypeId == 1 ? "Question downvoted" : "Answer downvoted";
                            break;
                    }

                    if (points != 0)
                    {
                        // Ajouter à l'historique de réputation
                        var reputationHistory = new ReputationHistory
                        {
                            UserId = post.OwnerUserId.Value,
                            PostId = postId,
                            ReputationChange = points,
                            ReputationHistoryTypeId = voteType == 2 ? (post.PostTypeId == 1 ? 1 : 2) : (post.PostTypeId == 1 ? 4 : 5),
                            CreationDate = DateTime.Now,
                            Description = description
                        };

                        con.ReputationHistories.Add(reputationHistory);
                        con.SaveChanges();

                        // Mettre à jour la réputation totale de l'utilisateur
                        MettreAJourReputationUtilisateur(post.OwnerUserId.Value);
                    }
                }
            }
        }

        public void MettreAJourReputationUtilisateur(long userId)
        {
            using (Connection con = new Connection())
            {
                var membre = con.Membres.Where(m => m.MembreId == userId).FirstOrDefault();
                if (membre != null)
                {
                    int reputationTotale = con.ReputationHistories.Where(r => r.UserId == userId)
                                                                  .Sum(r => (int?)r.ReputationChange) ?? 0;
                    
                    // Assurer un minimum de 1 point de réputation
                    membre.Reputation = Math.Max(1, reputationTotale);
                    con.SaveChanges();
                }
            }
        }

        public void Chargement_GDV(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var obj = (from v in con.Votes
                           join p in con.SujetForums on v.PostId equals p.SujetForumId
                           join u in con.Membres on v.UserId equals u.MembreId into userJoin
                           from u in userJoin.DefaultIfEmpty()
                           select new
                           {
                               VoteId = v.VoteId,
                               PostTitle = p.Title,
                               VoteType = v.VoteTypeId == 2 ? "UpVote" : v.VoteTypeId == 3 ? "DownVote" : "Other",
                               UserName = u != null ? u.Nom + " " + u.Prenom : "Anonymous",
                               CreationDate = v.CreationDate,
                               BountyAmount = v.BountyAmount
                           }).OrderByDescending(v => v.CreationDate).ToList();

                gdv.DataSource = obj;
                gdv.DataBind();
            }
        }

        public void AfficherDetails(long voteId, Votes_Class vote) { throw new NotImplementedException(); }
        public int Edit(Votes_Class vote, long voteId) { throw new NotImplementedException(); }
        public int Supprimer(long voteId) { throw new NotImplementedException(); }
        public void Search(GridView gdv, string searchTerm) { throw new NotImplementedException(); }
        public int Count() { throw new NotImplementedException(); }

        // MÉTHODES COMPLÈTES STACK OVERFLOW

        public int VoterCommentaire(long commentId, long userId, bool upVote, string ipAddress = null)
        {
            using (Connection con = new Connection())
            {
                // Les commentaires n'ont que des votes positifs dans Stack Overflow
                if (!upVote) return 0;

                var voteExistant = con.Votes.Where(v => v.PostId == commentId && v.UserId == userId && v.VoteTypeId == 2).FirstOrDefault();

                if (voteExistant == null)
                {
                    var vote = new Votes
                    {
                        PostId = commentId,
                        UserId = userId,
                        VoteTypeId = 2, // UpVote
                        CreationDate = DateTime.Now,
                        IPAddress = ipAddress
                    };

                    con.Votes.Add(vote);
                    con.SaveChanges();

                    // Mettre à jour le score du commentaire
                    var commentaire = con.Comments.Where(c => c.CommentId == commentId).FirstOrDefault();
                    if (commentaire != null)
                    {
                        commentaire.Score++;
                        con.SaveChanges();
                    }

                    return 1;
                }
                return 0;
            }
        }
        public int AnnulerVoteCommentaire(long commentId, long userId)
        {
            using (Connection con = new Connection())
            {
                var vote = con.Votes.Where(v => v.PostId == commentId && v.UserId == userId && v.VoteTypeId == 2).FirstOrDefault();

                if (vote != null)
                {
                    con.Votes.Remove(vote);
                    con.SaveChanges();

                    // Mettre à jour le score du commentaire
                    var commentaire = con.Comments.Where(c => c.CommentId == commentId).FirstOrDefault();
                    if (commentaire != null)
                    {
                        commentaire.Score--;
                        con.SaveChanges();
                    }

                    return 1;
                }
                return 0;
            }
        }

        public bool ADejaVoteCommentaire(long commentId, long userId)
        {
            using (Connection con = new Connection())
            {
                return con.Votes.Any(v => v.PostId == commentId && v.UserId == userId && v.VoteTypeId == 2);
            }
        }

        public int ObtenirScoreCommentaire(long commentId)
        {
            using (Connection con = new Connection())
            {
                var commentaire = con.Comments.Where(c => c.CommentId == commentId).FirstOrDefault();
                return commentaire?.Score ?? 0;
            }
        }
        public int VoterFermeture(long postId, long userId, string raison) { throw new NotImplementedException(); }
        public int VoterReouverture(long postId, long userId) { throw new NotImplementedException(); }
        public int VoterSuppression(long postId, long userId) { throw new NotImplementedException(); }
        public int VoterRestoration(long postId, long userId) { throw new NotImplementedException(); }
        public int SignalerSpam(long postId, long userId, string raison) { throw new NotImplementedException(); }
        public int SignalerOffensant(long postId, long userId, string raison) { throw new NotImplementedException(); }
        public int DemarrerBounty(long postId, long userId, int montant) { throw new NotImplementedException(); }
        public int AttribuerBounty(long postId, long reponseId, long userId, int montant) { throw new NotImplementedException(); }
        public void ChargerBountiesActifs(ListView lv) { throw new NotImplementedException(); }
        public void ChargerBountiesUtilisateur(ListView lv, long userId) { throw new NotImplementedException(); }
        public bool PeutDemarrerBounty(long postId, long userId) { throw new NotImplementedException(); }
        public void ChargerStatistiquesVotes(out int totalVotes, out int upVotes, out int downVotes)
        {
            using (Connection con = new Connection())
            {
                totalVotes = con.Votes.Count(v => v.VoteTypeId == 2 || v.VoteTypeId == 3);
                upVotes = con.Votes.Count(v => v.VoteTypeId == 2);
                downVotes = con.Votes.Count(v => v.VoteTypeId == 3);
            }
        }

        public void ChargerVotesRecents(ListView lv, int limite = 20)
        {
            using (Connection con = new Connection())
            {
                var votes = from v in con.Votes
                           join p in con.SujetForums on v.PostId equals p.SujetForumId
                           join u in con.Membres on v.UserId equals u.MembreId into userJoin
                           from u in userJoin.DefaultIfEmpty()
                           where v.VoteTypeId == 2 || v.VoteTypeId == 3
                           orderby v.CreationDate descending
                           select new
                           {
                               v.VoteId,
                               PostTitle = p.Title,
                               VoteType = v.VoteTypeId == 2 ? "UpVote" : "DownVote",
                               UserName = u != null ? u.Nom + " " + u.Prenom : "Anonymous",
                               v.CreationDate,
                               PostType = p.PostTypeId == 1 ? "Question" : "Réponse"
                           };

                lv.DataSource = votes.Take(limite).ToList();
                lv.DataBind();
            }
        }

        public void ChargerTopVoteurs(ListView lv, int limite = 10)
        {
            using (Connection con = new Connection())
            {
                var topVoteurs = from v in con.Votes
                                join u in con.Membres on v.UserId equals u.MembreId
                                where v.VoteTypeId == 2 || v.VoteTypeId == 3
                                group v by new { u.MembreId, u.Nom, u.Prenom, u.Reputation } into g
                                orderby g.Count() descending
                                select new
                                {
                                    UserId = g.Key.MembreId,
                                    UserName = g.Key.Nom + " " + g.Key.Prenom,
                                    Reputation = g.Key.Reputation,
                                    TotalVotes = g.Count(),
                                    UpVotes = g.Count(v => v.VoteTypeId == 2),
                                    DownVotes = g.Count(v => v.VoteTypeId == 3),
                                    VoteRatio = g.Count(v => v.VoteTypeId == 2) > 0 ?
                                        (double)g.Count(v => v.VoteTypeId == 2) / g.Count() * 100 : 0
                                };

                lv.DataSource = topVoteurs.Take(limite).ToList();
                lv.DataBind();
            }
        }
        public void ChargerPostsLesPlusVotes(ListView lv, int limite = 10) { throw new NotImplementedException(); }
        public void ChargerActiviteVotes(ListView lv, long userId, int limite = 20) { throw new NotImplementedException(); }
        public bool PeutVoter(long userId, int voteType)
        {
            using (Connection con = new Connection())
            {
                var membre = con.Membres.Where(m => m.MembreId == userId).FirstOrDefault();
                if (membre == null) return false;

                // Vérifier la réputation minimale pour voter
                switch (voteType)
                {
                    case 2: // UpVote
                        return membre.Reputation >= 15;
                    case 3: // DownVote
                        return membre.Reputation >= 125;
                    case 5: // Favorite
                        return membre.Reputation >= 15;
                    default:
                        return false;
                }
            }
        }

        public bool AAtteintLimiteVotesJournaliere(long userId)
        {
            using (Connection con = new Connection())
            {
                int votesAujourdhui = con.Votes.Count(v => v.UserId == userId &&
                                                     v.CreationDate >= DateTime.Today &&
                                                     (v.VoteTypeId == 2 || v.VoteTypeId == 3));

                return votesAujourdhui >= 40; // Limite Stack Overflow
            }
        }

        public int ObtenirNombreVotesJour(long userId)
        {
            using (Connection con = new Connection())
            {
                return con.Votes.Count(v => v.UserId == userId &&
                                      v.CreationDate >= DateTime.Today &&
                                      (v.VoteTypeId == 2 || v.VoteTypeId == 3));
            }
        }

        public bool EstVoteValide(long postId, long userId, int voteType)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si l'utilisateur peut voter
                if (!PeutVoter(userId, voteType)) return false;

                // Vérifier la limite journalière
                if (AAtteintLimiteVotesJournaliere(userId)) return false;

                // Vérifier si l'utilisateur n'est pas l'auteur du post
                var post = con.SujetForums.Where(p => p.SujetForumId == postId).FirstOrDefault();
                if (post != null && post.OwnerUserId == userId) return false;

                return true;
            }
        }
        public void DetecterVotesAnormaux(long userId) { throw new NotImplementedException(); }
        public void CalculerReputationPost(long postId) { throw new NotImplementedException(); }
        public int CalculerPointsVote(int voteType, bool estAuteur = false) { throw new NotImplementedException(); }
        public void AppliquerPointsReputation(long userId, long postId, int voteType) { throw new NotImplementedException(); }
        public void ChargerHistoriqueVotes(ListView lv, long postId) { throw new NotImplementedException(); }
        public void ChargerVotesUtilisateur(ListView lv, long userId, int limite = 50) { throw new NotImplementedException(); }
        public void LoggerVote(long postId, long userId, int voteType, string ipAddress) { throw new NotImplementedException(); }
        public void ChargerVotesSuspects(ListView lv) { throw new NotImplementedException(); }
        public void AnnulerVotesUtilisateur(long userId, string raison) { throw new NotImplementedException(); }
        public void RecalculerTousLesScores() { throw new NotImplementedException(); }
        public void NettoierVotesAnciens(DateTime dateLimit) { throw new NotImplementedException(); }
        public void ChargerRapportVotes(DateTime dateDebut, DateTime dateFin) { throw new NotImplementedException(); }
    }
}
