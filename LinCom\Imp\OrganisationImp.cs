﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class OrganisationImp : IOrganisation
    {
        private Organisation organisation = new Organisation();
        int msg;

        public void AfficherDetails(long organisationId, Organisation_Class organisationClass)
        {
            using (Connection con = new Connection())
            {
                var o = con.Organisations.FirstOrDefault(x => x.OrganisationId == organisationId);
                if (o != null)
                {
                    organisationClass.OrganisationId = o.OrganisationId;
                    organisationClass.Nom = o.Nom;
                    organisationClass.TypeOrganisationId = o.TypeOrganisationId;
                    organisationClass.ProvinceId = o.ProvinceId;
                    organisationClass.CommuneId = o.CommuneId;
                    organisationClass.Email = o.Email;
                    organisationClass.Telephone = o.Telephone;
                    organisationClass.SiteWeb = o.SiteWeb;
                    organisationClass.Logo = o.Logo;
                    organisationClass.RS = o.RS;
                    organisationClass.sigle = o.sigle;
                    organisationClass.RC = o.RC;
                    organisationClass.RC_doc = o.RC_doc;
                    organisationClass.NIF = o.NIF;
                    organisationClass.NIF_doc = o.NIF_doc;
                    organisationClass.facebook = o.facebook;
                    organisationClass.twitter = o.twitter;
                    organisationClass.instagramme = o.instagramme;
                    organisationClass.linkedin = o.linkedin;
                    organisationClass.youtube = o.youtube;
                    organisationClass.province = o.province;
                    organisationClass.commune = o.commune;
                    organisationClass.Description = o.Description;
                    organisationClass.Vision = o.Vision;
                    organisationClass.Mission = o.Mission;
                    organisationClass.NbreHomme = o.NbreHomme;
                    organisationClass.NbreFemme = o.NbreFemme;
                    organisationClass.Enregistre = o.Enregistre;
                    organisationClass.DateCreation = o.DateCreation;
                    organisationClass.Statut = o.Statut;
                    organisationClass.CreatedAt = o.CreatedAt;
                    organisationClass.UpdatedAt = o.UpdatedAt;
                    organisationClass.name = o.name;
                    organisationClass.Adresse = o.Adresse;
                    organisationClass.Latitude = o.Latitude ;
                    organisationClass.Longitude = o.Longitude;
                }
            }
        }

        public void AfficherDetails(string organisationId, Organisation_Class organisationClass)
        {
            using (Connection con = new Connection())
            {
                var o = con.Organisations.FirstOrDefault(x => x.name == organisationId);
                if (o != null)
                {
                    organisationClass.OrganisationId = o.OrganisationId;
                    organisationClass.Nom = o.Nom;
                    organisationClass.TypeOrganisationId = o.TypeOrganisationId;
                    organisationClass.ProvinceId = o.ProvinceId;
                    organisationClass.CommuneId = o.CommuneId;
                    organisationClass.Email = o.Email;
                    organisationClass.Telephone = o.Telephone;
                    organisationClass.SiteWeb = o.SiteWeb;
                    organisationClass.Logo = o.Logo;
                    organisationClass.RS = o.RS;
                    organisationClass.sigle = o.sigle;
                    organisationClass.RC = o.RC;
                    organisationClass.RC_doc = o.RC_doc;
                    organisationClass.NIF = o.NIF;
                    organisationClass.NIF_doc = o.NIF_doc;
                    organisationClass.facebook = o.facebook;
                    organisationClass.twitter = o.twitter;
                    organisationClass.instagramme = o.instagramme;
                    organisationClass.linkedin = o.linkedin;
                    organisationClass.youtube = o.youtube;
                    organisationClass.province = o.province;
                    organisationClass.commune = o.commune;
                    organisationClass.Description = o.Description;
                    organisationClass.Vision = o.Vision;
                    organisationClass.Mission = o.Mission;
                    organisationClass.NbreHomme = o.NbreHomme;
                    organisationClass.NbreFemme = o.NbreFemme;
                    organisationClass.Enregistre = o.Enregistre;
                    organisationClass.DateCreation = o.DateCreation;
                    organisationClass.Statut = o.Statut;
                    organisationClass.CreatedAt = o.CreatedAt;
                    organisationClass.UpdatedAt = o.UpdatedAt;
                    organisationClass.name = o.name;
                    organisationClass.Adresse = o.Adresse;
                    organisationClass.Latitude = o.Latitude;
                    organisationClass.Longitude = o.Longitude;
                }
            }
        }

        public int Ajouter(Organisation_Class organisationClass)
        {
            using (Connection con = new Connection())
            {
                organisation.Nom = organisationClass.Nom;
                organisation.TypeOrganisationId = organisationClass.TypeOrganisationId;
                organisation.ProvinceId = organisationClass.ProvinceId;
                organisation.CommuneId = organisationClass.CommuneId;
                organisation.Email = organisationClass.Email;
                organisation.Telephone = organisationClass.Telephone;
                organisation.SiteWeb = organisationClass.SiteWeb;
                organisation.Logo = organisationClass.Logo;
                organisation.RS = organisationClass.RS;
                organisation.sigle = organisationClass.sigle;
                organisation.RC = organisationClass.RC;
                organisation.RC_doc = organisationClass.RC_doc;
                organisation.NIF = organisationClass.NIF;
                organisation.NIF_doc = organisationClass.NIF_doc;
                organisation.facebook = organisationClass.facebook;
                organisation.twitter = organisationClass.twitter;
                organisation.instagramme = organisationClass.instagramme;
                organisation.linkedin = organisationClass.linkedin;
                organisation.youtube = organisationClass.youtube;
                organisation.province = organisationClass.province;
                organisation.commune = organisationClass.commune;
                organisation.Description = organisationClass.Description;
                organisation.Vision = organisationClass.Vision;
                organisation.Mission = organisationClass.Mission;
                organisation.NbreHomme = organisationClass.NbreHomme;
                organisation.NbreFemme = organisationClass.NbreFemme;
                organisation.Enregistre = organisationClass.Enregistre;
                organisation.DateCreation = organisationClass.DateCreation;
                organisation.Statut = "actif";
                organisation.name = organisationClass.name;
                organisation.Adresse = organisationClass.Adresse;
                organisationClass.Latitude = organisationClass.Latitude;
                organisationClass.Longitude = organisationClass.Longitude;

                try
                {
                    con.Organisations.Add(organisation);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }
        public int count(int cd, int ct,string publie, string code)
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var b = (from p in con.Organisations
                            
                             select p).Count();
                    n = b;
                }
                else if (cd == 1)
                {
                    var b = (from p in con.Organisations
                            
                             where p.Statut == publie && p.RS == code   
                             select p).Count();
                    n = b;
                }
               
               
            }
            return n;
        }
        public void Chargement_GDVL(ListView listv,string code, int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd == 0)
                {
                    var obj = (from p in con.Organisations
                               join ca in con.TypeOrganisations on p.TypeOrganisationId equals ca.TypeOrganisationId

                               join c in con.Provinces on p.ProvinceId equals c.ProvinceId
                               join pv in con.Communes on p.CommuneId equals pv.CommuneId

                               select new
                               {
                                   id = p.OrganisationId,
                                   TypeOrganisationId = p.TypeOrganisationId,
                                   nom = p.Nom,
                                   RS = p.RS,
                                   sigle = p.sigle,
                                  
                                   phone = p.Telephone,
                                   email = p.Email,
                                   
                                   RC = p.RC,
                                   RC_doc = p.RC_doc,
                                   NIF = p.NIF,
                                   NIF_doc = p.NIF_doc,

                                   adresse_complete = p.Adresse,
                                   description_coop = p.Description,

                                   DateCreation = p.DateCreation,
                                  
                                   logo = p.Logo,
                                   status = p.Statut,

                                   NMCAT = ca.Libelle,

                                   nom_CMN = c.Nom,

                                   province = p.province,
                                   nom_PVC = pv.Nom,
                                 
                               }).ToList();


                    listv.DataSource = obj;
                    listv.DataBind();


                }
                else if (cd == 1)
                {
                    var obj = (from p in con.Organisations
                               join ca in con.TypeOrganisations on p.TypeOrganisationId equals ca.TypeOrganisationId
                               join c in con.Provinces on p.ProvinceId equals c.ProvinceId
                               join pv in con.Communes on p.CommuneId equals pv.CommuneId
                               where p.RS == code
                               select new
                               {
                                   id = p.OrganisationId,
                                   TypeOrganisationId = p.TypeOrganisationId,
                                   nom = p.Nom,
                                   RS = p.RS,
                                   sigle = p.sigle,

                                   phone = p.Telephone,
                                   email = p.Email,

                                   RC = p.RC,
                                   RC_doc = p.RC_doc,
                                   NIF = p.NIF,
                                   NIF_doc = p.NIF_doc,

                                   adresse_complete = p.Adresse,
                                   description_coop = p.Description,

                                   DateCreation = p.DateCreation,

                                   logo = p.Logo,
                                   status = p.Statut,

                                   NMCAT = ca.Libelle,

                                   nom_CMN = c.Nom,

                                   province = p.province,
                                   nom_PVC = pv.Nom,
                               }).Take(6).ToList();


                    listv.DataSource = obj;
                    listv.DataBind();

                }
                else if (cd == 2)
                {
                    var obj = (from p in con.Organisations
                                   // from f in con.PVCs
                               group p by p.province into gd
                               select new
                               {
                                   number = gd.Count(),
                                 
                                   code = gd.Key,
                                   

                               }).ToList();


                    listv.DataSource = obj;
                    listv.DataBind();
                }
                else if (cd == 3)
                {
                    var obj = (from p in con.Organisations
                               join ca in con.TypeOrganisations on p.TypeOrganisationId equals ca.TypeOrganisationId
                               join c in con.Provinces on p.ProvinceId equals c.ProvinceId
                               join pv in con.Communes on p.CommuneId equals pv.CommuneId
                               where p.RS ==code
                               select new
                               {
                                   id = p.OrganisationId,
                                   TypeOrganisationId = p.TypeOrganisationId,
                                   nom = p.Nom,
                                   RS = p.RS,
                                   sigle = p.sigle,

                                   phone = p.Telephone,
                                   email = p.Email,

                                   RC = p.RC,
                                   RC_doc = p.RC_doc,
                                   NIF = p.NIF,
                                   NIF_doc = p.NIF_doc,

                                   adresse_complete = p.Adresse,
                                   description_coop = p.Description,

                                   DateCreation = p.DateCreation,

                                   logo = p.Logo,
                                   status = p.Statut,

                                   NMCAT = ca.Libelle,

                                   nom_CMN = c.Nom,

                                   province = p.province,
                                   nom_PVC = pv.Nom,
                               }).Take(6).ToList();


                }
            }

        }
    
        public void ChargerGridView(GridView gdv, string filtre = "", int typeOrg = 0)
        {
            using (Connection con = new Connection())
            {
                var query = from o in con.Organisations
                            select new
                            {
                                o.OrganisationId,
                                o.Nom,
                                o.TypeOrganisationId,
                                o.ProvinceId,
                                o.CommuneId,
                                o.Email,
                                o.Telephone,
                                o.SiteWeb,
                                o.province,
                                o.commune,
                                o.Description,
                                o.DateCreation,
                                o.Statut,
                                o.name
                            };

                if (!string.IsNullOrEmpty(filtre))
                {
                    query = query.Where(x => x.Nom.Contains(filtre) ||
                                           x.Email.Contains(filtre));
                }

                if (typeOrg > 0)
                {
                    query = query.Where(x => x.TypeOrganisationId == typeOrg);
                }

                gdv.DataSource = query.ToList();
                gdv.DataBind();
            }
        }

        public int Modifier(Organisation_Class organisationClass, long id)
        {
            using (Connection con = new Connection())
            {
                var o = con.Organisations.FirstOrDefault(x => x.OrganisationId == id);
                if (o != null)
                {

                    o.Nom = organisationClass.Nom;
                    o.TypeOrganisationId = organisationClass.TypeOrganisationId;
                    o.ProvinceId = organisationClass.ProvinceId;
                    o.CommuneId = organisationClass.CommuneId;
                    o.Email = organisationClass.Email;
                    o.Telephone = organisationClass.Telephone;
                    o.Logo = organisationClass.Logo;
                   
                    o.province = organisationClass.province;
                    o.commune = organisationClass.commune;
                    o.DateCreation = organisationClass.DateCreation;
                    o.Enregistre = organisationClass.Enregistre;
                    o.Statut = organisationClass.Statut;
                    o.UpdatedAt = DateTime.Now;
                    o.sigle = organisationClass.sigle;
                    o.name=organisationClass.name;
                    o.Adresse = organisationClass.Adresse;
                    o.Description=organisationClass.Description;
                    o.RS = organisationClass.RS;
                    try
                    {

                        if (con.SaveChanges() == 1)
                    {

                        con.Organisations.Add(o);
                        con.Entry(o).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg;
            }
        }
        public void chargerOrganisation(DropDownList ddw)
        {
            ddw.Items.Clear();
            using (Connection con = new Connection())
            {
                var obj = (from p in con.Organisations where p.Statut=="actif" select p).ToList();

                if (obj != null && obj.Count() > 0)
                {
                    ddw.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner l'organisation";
                    ddw.Items.Add(item0);

                    foreach (var data in obj)
                    {
                        ListItem item = new ListItem();
                        item.Value = data.OrganisationId.ToString();
                        item.Text = data.Nom;
                        ddw.Items.Add(item);
                    }

                }
                else
                {
                    ddw.Items.Clear();

                    ListItem item0 = new ListItem();
                    item0.Value = "-1";
                    item0.Text = "Selectionner l'organisation";
                    ddw.Items.Add(item0);

                }

            }
        }


        public int Supprimer(long organisationId)
        {
            using (Connection con = new Connection())
            {
                var o = con.Organisations.FirstOrDefault(x => x.OrganisationId == organisationId);
                if (o != null)
                {
                    o.Statut = "supprime";
                    o.UpdatedAt = DateTime.Now;
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}