﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ISessionMentorat
    {
        void AfficherDetails(int idSession, SessionMentorat_Class session);
        int Ajouter(SessionMentorat_Class session);
        int Modifier(SessionMentorat_Class session);
        int Supprimer(int idSession);
        void chargerSessionMentorat(DropDownList lst);
        void ChargerSessions(GridView gdv, int? idMentor = null, int? idMentoree = null);
        void ChargerSessionsParProgramme(GridView gdv, int idProgramme);
        void ChargerSessionsPubliques(ListView lv, int limite = 10);
        int count();
    }
}
