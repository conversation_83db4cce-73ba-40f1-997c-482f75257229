﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class SupportFormationImp : ISupportFormation
    {
        int msg;
        private SupportFormation support = new SupportFormation();

        public void AfficherDetails(int idSupport, SupportFormation_Class supportClass)
        {
            using (Connection con = new Connection())
            {
                var s = con.SupportFormations.FirstOrDefault(x => x.SupportFormationId == idSupport);
                if (s != null)
                {
                    supportClass.SupportFormationId = s.SupportFormationId;
                    supportClass.FormationId = s.FormationId;
                    supportClass.Titre = s.Titre;
                    supportClass.Fichier = s.Fichier;
                    supportClass.name = s.name;
                }
            }
        }

        public int Ajouter(SupportFormation_Class supportClass)
        {
            using (Connection con = new Connection())
            {
                support.FormationId = supportClass.FormationId;
                support.Titre = supportClass.Titre;
                support.Fichier = supportClass.Fichier;
                support.name = supportClass.name;
               

                try
                {
                    con.SupportFormations.Add(support);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch
                {
                    return msg = 0;
                }
            }
        }

        public void ChargerSupport(GridView gdv)
        {
            throw new NotImplementedException();
        }

        public void chargerSupportFormation(DropDownList ddl)
        {
            throw new NotImplementedException();
        }

        public void ChargerSupports(GridView gdv, int idFormation, string name = "")
        {
            using (Connection con = new Connection())
            {
                var query = from s in con.SupportFormations
                            where s.FormationId == idFormation
                            select new
                            {
                                s.SupportFormationId,
                                s.Titre,
                                s.name,
                               
                                s.Fichier,
                                
                            };

                if (!string.IsNullOrEmpty(name))
                {
                    query = query.Where(x => x.Titre == name);
                }

                gdv.DataSource = query.OrderBy(x => x.name).ToList();
                gdv.DataBind();
            }
        }

        
        public int Modifier(SupportFormation_Class supportClass)
        {
            using (Connection con = new Connection())
            {
                var s = con.SupportFormations.FirstOrDefault(x => x.SupportFormationId == supportClass.SupportFormationId);
                if (s != null)
                {
                    s.Titre = supportClass.Titre;
                    s.name = supportClass.name;
                    s.Fichier = supportClass.Fichier;
                    

                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }

        public string ObtenirFichier(int idSupport)
        {
            using (Connection con = new Connection())
            {
                return con.SupportFormations
                    .Where(s => s.SupportFormationId == idSupport)
                    .Select(s => s.Fichier)
                    .FirstOrDefault();
            }
        }

   

        public int Supprimer(int idSupport)
        {
            using (Connection con = new Connection())
            {
                var s = con.SupportFormations.FirstOrDefault(x => x.SupportFormationId == idSupport);
                if (s != null)
                {
                   
                    try
                    {
                        if (con.SaveChanges() == 1)
                        {
                            return msg = 1;
                        }
                        else
                            return msg = 0;
                    }
                    catch
                    {
                        return msg = 0;
                    }
                }
                return msg = 0;
            }
        }
    }
}