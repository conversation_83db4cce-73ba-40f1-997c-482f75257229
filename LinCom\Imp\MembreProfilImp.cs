﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace LinCom.Imp
{
    public class MembreProfilImp: IMembreProfil
    {
        private MembreProfil membreProfil = new MembreProfil();

        public void AfficherDetails(long membreProfilId, MembreProfil_Class membreProfilClass)
        {
            using (Connection con = new Connection())
            {
                var mp = con.MembreProfils.FirstOrDefault(x => x.MembreProfilId == membreProfilId);
                if (mp != null)
                {
                    membreProfilClass.MembreProfilId = mp.MembreProfilId;
                    membreProfilClass.MembreId = mp.MembreId;
                    membreProfilClass.PhotoProfil = mp.PhotoProfil;
                    membreProfilClass.Facebook = mp.facebook;
                    membreProfilClass.SiteWeb = mp.siteweb;
                    membreProfilClass.Twitter = mp.twitter;
                    membreProfilClass.Instagramme = mp.instagramme;
                    membreProfilClass.Linkedin = mp.linkedin;
                    membreProfilClass.Youtube = mp.youtube;
                    membreProfilClass.Biographie = mp.Biographie;
                    membreProfilClass.DateInscription = mp.DateInscription;
                    membreProfilClass.name = mp.name;
                }
            }
        }

        public void AfficherDetails(string name, MembreProfil_Class membreProfilClass)
        {
            using (Connection con = new Connection())
            {
                var mp = con.MembreProfils.FirstOrDefault(x => x.name == name);
                if (mp != null)
                {
                    membreProfilClass.MembreProfilId = mp.MembreProfilId;
                    membreProfilClass.MembreId = mp.MembreId;
                    membreProfilClass.PhotoProfil = mp.PhotoProfil;
                    membreProfilClass.Facebook = mp.facebook;
                    membreProfilClass.SiteWeb = mp.siteweb;
                    membreProfilClass.Twitter = mp.twitter;
                    membreProfilClass.Instagramme = mp.instagramme;
                    membreProfilClass.Linkedin = mp.linkedin;
                    membreProfilClass.Youtube = mp.youtube;
                    membreProfilClass.Biographie = mp.Biographie;
                    membreProfilClass.DateInscription = mp.DateInscription;
                    membreProfilClass.name = mp.name;
                }
            }
        }

        public int Ajouter(MembreProfil_Class membreProfilClass)
        {
            using (Connection con = new Connection())
            {
                membreProfil.MembreId = membreProfilClass.MembreId;
                membreProfil.PhotoProfil = membreProfilClass.PhotoProfil;
                membreProfil.facebook = membreProfilClass.Facebook;
                membreProfil.siteweb = membreProfilClass.SiteWeb;
                membreProfil.twitter = membreProfilClass.Twitter;
                membreProfil.instagramme = membreProfilClass.Instagramme;
                membreProfil.linkedin = membreProfilClass.Linkedin;
                membreProfil.youtube = membreProfilClass.Youtube;
                membreProfil.Biographie = membreProfilClass.Biographie;
                membreProfil.DateInscription = DateTime.Now;
                membreProfil.name = membreProfilClass.name;

                try
                {
                    con.MembreProfils.Add(membreProfil);
                    return con.SaveChanges();
                }
                catch
                {
                    return 0;
                }
            }
        }

        public void ChargerProfil(long membreId, MembreProfil_Class membreProfilClass)
        {
            using (Connection con = new Connection())
            {
                var mp = con.MembreProfils.FirstOrDefault(x => x.MembreId == membreId);
                if (mp != null)
                {
                    membreProfilClass.MembreProfilId = mp.MembreProfilId;
                    membreProfilClass.MembreId = mp.MembreId;
                    membreProfilClass.PhotoProfil = mp.PhotoProfil;
                    membreProfilClass.Facebook = mp.facebook;
                    membreProfilClass.SiteWeb = mp.siteweb;
                    membreProfilClass.Twitter = mp.twitter;
                    membreProfilClass.Instagramme = mp.instagramme;
                    membreProfilClass.Linkedin = mp.linkedin;
                    membreProfilClass.Youtube = mp.youtube;
                    membreProfilClass.Biographie = mp.Biographie;
                    membreProfilClass.DateInscription = mp.DateInscription;
                    membreProfilClass.name = mp.name;
                }
            }
        }

        public int Modifier(MembreProfil_Class membreProfilClass)
        {
            using (Connection con = new Connection())
            {
                var mp = con.MembreProfils.FirstOrDefault(x => x.MembreProfilId == membreProfilClass.MembreProfilId);
                if (mp != null)
                {
                    mp.PhotoProfil = membreProfilClass.PhotoProfil;
                    mp.facebook = membreProfilClass.Facebook;
                    mp.siteweb = membreProfilClass.SiteWeb;
                    mp.twitter = membreProfilClass.Twitter;
                    mp.instagramme = membreProfilClass.Instagramme;
                    mp.linkedin = membreProfilClass.Linkedin;
                    mp.youtube = membreProfilClass.Youtube;
                    mp.Biographie = membreProfilClass.Biographie;
                    mp.name = membreProfilClass.name;

                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }

        public int Supprimer(long membreProfilId)
        {
            using (Connection con = new Connection())
            {
                var mp = con.MembreProfils.FirstOrDefault(x => x.MembreProfilId == membreProfilId);
                if (mp != null)
                {
                    con.MembreProfils.Remove(mp);
                    try
                    {
                        return con.SaveChanges();
                    }
                    catch
                    {
                        return 0;
                    }
                }
                return 0;
            }
        }
    }
}