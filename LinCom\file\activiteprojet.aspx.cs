﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class activiteprojet : System.Web.UI.Page
    {

        private int info;
        int financementId;
        ActiviteProjet_Class actpro = new ActiviteProjet_Class();
        ActiviteProjet_Class actproj = new ActiviteProjet_Class();
        IActiviteProjet obj = new ActiviteProjetImp();
        ICommonCode co = new CommonCode();
        IPoste objpos = new PosteImp();
        Post_Class pos = new Post_Class();

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        DomaineRessource_Class actco = new DomaineRessource_Class();
        IDomaineRessource objactco = new DomaineRessourceImp();
        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        static string imge, imge1, pdfe, nameorg;
        static long ide, idorg;
        static int rolid;
        long index;
        static string nsco;

        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            financementId = Convert.ToInt32(nsco);
            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    ide = Convert.ToInt64(idperso.Value);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                InitialiserMessages();
                objpos.chargerPost(drpdprojet, -1, idorg, "projet","Selectionner le projet",0);
                // Vérifier si un ID est passé en paramètre pour l'édition
                if (nsco != null)
                {

                    btnEnregistrer.InnerText = "Modifier";
                    AfficherDetails();
                }
                else
                {
                    btnEnregistrer.InnerText = "Enregistrer";
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
        }


        protected void btn_ajouter_ServerClick(object sender, EventArgs e)
        {
            if (nsco != null)
            {
                ModifierActiviteProjet();
            }
            else
            {
                AjouterActiviteprojet();
            }
        }
        private string UploadImage(FileUpload fil, int cd)
        {
            if (fil.HasFile)
            {
                string fileName = Path.GetFileName(fil.FileName);
                string extension = Path.GetExtension(fileName).ToLower();

                string[] validExtensions = { ".png", ".jpeg", ".gif", ".jpg", ".jfif" };

                if (!validExtensions.Contains(extension))
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "La photo doit avoir ces extensions : .png, .jpg, .gif, .jpeg, .jfif. ";

                    return null;
                }

                if (fil.PostedFile.ContentLength > 104857600)
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Votre photo est trop volumineuse. Elle doit faire moins de 100MB.";

                    return null;
                }

                string path = Server.MapPath("~/file/activeproje/") + fileName;
                fil.SaveAs(path);
                imge = fileName;

            }
            else
            {
                if (nsco == null)
                {
                    imge = "No Image";
                }
                else
                {

                    obj.AfficherDetails(Convert.ToInt64(nsco), idorg,"", 0, actpro);

                    if (cd == 0)
                    { imge = actpro.photo1; }
                    else if (cd == 1)
                    { imge = actpro.photo2; }

                }
            }
            return imge;

        }


        private void AjouterActiviteprojet()
        {
            try
            {
                if (drpdprojet.SelectedValue == "-1" ||
                    string.IsNullOrEmpty(txtIntitule.Value) ||  string.IsNullOrEmpty(txtDate.Value) ||
                    drpdstatut.SelectedValue == "-1" || drpdetat.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }

                actproj.PostId = Convert.ToInt64(drpdprojet.SelectedValue);
                actproj.Titre = txtIntitule.Value;
                actproj.Description = txtdescription.Value;
                actproj.DateActivite = txtDate.Value;
                actproj.MembreId = ide;
                actproj.OrganisationId = idorg;
                actproj.photo1 =UploadImage(fileupd,0)  ;
                actproj.photo2 = UploadImage(fileupd1,1);
                actproj.statut = drpdstatut.SelectedValue;
                actproj.DateActiviteEnreg = DateTime.Now;
                actproj.name = co.GenerateSlug(txtIntitule.Value);
                actproj.summery = txtdescription.Value;
                actproj.number_of_view = 0;
                actproj.like = 0;
                actproj.dislike = 0;
                actproj.MOIS = Convert.ToDateTime(txtDate.Value).Month.ToString();
                actproj.ANNEE = Convert.ToDateTime(txtDate.Value).Year.ToString();
                actproj.DateCreation = DateTime.Now;
                actproj.etat = drpdetat.SelectedValue;

                
                info = obj.Ajouter(actproj);

                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    msg_succes.InnerText = "L'activité a été enregistré avec succès";
                    ReinitialiserFormulaire();
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de l'enregistrement de l'activité";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        private void ModifierActiviteProjet()
        {
            try
            {
                if (drpdprojet.SelectedValue == "-1" ||
                    string.IsNullOrEmpty(txtIntitule.Value) ||  string.IsNullOrEmpty(txtDate.Value) ||
                    drpdstatut.SelectedValue == "-1" || drpdetat.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }

                actproj.PostId = Convert.ToInt64(drpdprojet.SelectedValue);
                actproj.Titre = txtIntitule.Value;
                actproj.Description = txtdescription.Value;
                actproj.DateActivite =txtDate.Value;
              
                actproj.photo1 = UploadImage(fileupd, 0);
                actproj.photo2 = UploadImage(fileupd1, 1);
                actproj.statut = drpdstatut.SelectedValue;
                actproj.DateActiviteEnreg = DateTime.Now;
                actproj.name = co.GenerateSlug(txtIntitule.Value);
                actproj.summery = txtdescription.Value;
               
                actproj.MOIS = Convert.ToDateTime(txtDate.Value).Month.ToString();
                actproj.ANNEE = Convert.ToDateTime(txtDate.Value).Year.ToString();
               
                actproj.etat = drpdetat.SelectedValue;


                info = obj.Modifier(actproj, Convert.ToInt64(nsco), idorg, "", 0);

                if (info == 1)
                {
                    Response.Redirect("~/file/listactiviteprojet.aspx");
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de la modification de l'activité";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }
        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            // Vérifier la validation côté serveur
            if (!Page.IsValid)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Veuillez corriger les erreurs avant de continuer.";
                return;
            }

            if (nsco == null)
            {
                AjouterActiviteprojet();
            }
            else
            {
                ModifierActiviteProjet();
            }
        }
        private void AfficherDetails()
        {
            if (Convert.ToInt64(nsco) > 0)
            {
                obj.AfficherDetails(Convert.ToInt64(nsco),idorg,"",0, actproj);

                if (actproj.ActiviteProjetId > 0)
                {
                    drpdprojet.SelectedValue = actproj.PostId.ToString();
                    txtIntitule.Value = actproj.Titre;
                    txtdescription.Value = actproj.Description;
                    txtDate.Value = actproj.DateActivite;

                    drpdstatut.SelectedValue = actproj.statut;
                    drpdetat.SelectedValue = actproj.etat;

                }
            }
        }

        private void ReinitialiserFormulaire()
        {
            drpdprojet.SelectedValue = "-1";
            txtIntitule.Value = "";
            txtdescription.Value = "";
            txtDate.Value = "";

            drpdstatut.SelectedValue = "-1";
            drpdetat.SelectedValue = "-1";

            // Facultatif : remettre les fichiers uploadés à vide (si tu as des balises <img> pour prévisualiser)
            fileupd.Dispose();
            fileupd1.Dispose();

        }
    }
}