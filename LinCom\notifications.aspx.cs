using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class notifications : System.Web.UI.Page
    {
        private NotificationImp notificationImp = new NotificationImp();
        private long? CurrentUserId;
        private string CurrentFilter = "all";
        private int PageSize = 20;
        private int CurrentPage = 1;

        protected void Page_Load(object sender, EventArgs e)
        {
            // Vérifier si l'utilisateur est connecté
            if (Session["MembreId"] == null)
            {
                Response.Redirect("login.aspx?returnUrl=" + HttpUtility.UrlEncode(Request.Url.ToString()));
                return;
            }

            CurrentUserId = Convert.ToInt64(Session["MembreId"]);

            if (!IsPostBack)
            {
                LoadStatistics();
                LoadNotifications();
            }
        }

        private void LoadStatistics()
        {
            try
            {
                notificationImp.ChargerStatistiques(rptStats, CurrentUserId.Value);
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des statistiques : " + ex.Message);
            }
        }

        private void LoadNotifications(string filter = "all")
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var query = con.Notifications.Where(n => n.MembreId == CurrentUserId.Value);

                    // Appliquer les filtres
                    switch (filter.ToLower())
                    {
                        case "unread":
                            query = query.Where(n => n.Lu == false);
                            break;
                        case "forum":
                            query = query.Where(n => n.name == "Forum");
                            break;
                        case "badges":
                            query = query.Where(n => n.Titre.Contains("badge"));
                            break;
                        // "all" ne nécessite pas de filtre supplémentaire
                    }

                    var notifications = query.OrderByDescending(n => n.DateNotification)
                                            .Skip((CurrentPage - 1) * PageSize)
                                            .Take(PageSize)
                                            .Select(n => new
                                            {
                                                n.NotificationId,
                                                n.Titre,
                                                n.Message,
                                                n.DateNotification,
                                                n.Lu
                                            }).ToList();

                    lvNotifications.DataSource = notifications;
                    lvNotifications.DataBind();

                    // Vérifier s'il y a plus de notifications à charger
                    int totalCount = query.Count();
                    btnLoadMore.Visible = (CurrentPage * PageSize) < totalCount;
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des notifications : " + ex.Message);
            }
        }

        protected void FilterNotifications_Click(object sender, EventArgs e)
        {
            LinkButton btn = (LinkButton)sender;
            CurrentFilter = btn.CommandArgument;
            CurrentPage = 1; // Reset à la première page

            // Mettre à jour l'apparence des onglets
            UpdateTabAppearance(btn);

            // Recharger les notifications avec le nouveau filtre
            LoadNotifications(CurrentFilter);
        }

        private void UpdateTabAppearance(LinkButton activeTab)
        {
            // Retirer la classe active de tous les onglets
            lnkAll.CssClass = "filter-tab";
            lnkUnread.CssClass = "filter-tab";
            lnkForum.CssClass = "filter-tab";
            lnkBadges.CssClass = "filter-tab";

            // Ajouter la classe active à l'onglet sélectionné
            activeTab.CssClass = "filter-tab active";
        }

        protected void btnMarkAllRead_Click(object sender, EventArgs e)
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var unreadNotifications = con.Notifications.Where(n => n.MembreId == CurrentUserId.Value && n.Lu == false);
                    
                    foreach (var notification in unreadNotifications)
                    {
                        notification.Lu = true;
                    }

                    con.SaveChanges();
                    
                    LoadNotifications(CurrentFilter);
                    LoadStatistics();
                    ShowSuccess("Toutes les notifications ont été marquées comme lues.");
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la mise à jour : " + ex.Message);
            }
        }

        protected void btnDeleteSelected_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedIds = GetSelectedNotificationIds();
                if (selectedIds.Count == 0)
                {
                    ShowWarning("Veuillez sélectionner au moins une notification.");
                    return;
                }

                using (var con = new LinCom.Model.Connection())
                {
                    var notificationsToDelete = con.Notifications.Where(n => selectedIds.Contains(n.NotificationId));
                    con.Notifications.RemoveRange(notificationsToDelete);
                    con.SaveChanges();

                    LoadNotifications(CurrentFilter);
                    LoadStatistics();
                    ShowSuccess($"{selectedIds.Count} notification(s) supprimée(s).");
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la suppression : " + ex.Message);
            }
        }

        protected void btnMarkSelectedRead_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedIds = GetSelectedNotificationIds();
                if (selectedIds.Count == 0)
                {
                    ShowWarning("Veuillez sélectionner au moins une notification.");
                    return;
                }

                using (var con = new LinCom.Model.Connection())
                {
                    var notificationsToUpdate = con.Notifications.Where(n => selectedIds.Contains(n.NotificationId));
                    
                    foreach (var notification in notificationsToUpdate)
                    {
                        notification.Lu = true;
                    }

                    con.SaveChanges();

                    LoadNotifications(CurrentFilter);
                    LoadStatistics();
                    ShowSuccess($"{selectedIds.Count} notification(s) marquée(s) comme lue(s).");
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de la mise à jour : " + ex.Message);
            }
        }

        protected void btnLoadMore_Click(object sender, EventArgs e)
        {
            CurrentPage++;
            LoadNotifications(CurrentFilter);
        }

        protected void lvNotifications_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            long notificationId = Convert.ToInt64(e.CommandArgument);

            try
            {
                switch (e.CommandName)
                {
                    case "MarkRead":
                        notificationImp.MarquerCommeLue(notificationId);
                        LoadNotifications(CurrentFilter);
                        LoadStatistics();
                        ShowSuccess("Notification marquée comme lue.");
                        break;

                    case "Delete":
                        notificationImp.Supprimer(notificationId);
                        LoadNotifications(CurrentFilter);
                        LoadStatistics();
                        ShowSuccess("Notification supprimée.");
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de l'action : " + ex.Message);
            }
        }

        private List<long> GetSelectedNotificationIds()
        {
            var selectedIds = new List<long>();
            
            foreach (ListViewItem item in lvNotifications.Items)
            {
                var checkbox = item.FindControl("notification-checkbox") as CheckBox;
                if (checkbox != null && checkbox.Checked)
                {
                    // Récupérer l'ID depuis l'attribut data
                    var notificationId = ((CheckBox)checkbox).Attributes["data-notification-id"];
                    if (long.TryParse(notificationId, out long id))
                    {
                        selectedIds.Add(id);
                    }
                }
            }

            return selectedIds;
        }

        protected string GetNotificationTypeClass(string title)
        {
            if (title.Contains("badge") || title.Contains("Badge"))
                return "badge";
            if (title.Contains("réponse") || title.Contains("Réponse"))
                return "reponse";
            if (title.Contains("vote") || title.Contains("Vote"))
                return "vote";
            if (title.Contains("commentaire") || title.Contains("Commentaire"))
                return "commentaire";
            if (title.Contains("question") || title.Contains("Question"))
                return "question";

            return "default";
        }

        protected string GetNotificationIcon(string title)
        {
            if (title.Contains("badge") || title.Contains("Badge"))
                return "fas fa-medal";
            if (title.Contains("réponse") || title.Contains("Réponse"))
                return "fas fa-comment";
            if (title.Contains("vote") || title.Contains("Vote"))
                return "fas fa-thumbs-up";
            if (title.Contains("commentaire") || title.Contains("Commentaire"))
                return "fas fa-comment-dots";
            if (title.Contains("question") || title.Contains("Question"))
                return "fas fa-question-circle";
            
            return "fas fa-bell";
        }

        protected string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "à l'instant";
            if (timeSpan.TotalMinutes < 60)
                return $"il y a {(int)timeSpan.TotalMinutes} min";
            if (timeSpan.TotalHours < 24)
                return $"il y a {(int)timeSpan.TotalHours}h";
            if (timeSpan.TotalDays < 30)
                return $"il y a {(int)timeSpan.TotalDays} jours";
            
            return dateTime.ToString("dd MMM yyyy");
        }

        private void ShowError(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "error", 
                $"toastr.error('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }

        private void ShowSuccess(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "success", 
                $"toastr.success('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }

        private void ShowWarning(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "warning", 
                $"toastr.warning('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }
    }
}
