using LinCom.Classe;
using LinCom.Imp;
using LinCom.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class moderation_dashboard : System.Web.UI.Page
    {
        private FlagsImp flagsImp = new FlagsImp();
        private SujetForumImp sujetImp = new SujetForumImp();
        private long? CurrentUserId;

        protected void Page_Load(object sender, EventArgs e)
        {
            // Vérifier si l'utilisateur est connecté
            if (Session["MembreId"] == null)
            {
                Response.Redirect("login.aspx");
                return;
            }

            CurrentUserId = Convert.ToInt64(Session["MembreId"]);

            // Vérifier si l'utilisateur a les permissions de modération
            if (!ForumSecurityHelper.CanUserPerformAction(CurrentUserId.Value, "moderate"))
            {
                Response.Redirect("forum-questions.aspx");
                return;
            }

            if (!IsPostBack)
            {
                LoadModerationStats();
                LoadFlags();
                LoadReviewQueue();
                SetModeratorInfo();
            }
        }

        private void SetModeratorInfo()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var moderator = con.Membres.Where(m => m.MembreId == CurrentUserId.Value).FirstOrDefault();
                    if (moderator != null)
                    {
                        lblModeratorName.Text = $"{moderator.Nom} {moderator.Prenom}";
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des informations modérateur : " + ex.Message);
            }
        }

        private void LoadModerationStats()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    // Signalements en attente
                    int pendingFlags = con.Flags.Count(f => f.Status == "Pending");
                    lblPendingFlags.Text = pendingFlags.ToString();
                    lblFlagsCount.Text = pendingFlags.ToString();

                    // Posts à réviser (posts récents avec score négatif ou signalés)
                    int reviewQueue = con.SujetForums.Count(s => s.Score < -2 || 
                        con.Flags.Any(f => f.PostId == s.SujetForumId && f.Status == "Pending"));
                    lblReviewQueue.Text = reviewQueue.ToString();
                    lblReviewCount.Text = reviewQueue.ToString();

                    // Actions aujourd'hui
                    int todayActions = con.Flags.Count(f => f.CreatedDate.HasValue && 
                        f.CreatedDate.Value.Date == DateTime.Today && 
                        f.Status != "Pending");
                    lblTodayActions.Text = todayActions.ToString();

                    // Utilisateurs actifs
                    int activeUsers = con.Membres.Count(m => m.DerniereConnexion.HasValue && 
                        m.DerniereConnexion.Value > DateTime.Now.AddDays(-7));
                    lblActiveUsers.Text = activeUsers.ToString();
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des statistiques : " + ex.Message);
            }
        }

        private void LoadFlags()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var flags = (from f in con.Flags
                                join s in con.SujetForums on f.PostId equals s.SujetForumId
                                join m in con.Membres on f.FlaggedBy equals m.MembreId
                                where f.Status == "Pending"
                                orderby f.CreatedDate descending
                                select new
                                {
                                    f.FlagId,
                                    f.FlagType,
                                    f.Reason,
                                    f.CreatedDate,
                                    f.PostId,
                                    PostContent = s.Body,
                                    FlaggedByName = m.Nom + " " + m.Prenom,
                                    FlaggedByReputation = m.Reputation,
                                    Priority = GetFlagPriority(f.FlagType)
                                }).Take(20).ToList();

                    lvFlags.DataSource = flags;
                    lvFlags.DataBind();
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement des signalements : " + ex.Message);
            }
        }

        private void LoadReviewQueue()
        {
            try
            {
                using (var con = new LinCom.Model.Connection())
                {
                    var reviewPosts = (from s in con.SujetForums
                                      join m in con.Membres on s.OwnerUserId equals m.MembreId into memberJoin
                                      from m in memberJoin.DefaultIfEmpty()
                                      where s.Score < -2 || con.Flags.Any(f => f.PostId == s.SujetForumId && f.Status == "Pending")
                                      orderby s.CreationDate descending
                                      select new
                                      {
                                          PostId = s.SujetForumId,
                                          PostType = s.PostTypeId == 1 ? "Question" : "Réponse",
                                          Title = s.Title ?? "Réponse",
                                          Body = s.Body,
                                          Score = s.Score,
                                          CreationDate = s.CreationDate,
                                          AuthorName = m != null ? m.Nom + " " + m.Prenom : s.OwnerDisplayName,
                                          AuthorReputation = m != null ? m.Reputation : 0
                                      }).Take(15).ToList();

                    lvReview.DataSource = reviewPosts;
                    lvReview.DataBind();
                }
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors du chargement de la queue de révision : " + ex.Message);
            }
        }

        protected void SwitchTab_Click(object sender, EventArgs e)
        {
            LinkButton btn = (LinkButton)sender;
            string tab = btn.CommandArgument;

            // Masquer tous les onglets
            flagsTab.Style["display"] = "none";
            reviewTab.Style["display"] = "none";
            usersTab.Style["display"] = "none";
            historyTab.Style["display"] = "none";

            // Retirer la classe active de tous les onglets
            lnkFlags.CssClass = "mod-tab";
            lnkReview.CssClass = "mod-tab";
            lnkUsers.CssClass = "mod-tab";
            lnkHistory.CssClass = "mod-tab";

            // Afficher l'onglet sélectionné et marquer comme actif
            switch (tab)
            {
                case "flags":
                    flagsTab.Style["display"] = "block";
                    lnkFlags.CssClass = "mod-tab active";
                    LoadFlags();
                    break;
                case "review":
                    reviewTab.Style["display"] = "block";
                    lnkReview.CssClass = "mod-tab active";
                    LoadReviewQueue();
                    break;
                case "users":
                    usersTab.Style["display"] = "block";
                    lnkUsers.CssClass = "mod-tab active";
                    break;
                case "history":
                    historyTab.Style["display"] = "block";
                    lnkHistory.CssClass = "mod-tab active";
                    break;
            }
        }

        protected void lvFlags_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            long flagId = Convert.ToInt64(e.CommandArgument);

            try
            {
                switch (e.CommandName)
                {
                    case "Approve":
                        ApproveFlag(flagId);
                        ShowSuccess("Signalement approuvé.");
                        break;
                    case "Reject":
                        RejectFlag(flagId);
                        ShowSuccess("Signalement rejeté.");
                        break;
                    case "Edit":
                        long postId = Convert.ToInt64(e.CommandArgument);
                        Response.Redirect($"edit-post.aspx?id={postId}");
                        break;
                    case "Close":
                        ClosePost(Convert.ToInt64(e.CommandArgument));
                        ShowSuccess("Post fermé.");
                        break;
                }

                LoadFlags();
                LoadModerationStats();
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de l'action de modération : " + ex.Message);
            }
        }

        protected void lvReview_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            long postId = Convert.ToInt64(e.CommandArgument);

            try
            {
                switch (e.CommandName)
                {
                    case "ApprovePost":
                        ApprovePost(postId);
                        ShowSuccess("Post approuvé.");
                        break;
                    case "EditPost":
                        Response.Redirect($"edit-post.aspx?id={postId}");
                        break;
                    case "DeletePost":
                        DeletePost(postId);
                        ShowSuccess("Post supprimé.");
                        break;
                }

                LoadReviewQueue();
                LoadModerationStats();
            }
            catch (Exception ex)
            {
                ShowError("Erreur lors de l'action de révision : " + ex.Message);
            }
        }

        private void ApproveFlag(long flagId)
        {
            using (var con = new LinCom.Model.Connection())
            {
                var flag = con.Flags.Where(f => f.FlagId == flagId).FirstOrDefault();
                if (flag != null)
                {
                    flag.Status = "Approved";
                    flag.ModeratedBy = CurrentUserId.Value;
                    flag.ModeratedDate = DateTime.Now;
                    con.SaveChanges();

                    ForumSecurityHelper.LogSecurityIncident($"Flag approved: {flag.FlagType}", 
                        CurrentUserId.Value, Request.UserHostAddress);
                }
            }
        }

        private void RejectFlag(long flagId)
        {
            using (var con = new LinCom.Model.Connection())
            {
                var flag = con.Flags.Where(f => f.FlagId == flagId).FirstOrDefault();
                if (flag != null)
                {
                    flag.Status = "Rejected";
                    flag.ModeratedBy = CurrentUserId.Value;
                    flag.ModeratedDate = DateTime.Now;
                    con.SaveChanges();

                    ForumSecurityHelper.LogSecurityIncident($"Flag rejected: {flag.FlagType}", 
                        CurrentUserId.Value, Request.UserHostAddress);
                }
            }
        }

        private void ClosePost(long postId)
        {
            using (var con = new LinCom.Model.Connection())
            {
                var post = con.SujetForums.Where(s => s.SujetForumId == postId).FirstOrDefault();
                if (post != null)
                {
                    post.ClosedDate = DateTime.Now;
                    post.ClosedBy = CurrentUserId.Value;
                    con.SaveChanges();

                    ForumSecurityHelper.LogSecurityIncident($"Post closed: {postId}", 
                        CurrentUserId.Value, Request.UserHostAddress);
                }
            }
        }

        private void ApprovePost(long postId)
        {
            using (var con = new LinCom.Model.Connection())
            {
                var post = con.SujetForums.Where(s => s.SujetForumId == postId).FirstOrDefault();
                if (post != null)
                {
                    // Marquer comme approuvé (ajouter un champ si nécessaire)
                    post.LastEditDate = DateTime.Now;
                    con.SaveChanges();

                    ForumSecurityHelper.LogSecurityIncident($"Post approved: {postId}", 
                        CurrentUserId.Value, Request.UserHostAddress);
                }
            }
        }

        private void DeletePost(long postId)
        {
            using (var con = new LinCom.Model.Connection())
            {
                var post = con.SujetForums.Where(s => s.SujetForumId == postId).FirstOrDefault();
                if (post != null)
                {
                    // Soft delete
                    post.DeletionDate = DateTime.Now;
                    con.SaveChanges();

                    ForumSecurityHelper.LogSecurityIncident($"Post deleted: {postId}", 
                        CurrentUserId.Value, Request.UserHostAddress);
                }
            }
        }

        private string GetFlagPriority(string flagType)
        {
            switch (flagType?.ToLower())
            {
                case "spam":
                case "offensive":
                    return "high-priority";
                case "inappropriate":
                case "duplicate":
                    return "medium-priority";
                default:
                    return "";
            }
        }

        protected string GetPriorityClass(string priority)
        {
            return priority ?? "";
        }

        protected string GetUserInitials(string userName)
        {
            if (string.IsNullOrEmpty(userName))
                return "?";

            var parts = userName.Split(' ');
            if (parts.Length >= 2)
                return (parts[0][0].ToString() + parts[1][0].ToString()).ToUpper();
            
            return userName[0].ToString().ToUpper();
        }

        protected string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "à l'instant";
            if (timeSpan.TotalMinutes < 60)
                return $"il y a {(int)timeSpan.TotalMinutes} min";
            if (timeSpan.TotalHours < 24)
                return $"il y a {(int)timeSpan.TotalHours}h";
            if (timeSpan.TotalDays < 30)
                return $"il y a {(int)timeSpan.TotalDays} jours";
            
            return dateTime.ToString("dd MMM yyyy");
        }

        protected string TruncateText(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text))
                return "";

            if (text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength) + "...";
        }

        private void ShowError(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "error", 
                $"toastr.error('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }

        private void ShowSuccess(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "success", 
                $"toastr.success('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }

        private void ShowWarning(string message)
        {
            ClientScript.RegisterStartupScript(this.GetType(), "warning", 
                $"toastr.warning('{HttpUtility.JavaScriptStringEncode(message)}');", true);
        }
    }
}
