using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class PostViewsImp
    {
        int msg;
        PostViews p = new PostViews();

        public int Add(PostViews_Class vue)
        {
            using (Connection con = new Connection())
            {
                p.PostId = vue.PostId;
                p.UserId = vue.UserId;
                p.IPAddress = vue.IPAddress;
                p.UserAgent = vue.UserAgent;
                p.ViewDate = DateTime.Now;
                p.SessionId = vue.SessionId;
                p.ReferrerUrl = vue.ReferrerUrl;

                try
                {
                    con.PostViews.Add(p);
                    if (con.SaveChanges() == 1)
                    {
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {
                    return msg = 0;
                }
            }
        }

        public void AjouterVue(long postId, long? userId = null, string ipAddress = null, string userAgent = null, string referrer = null)
        {
            using (Connection con = new Connection())
            {
                // Vérifier si c'est une vue unique
                bool vueUnique = true;
                
                if (userId.HasValue)
                {
                    vueUnique = !con.PostViews.Any(v => v.PostId == postId && v.UserId == userId.Value);
                }
                else if (!string.IsNullOrEmpty(ipAddress))
                {
                    vueUnique = !con.PostViews.Any(v => v.PostId == postId && v.IPAddress == ipAddress && 
                                                  v.ViewDate >= DateTime.Now.AddHours(-1)); // 1 heure de délai
                }

                if (vueUnique)
                {
                    var vue = new PostViews_Class
                    {
                        PostId = postId,
                        UserId = userId,
                        IPAddress = ipAddress,
                        UserAgent = userAgent,
                        SessionId = HttpContext.Current?.Session?.SessionID,
                        ReferrerUrl = referrer
                    };

                    Add(vue);

                    // Mettre à jour le compteur de vues du post
                    var post = con.SujetForums.Where(s => s.SujetForumId == postId).FirstOrDefault();
                    if (post != null)
                    {
                        post.ViewCount++;
                        con.SaveChanges();
                    }
                }
            }
        }

        public int ObtenirNombreVues(long postId)
        {
            using (Connection con = new Connection())
            {
                return con.PostViews.Count(v => v.PostId == postId);
            }
        }

        public int ObtenirVuesUniques(long postId)
        {
            using (Connection con = new Connection())
            {
                return con.PostViews.Where(v => v.PostId == postId)
                                   .GroupBy(v => v.UserId ?? 0)
                                   .Count();
            }
        }

        public void ChargerVuesRecentes(ListView lv, long postId, int limite = 10)
        {
            using (Connection con = new Connection())
            {
                var vues = from v in con.PostViews
                          join u in con.Membres on v.UserId equals u.MembreId into userJoin
                          from u in userJoin.DefaultIfEmpty()
                          where v.PostId == postId
                          orderby v.ViewDate descending
                          select new
                          {
                              v.ViewId,
                              v.ViewDate,
                              v.IPAddress,
                              UserName = u != null ? u.Nom + " " + u.Prenom : "Anonyme",
                              v.ReferrerUrl,
                              Browser = ExtractBrowser(v.UserAgent),
                              Location = GetLocationFromIP(v.IPAddress)
                          };

                lv.DataSource = vues.Take(limite).ToList();
                lv.DataBind();
            }
        }

        public void ChargerStatistiquesVues(long postId, out PostViewsStats_Class stats)
        {
            using (Connection con = new Connection())
            {
                var post = con.SujetForums.Where(s => s.SujetForumId == postId).FirstOrDefault();
                var vues = con.PostViews.Where(v => v.PostId == postId).ToList();

                stats = new PostViewsStats_Class
                {
                    PostId = postId,
                    PostTitle = post?.Title ?? "",
                    TotalViews = vues.Count,
                    UniqueViews = vues.GroupBy(v => v.UserId ?? 0).Count(),
                    TodayViews = vues.Count(v => v.ViewDate >= DateTime.Today),
                    WeekViews = vues.Count(v => v.ViewDate >= DateTime.Now.AddDays(-7)),
                    MonthViews = vues.Count(v => v.ViewDate >= DateTime.Now.AddDays(-30)),
                    YearViews = vues.Count(v => v.ViewDate >= DateTime.Now.AddYears(-1)),
                    FirstView = vues.Any() ? vues.Min(v => v.ViewDate) : DateTime.Now,
                    LastView = vues.Any() ? vues.Max(v => v.ViewDate) : DateTime.Now
                };

                if (vues.Any())
                {
                    var totalDays = (DateTime.Now - stats.FirstView).TotalDays;
                    stats.AverageViewsPerDay = totalDays > 0 ? stats.TotalViews / totalDays : 0;
                }
            }
        }

        public void ChargerTopPostsVues(ListView lv, int limite = 20, string periode = "all")
        {
            using (Connection con = new Connection())
            {
                DateTime dateDebut = DateTime.MinValue;
                
                switch (periode.ToLower())
                {
                    case "today":
                        dateDebut = DateTime.Today;
                        break;
                    case "week":
                        dateDebut = DateTime.Now.AddDays(-7);
                        break;
                    case "month":
                        dateDebut = DateTime.Now.AddDays(-30);
                        break;
                    case "year":
                        dateDebut = DateTime.Now.AddYears(-1);
                        break;
                }

                var topPosts = from s in con.SujetForums
                              where s.PostTypeId == 1 // Questions seulement
                              let vuesPeriode = periode == "all" ? s.ViewCount : 
                                  con.PostViews.Count(v => v.PostId == s.SujetForumId && v.ViewDate >= dateDebut)
                              where vuesPeriode > 0
                              orderby vuesPeriode descending
                              select new
                              {
                                  s.SujetForumId,
                                  s.Title,
                                  s.CreationDate,
                                  s.OwnerDisplayName,
                                  TotalViews = s.ViewCount,
                                  PeriodViews = vuesPeriode,
                                  s.Score,
                                  s.AnswerCount
                              };

                lv.DataSource = topPosts.Take(limite).ToList();
                lv.DataBind();
            }
        }

        public bool EstVueUnique(long postId, long? userId, string ipAddress)
        {
            using (Connection con = new Connection())
            {
                if (userId.HasValue)
                {
                    return !con.PostViews.Any(v => v.PostId == postId && v.UserId == userId.Value);
                }
                else if (!string.IsNullOrEmpty(ipAddress))
                {
                    return !con.PostViews.Any(v => v.PostId == postId && v.IPAddress == ipAddress && 
                                            v.ViewDate >= DateTime.Now.AddHours(-1));
                }
                return true;
            }
        }

        public void ChargerVuesParPeriode(ListView lv, long postId, DateTime dateDebut, DateTime dateFin)
        {
            using (Connection con = new Connection())
            {
                var vues = from v in con.PostViews
                          where v.PostId == postId && v.ViewDate >= dateDebut && v.ViewDate <= dateFin
                          group v by v.ViewDate.Date into g
                          orderby g.Key
                          select new
                          {
                              Date = g.Key,
                              TotalViews = g.Count(),
                              UniqueViews = g.GroupBy(v => v.UserId ?? 0).Count(),
                              AnonymousViews = g.Count(v => !v.UserId.HasValue)
                          };

                lv.DataSource = vues.ToList();
                lv.DataBind();
            }
        }

        public void NettoierVuesAnciennes(DateTime dateLimit)
        {
            using (Connection con = new Connection())
            {
                var vuesAnciennes = con.PostViews.Where(v => v.ViewDate < dateLimit).ToList();
                con.PostViews.RemoveRange(vuesAnciennes);
                con.SaveChanges();
            }
        }

        private string ExtractBrowser(string userAgent)
        {
            if (string.IsNullOrEmpty(userAgent)) return "Unknown";
            
            if (userAgent.Contains("Chrome")) return "Chrome";
            if (userAgent.Contains("Firefox")) return "Firefox";
            if (userAgent.Contains("Safari")) return "Safari";
            if (userAgent.Contains("Edge")) return "Edge";
            if (userAgent.Contains("Opera")) return "Opera";
            
            return "Other";
        }

        private string GetLocationFromIP(string ipAddress)
        {
            // Implémentation basique - dans un vrai projet, utiliser un service de géolocalisation
            if (string.IsNullOrEmpty(ipAddress)) return "Unknown";
            
            // IPs locales
            if (ipAddress.StartsWith("192.168.") || ipAddress.StartsWith("10.") || ipAddress.StartsWith("127."))
                return "Local";
            
            return "External";
        }

        public void Chargement_GDV(GridView gdv)
        {
            using (Connection con = new Connection())
            {
                var obj = (from v in con.PostViews
                           join s in con.SujetForums on v.PostId equals s.SujetForumId
                           join u in con.Membres on v.UserId equals u.MembreId into userJoin
                           from u in userJoin.DefaultIfEmpty()
                           select new
                           {
                               ViewId = v.ViewId,
                               PostTitle = s.Title,
                               UserName = u != null ? u.Nom + " " + u.Prenom : "Anonyme",
                               ViewDate = v.ViewDate,
                               IPAddress = v.IPAddress,
                               Browser = ExtractBrowser(v.UserAgent)
                           }).OrderByDescending(v => v.ViewDate).ToList();

                gdv.DataSource = obj;
                gdv.DataBind();
            }
        }

        public int Count()
        {
            using (Connection con = new Connection())
            {
                return con.PostViews.Count();
            }
        }
    }
}
