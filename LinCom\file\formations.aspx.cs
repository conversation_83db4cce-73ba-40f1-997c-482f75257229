﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class formations : System.Web.UI.Page
    {
        private int info;
        int formationId;
        Formation_Class formationClass = new Formation_Class();
        DomaineFormation_Class domaineFormationClass = new DomaineFormation_Class();
        SupportFormation_Class supportFormationClass = new SupportFormation_Class();
        IFormation objFormation = new FormationImp();
        ISupportFormation objSupportFormation = new SupportFormationImp();
        ICommonCode co = new CommonCode();
        RoleMembre_Class rl = new RoleMembre_Class();
        IRoleMembre objrl = new RoleMembreImp();
        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        static string imge, imge1, pdfe, nameorg;
        static long ide, idorg;
        static int rolid;
        long index;
        static string nsco;
        DataTable dat = new DataTable();


        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];

            if (!IsPostBack)
            {
                InitialiserMessages();
                ChargerDomaines();

                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    ide = Convert.ToInt64(idperso.Value);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;
                // Vérifier si un ID est passé en paramètre pour l'édition
                if (ViewState["Record"] == null)
                {//depense
                    dat.Columns.Add("#Code");
                    dat.Columns.Add("Domaine d'Intervention");

                    ViewState["Record"] = dat;
                }

                if (Request.QueryString["id"] != null)
                {
                    formationId = Convert.ToInt32(Request.QueryString["id"]);
                    btn_ajouter.InnerText = "Modifier";
                    AfficherDetails();
                }
                else
                {
                    btn_ajouter.InnerText = "Ajouter";
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
        }

        private void ChargerDomaines()
        {
            // Utilisation de la méthode implémentée dans FormationImp
          //  objFormation.ChargerDomaines(drpddomain);
        }

        protected void btn_ajouter_ServerClick(object sender, EventArgs e)
        {
            if (Request.QueryString["id"] != null)
            {
                ModifierFormation();
            }
            else
            {
                AjouterFormation();
            }
        }

        protected void btn_ajouter_domaine_ServerClick(object sender, EventArgs e)
        {
            //AjouterDomaineFormation();
        }

        private void AjouterFormation()
        {
            try
            {
                if (string.IsNullOrEmpty(txtTitre.Value) || string.IsNullOrEmpty(txtDatePublication.Value) ||
                    string.IsNullOrEmpty(txtDescription.Value) || drpdstatutforma.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }

                formationClass.Titre = txtTitre.Value;
                formationClass.DatePublication = Convert.ToDateTime(txtDatePublication.Value).ToString();
                formationClass.Description = txtDescription.Value;
                formationClass.statut = drpdstatutforma.SelectedValue;
                formationClass.name = co.GenerateSlug(txtTitre.Value);


                formationClass.DateFormation = Convert.ToDateTime(txtDatePublication.Value);

                formationClass.MembreId =ide;

                formationClass.OrganisationId = idorg;
                formationClass.MOIS = Convert.ToDateTime(txtDatePublication.Value).Month.ToString();
                formationClass.ANNEE = Convert.ToDateTime(txtDatePublication.Value).Year.ToString();

                info = objFormation.Ajouter(formationClass);

                if (info == 1)
                {
                    // Si un support est spécifié, l'ajouter également
                    if (!string.IsNullOrEmpty(txtTitreSupport.Value))
                    {
                        AjouterSupportFormation();
                    }

                    div_msg_succes.Visible = true;
                    msg_succes.InnerText = "La formation a été enregistrée avec succès";
                    ReinitialiserFormulaire();
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de l'enregistrement de la formation";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

      
        private void AjouterSupportFormation()
        {
            try
            {
                if (string.IsNullOrEmpty(txtTitreSupport.Value) || drpdstatut.SelectedValue == "-1")
                {
                    return; // Ne pas ajouter de support si les champs ne sont pas remplis
                }

                supportFormationClass.Titre = txtTitreSupport.Value;
                supportFormationClass.Fichier = txtLienSupport.Value;
                supportFormationClass.statut = drpdstatut.SelectedValue;
                supportFormationClass.FormationId = formationId;
                supportFormationClass.name = co.GenerateSlug(txtTitreSupport.Value);

                objSupportFormation.Ajouter(supportFormationClass);

                txtTitreSupport.Value = string.Empty;
                txtLienSupport.Value = string.Empty;
                drpdstatut.SelectedValue = "-1";
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur lors de l'ajout du support: " + ex.Message;
            }
        }

        private void ModifierFormation()
        {
            try
            {
                if (string.IsNullOrEmpty(txtTitre.Value) || string.IsNullOrEmpty(txtDatePublication.Value) ||
                    string.IsNullOrEmpty(txtDescription.Value) || drpdstatutforma.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }

                formationClass.Titre = txtTitre.Value;
                formationClass.DatePublication = Convert.ToDateTime(txtDatePublication.Value).ToString();
                formationClass.Description = txtDescription.Value;
                formationClass.statut = drpdstatutforma.SelectedValue;
                formationClass.name = co.GenerateSlug(txtTitre.Value);


                formationClass.DateFormation = Convert.ToDateTime(txtDatePublication.Value);

                formationClass.MembreId = ide;

                formationClass.OrganisationId = idorg;
                formationClass.MOIS = Convert.ToDateTime(txtDatePublication.Value).Month.ToString();
                formationClass.ANNEE = Convert.ToDateTime(txtDatePublication.Value).Year.ToString();


                info = objFormation.Modifier(formationClass, Convert.ToInt64(nsco),idorg);

                if (info == 1)
                {
                    Response.Redirect("~/file/listformations.aspx");
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de la modification de la formation";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        private void AfficherDetails()
        {
            if (formationId > 0)
            {
                objFormation.AfficherDetails(Convert.ToInt64(nsco),idorg, formationClass);

                if (formationClass.FormationId > 0)
                {
                    txtTitre.Value = formationClass.Titre;
                    txtDatePublication.Value = formationClass.DateFormation?.ToString("yyyy-MM-dd") ?? string.Empty;
                    txtDescription.Value = formationClass.Description;
                    drpdstatutforma.SelectedValue = formationClass.statut;
                }
            }
        }

        private void ReinitialiserFormulaire()
        {
            txtTitre.Value = string.Empty;
            txtDatePublication.Value = string.Empty;
            txtDescription.Value = string.Empty;
            txtTitreSupport.Value = string.Empty;
            txtLienSupport.Value = string.Empty;
            drpdstatutforma.SelectedValue = "-1";
            drpdstatut.SelectedValue = "-1";
            //drpddomain.SelectedValue = "-1";
        }
    }
}