﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IDomaineIntervention
    {
        void AfficherDetails(int idDomaine, DomaineIntervention_Class domaine);
        int Ajouter(DomaineIntervention_Class domaine);
        int Modifier(int id,DomaineIntervention_Class domaine);
        int Supprimer(int idDomaine);
        void ChargerDomaines(GridView gdv);
        void ChargerDomainedisponible(DropDownList ddl);
        void AfficherDetails(string code, DomaineIntervention_Class domaineClass,int cd);
        void ChargerDomaines(ListView gdv);

    }
}
