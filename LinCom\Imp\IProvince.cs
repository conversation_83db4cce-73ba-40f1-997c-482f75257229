﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IProvince
    {
        int add(Province_Class add);
        void Chargement_GDV(GridView GV_apv);
        void search(GridView GV_apv, string code);
        void afficherDetails(int code, Province_Class pr);
        void afficherDetails(string code, Province_Class pr);
        int edit(Province_Class cl, int id);
        int supprimer(int id);
       
        void chargerProvince(DropDownList lst);
        int count();
    }
}
