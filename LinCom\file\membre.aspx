﻿<%@ Page Title="" Language="C#" ValidateRequest="false" MasterPageFile="~/file/OfficeMaster.Master" AutoEventWireup="true" CodeBehind="membre.aspx.cs" Inherits="LinCom.file.membre" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="breadcomb-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="breadcomb-list">
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="breadcomb-wp">

                                    <div class="breadcomb-ctn">
                                        <h2>Membres</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-3">
                                <div class="breadcomb-report">

                                    <a data-toggle="tooltip" data-placement="left" href="listmembre.aspx" title="Clique sur ce button pour visualiser la liste des membres" class="btn">Liste des membres</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcomb area End-->
    <!-- Form Element area Start-->
    <div class="form-element-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="form-element-list">

                        <div class="cmp-tb-hd bcs-hd">
                            <p style="color: red">Remplissez les champs obligatoires avec asterix</p>
                        </div>
                        <div class="alert-list">
                            <asp:Panel ID="div_msg_succes" runat="server" CssClass="alert alert-success alert-dismissible" Visible="false">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <asp:Label ID="msg_succes" runat="server" Text="Enregistrement réussi"></asp:Label>
                            </asp:Panel>
                            <asp:Panel ID="div_msg_error" runat="server" CssClass="alert alert-danger alert-dismissible" Visible="false">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true"><i class="notika-icon notika-close"></i></span></button>
                                <asp:Label ID="msg_error" runat="server" Text="Enregistrement échoué"></asp:Label>
                            </asp:Panel>
                        </div>
                        <div class="row">
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-house"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Nom *</label>
                                        <asp:TextBox ID="txtNom" runat="server" CssClass="form-control" placeholder="Nom *"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvNom" runat="server" ControlToValidate="txtNom"
                                            ErrorMessage="Le nom est requis" ForeColor="Red" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-house"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Prénom *</label>
                                        <asp:TextBox ID="txtPrenom" runat="server" CssClass="form-control" placeholder="Prénom *"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvPrenom" runat="server" ControlToValidate="txtPrenom"
                                            ErrorMessage="Le prénom est requis" ForeColor="Red" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-house"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Genre *</label>
                                        <asp:DropDownList class="form-control" ID="drpdsexe" runat="server">
                                            <asp:ListItem Value="-1">Selectionner le genre</asp:ListItem>
                                            <asp:ListItem Value="Masculin">Masculin</asp:ListItem>
                                            <asp:ListItem Value="Féminin">Féminin</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-calendar"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Date de Naissance</label>
                                        <asp:TextBox ID="txtDateNaissance" runat="server" CssClass="form-control" TextMode="Date" placeholder="Date de Naissance *"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvDateNaissance" runat="server" ControlToValidate="txtDateNaissance"
                                            ErrorMessage="La date de naissance est requise" ForeColor="Red" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                            </div>



                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-phone"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Numéro de Téléphone *</label>
                                        <asp:TextBox ID="txtTelephone" runat="server" CssClass="form-control" placeholder="Numéro de Téléphone"></asp:TextBox>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-map"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Province de résidence *</label>
                                        <asp:DropDownList class="form-control" ID="drpdprov" runat="server" AutoPostBack="true" OnSelectedIndexChanged="drpdprov_SelectedIndexChanged">
                                            <asp:ListItem Value="-1">Selectionner la Province</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-map"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Commune de résidence *</label>
                                        <asp:DropDownList class="form-control" ID="drpdcom" runat="server">
                                            <asp:ListItem Value="-1">Selectionner la Commune</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-map"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <label>Adresse complète de résidence</label>
                                        <asp:TextBox ID="txtAdresse" runat="server" CssClass="form-control" placeholder="Adresse complète"></asp:TextBox>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-map"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Poste occupé du Membre</label>
                                        <asp:TextBox ID="txtposte" runat="server" CssClass="form-control" placeholder="Poste du membre"></asp:TextBox>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-map"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Role du Membre</label>
                                        <asp:DropDownList class="form-control" ID="drpdrole" runat="server">
                                            <asp:ListItem Value="-1">Selectionner le role</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>


                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-calendar"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Date d'Adhesion</label>
                                        <asp:TextBox ID="txtDateAdhesion" runat="server" CssClass="form-control" TextMode="Date" placeholder="Date d'adhesion"></asp:TextBox>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-mail"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Adresse E-mail du Membre</label>
                                        <asp:TextBox ID="txtEmail" runat="server" CssClass="form-control" placeholder="Adresse E-mail" TextMode="Email"></asp:TextBox>
                                        <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txtEmail"
                                            ErrorMessage="Format d'email invalide" ForeColor="Red" Display="Dynamic"
                                            ValidationExpression="^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$"></asp:RegularExpressionValidator>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-mail"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Confirmer Adresse E-mail du Membre</label>
                                        <asp:TextBox ID="txtConfirmEmail" runat="server" CssClass="form-control" placeholder="Confirmer l'Adresse E-mail" TextMode="Email"></asp:TextBox>
                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ControlToValidate="txtConfirmEmail"
                                            ErrorMessage="Format d'email invalide" ForeColor="Red" Display="Dynamic"
                                            ValidationExpression="^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$"></asp:RegularExpressionValidator>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-mail"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Mot de passe du Membre</label>
                                        <asp:TextBox ID="txtpswd" runat="server" CssClass="form-control" placeholder="Mot de passe" TextMode="Password"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtDateNaissance"
     ErrorMessage="Le mot de passe est requis" ForeColor="Red" Display="Dynamic"></asp:RequiredFieldValidator>
                                  
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-mail"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Confirmer le mot de passe du Membre</label>
                                        <asp:TextBox ID="txtconfirmpswd" runat="server" CssClass="form-control" placeholder="Confirmer le Mot de passe" TextMode="Password"></asp:TextBox>
                                                                           <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txtDateNaissance"
ErrorMessage="La confirmation du mot de passe est requise" ForeColor="Red" Display="Dynamic"></asp:RequiredFieldValidator>
                                  
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-edit"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Statut du Membre</label>
                                        <asp:DropDownList class="form-control" ID="drpdstatut" runat="server">

                                            <asp:ListItem Value="-1">Selectionner le statut</asp:ListItem>
                                            <asp:ListItem Value="actif">Activé</asp:ListItem>
                                            <asp:ListItem Value="inactif">Desactivé</asp:ListItem>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-picture"></i>
                                    </div>
                                    <div class="nk-int-st">
                                         <label>Photo du Membre</label>
                                        <asp:FileUpload ID="fileupd" runat="server" class="form-control" />

                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <div class="form-group ic-cmp-int">
                                    <div class="form-ic-cmp">
                                        <i class="notika-icon notika-form"></i>
                                    </div>
                                    <div class="nk-int-st">
                                        <div>
                                             <label>Profil du Membre</label>
                                            <textarea class="html-editor" runat="server" id="txtBiographie" rows="10"></textarea>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                <button type="button" runat="server" id="btnEnregistrer" onserverclick="btnEnregistrer_ServerClick" class="btn btn-success notika-gp-success">Ajouter</button>
                            </div>

                        </div>

                    </div>
                </div>
            </div>

        </div>
    </div>
    <!-- Form Element area End-->
</asp:Content>
