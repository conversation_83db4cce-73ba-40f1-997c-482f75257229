﻿using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class domaineintervention : System.Web.UI.Page
    {

        private int info;
        string nscno;
        DomaineIntervention_Class prov = new  DomaineIntervention_Class();
         DomaineIntervention_Class pro = new  DomaineIntervention_Class();
        IDomaineIntervention obj = new DomaineInterventionImp();
        ICommonCode co = new CommonCode();
        static string typenm; static int id, idpers, rol;
        protected void Page_Load(object sender, EventArgs e)
        {
            nscno = Request.QueryString["name"];
            if (!IsPostBack)
            {

                initial_msg();
                if (nscno == null)
                {
                    btn_enreg.InnerText = "Enregistrer";
                    // Response.Redirect("~/sima/typeorganisation.aspx/");
                }
                else

                {
                    btn_enreg.InnerText = "Modifier";
                    afficher();
                }

            }
        }
        private void initial_msg()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;

        }
        public string Generatename(string title)
        {
            return title.ToLower()
                        .Replace("-", "")
                        .Replace(",", "")
                        .Replace("'", "")
                        .Replace("?", "")
                        .Replace("!", "")
                        .Replace(".", "")
                        .Replace(":", "")
                        .Replace(" ", "");
        }
        public void adPrv()
        {
            try
            {
                if (txtnm.Value == "")
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";
                }
                else
                {
                    prov.name = co.GenerateSlug(txtnm.Value);
                    prov.Libelle = txtnm.Value;
                    info = obj.Ajouter(prov);
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listdomaineintervention.aspx");

                    }
                    else
                    {
                        div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Ce domaine d'intervention Existe deja";

                    }


                }

            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "ce domaine d'intervention Existe deja";

            }

            //LabelMsg.InnerText = obj.ajouterProvince(prov).ToString();
        }

        public void upte()
        {
            try
            {
                if (txtnm.Value == "")
                {
                    div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "Renseignez tous les champs";
                }
                else
                {
                    prov.name = co.GenerateSlug(txtnm.Value);
                    prov.Libelle = txtnm.Value;
                    obj.AfficherDetails(nscno, pro,0);
                    info = obj.Modifier(Convert.ToInt32(pro.DomaineInterventionId), prov);
                    if (info == 1)
                    {
                        Response.Redirect("~/file/listdomaineintervention.aspx");
                    }
                    else
                    {
                        Response.Redirect("~/file/listdomaineintervention.aspx");

                    }
                }

            }
            catch (SqlException e)
            {
                div_msg_succes.Visible = false; div_msg_error.Visible = true; msg_error.InnerText = "ce domaine d'intervention Existe deja";

            }
        }

        protected void btn_enreg_ServerClick(object sender, EventArgs e)
        {
            if (nscno == null)
            {
                adPrv();

            }
            else
                upte();
        }


        protected void afficher()
        {
            if (nscno != null)
            {
                obj.AfficherDetails(nscno, prov,0);
                txtnm.Value = prov.Libelle;

            }
        }


    }
}