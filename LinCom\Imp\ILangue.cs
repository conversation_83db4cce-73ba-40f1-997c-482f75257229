﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface ILangue
    {
        void AfficherDetails(int idLangue, Langue_Class langueClass);
        int Ajouter(Langue_Class langueClass);
        void ChargerLangues(GridView gdv, bool actifSeulement = true);
        void chargerLangues(DropDownList lst);
        int Modifier(Langue_Class langueClass);
        int Supprimer(int idLangue);
    }
}
