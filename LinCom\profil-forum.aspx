<%@ Page Title="Profil Forum - LinCom" Language="C#" MasterPageFile="~/MasterPage.Master" AutoEventWireup="true" CodeBehind="profil-forum.aspx.cs" Inherits="LinCom.profil_forum" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="Content/forum-lincom.css" rel="stylesheet" />
    <style>
        .profile-header {
            background: var(--lincom-gradient-primary);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
            border-bottom: 4px solid var(--lincom-accent);
            position: relative;
            overflow: hidden;
        }

        .profile-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: white;
            color: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            font-weight: bold;
            margin: 0 auto 1rem auto;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .reputation-score {
            font-size: 2.5rem;
            font-weight: bold;
            color: #28a745;
            text-align: center;
        }
        
        .reputation-label {
            text-align: center;
            color: #586069;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border: 1px solid #e1e4e8;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #0366d6;
            display: block;
        }
        
        .stat-label {
            color: #586069;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .badges-section {
            background: white;
            border: 1px solid #e1e4e8;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .badge-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            border-bottom: 1px solid #f1f3f4;
            transition: background 0.2s ease;
        }
        
        .badge-item:hover {
            background: #f6f8fa;
        }
        
        .badge-item:last-child {
            border-bottom: none;
        }
        
        .badge-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }
        
        .badge-gold { background: #ffd700; }
        .badge-silver { background: #c0c0c0; }
        .badge-bronze { background: #cd7f32; }
        
        .badge-info {
            flex: 1;
        }
        
        .badge-name {
            font-weight: 600;
            color: #24292e;
        }
        
        .badge-description {
            color: #586069;
            font-size: 0.9rem;
        }
        
        .badge-date {
            color: #586069;
            font-size: 0.8rem;
        }
        
        .activity-timeline {
            background: white;
            border: 1px solid #e1e4e8;
            border-radius: 8px;
            padding: 1.5rem;
        }
        
        .timeline-item {
            display: flex;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .timeline-item:last-child {
            border-bottom: none;
        }
        
        .timeline-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            color: white;
            flex-shrink: 0;
        }
        
        .timeline-content {
            flex: 1;
        }
        
        .timeline-action {
            font-weight: 500;
            color: #24292e;
        }
        
        .timeline-details {
            color: #586069;
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }
        
        .timeline-time {
            color: #586069;
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }
        
        .reputation-chart {
            background: white;
            border: 1px solid #e1e4e8;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #586069;
        }
        
        .tabs-container {
            background: white;
            border: 1px solid #e1e4e8;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .tabs-nav {
            display: flex;
            background: #f6f8fa;
            border-bottom: 1px solid #e1e4e8;
        }
        
        .tab-btn {
            flex: 1;
            padding: 1rem;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 500;
            color: #586069;
            transition: all 0.2s ease;
        }
        
        .tab-btn.active {
            background: white;
            color: #0366d6;
            border-bottom: 2px solid #0366d6;
        }
        
        .tab-btn:hover {
            background: #e1e4e8;
        }
        
        .tab-content {
            padding: 1.5rem;
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .post-item {
            padding: 1rem 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .post-item:last-child {
            border-bottom: none;
        }
        
        .post-title {
            font-weight: 500;
            color: #0366d6;
            text-decoration: none;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .post-title:hover {
            text-decoration: underline;
        }
        
        .post-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.9rem;
            color: #586069;
        }
        
        .post-score {
            color: #28a745;
            font-weight: 500;
        }
        
        .level-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .level-badge {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .level-progress {
            flex: 1;
            height: 8px;
            background: #e1e4e8;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .level-progress-bar {
            height: 100%;
            background: linear-gradient(45deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        
        .privileges-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .privilege-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .privilege-item:last-child {
            border-bottom: none;
        }
        
        .privilege-icon {
            color: #28a745;
        }
        
        .privilege-text {
            color: #24292e;
        }
        
        .privilege-rep {
            margin-left: auto;
            color: #586069;
            font-size: 0.9rem;
        }

        /* Classes pour les icônes d'activité */
        .activity-icon-question {
            background: var(--lincom-secondary) !important;
        }

        .activity-icon-answer {
            background: var(--lincom-success) !important;
        }

        .activity-icon-vote {
            background: var(--lincom-warning) !important;
        }

        .activity-icon-badge {
            background: var(--lincom-accent) !important;
        }

        .activity-icon-default {
            background: var(--lincom-primary) !important;
        }

        /* Classes pour les icônes de réputation */
        .reputation-icon-positive {
            background: var(--lincom-success) !important;
        }

        .reputation-icon-negative {
            background: var(--lincom-accent) !important;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Header du Profil -->
    <div class="profile-header">
        <div class="container">
            <div class="row">
                <div class="col-md-3 text-center">
                    <div class="profile-avatar">
                        <asp:Label ID="lblUserInitials" runat="server"></asp:Label>
                    </div>
                </div>
                <div class="col-md-6">
                    <h1><asp:Label ID="lblUserName" runat="server"></asp:Label></h1>
                    <p class="lead">
                        Membre depuis <asp:Label ID="lblMemberSince" runat="server"></asp:Label>
                    </p>
                    <div class="level-indicator">
                        <div class="level-badge">
                            <asp:Label ID="lblUserLevel" runat="server"></asp:Label>
                        </div>
                        <div class="level-progress">
                            <div class="level-progress-bar" style="width: 65%"></div>
                        </div>
                        <small class="text-light">
                            <asp:Label ID="lblNextLevel" runat="server"></asp:Label>
                        </small>
                    </div>
                </div>
                <div class="col-md-3 text-center">
                    <div class="reputation-score">
                        <asp:Label ID="lblReputation" runat="server"></asp:Label>
                    </div>
                    <div class="reputation-label">Points de réputation</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- Statistiques -->
            <div class="col-md-12">
                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number">
                            <asp:Label ID="lblQuestionCount" runat="server"></asp:Label>
                        </span>
                        <div class="stat-label">Questions posées</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">
                            <asp:Label ID="lblAnswerCount" runat="server"></asp:Label>
                        </span>
                        <div class="stat-label">Réponses données</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">
                            <asp:Label ID="lblAcceptedAnswers" runat="server"></asp:Label>
                        </span>
                        <div class="stat-label">Réponses acceptées</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">
                            <asp:Label ID="lblTotalViews" runat="server"></asp:Label>
                        </span>
                        <div class="stat-label">Vues totales</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">
                            <asp:Label ID="lblBadgeCount" runat="server"></asp:Label>
                        </span>
                        <div class="stat-label">Badges obtenus</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">
                            <asp:Label ID="lblVotesGiven" runat="server"></asp:Label>
                        </span>
                        <div class="stat-label">Votes donnés</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Contenu Principal -->
            <div class="col-md-8">
                <!-- Graphique de Réputation -->
                <div class="reputation-chart">
                    <h4><i class="fas fa-chart-line"></i> Évolution de la réputation</h4>
                    <div class="chart-container">
                        <canvas id="reputationChart" width="600" height="300"></canvas>
                    </div>
                </div>

                <!-- Onglets d'Activité -->
                <div class="tabs-container">
                    <div class="tabs-nav">
                        <button class="tab-btn active" data-tab="questions">
                            <i class="fas fa-question-circle"></i> Questions
                        </button>
                        <button class="tab-btn" data-tab="answers">
                            <i class="fas fa-comment"></i> Réponses
                        </button>
                        <button class="tab-btn" data-tab="activity">
                            <i class="fas fa-history"></i> Activité
                        </button>
                        <button class="tab-btn" data-tab="reputation">
                            <i class="fas fa-trophy"></i> Réputation
                        </button>
                    </div>

                    <!-- Onglet Questions -->
                    <div class="tab-content active" id="tab-questions">
                        <asp:ListView ID="lvUserQuestions" runat="server">
                            <LayoutTemplate>
                                <div id="itemPlaceholder" runat="server"></div>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <div class="post-item">
                                    <a href="question-detail.aspx?id=<%# Eval("SujetForumId") %>" class="post-title">
                                        <%# Eval("Title") %>
                                    </a>
                                    <div class="post-meta">
                                        <span class="post-score">
                                            <i class="fas fa-arrow-up"></i> <%# Eval("Score") %>
                                        </span>
                                        <span>
                                            <i class="fas fa-comment"></i> <%# Eval("AnswerCount") %> réponses
                                        </span>
                                        <span>
                                            <i class="fas fa-eye"></i> <%# Eval("ViewCount") %> vues
                                        </span>
                                        <span>
                                            <%# GetTimeAgo(Convert.ToDateTime(Eval("CreationDate"))) %>
                                        </span>
                                    </div>
                                </div>
                            </ItemTemplate>
                            <EmptyDataTemplate>
                                <div class="text-center py-4">
                                    <i class="fas fa-question-circle fa-2x text-muted mb-3"></i>
                                    <p class="text-muted">Aucune question posée pour le moment.</p>
                                </div>
                            </EmptyDataTemplate>
                        </asp:ListView>
                    </div>

                    <!-- Onglet Réponses -->
                    <div class="tab-content" id="tab-answers">
                        <asp:ListView ID="lvUserAnswers" runat="server">
                            <LayoutTemplate>
                                <div id="itemPlaceholder" runat="server"></div>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <div class="post-item">
                                    <a href="question-detail.aspx?id=<%# Eval("QuestionId") %>" class="post-title">
                                        <%# Eval("QuestionTitle") %>
                                    </a>
                                    <div class="post-meta">
                                        <span class="post-score">
                                            <i class="fas fa-arrow-up"></i> <%# Eval("Score") %>
                                        </span>
                                        <%# Convert.ToBoolean(Eval("IsAccepted")) ? "<span class='text-success'><i class='fas fa-check'></i> Acceptée</span>" : "" %>
                                        <span>
                                            <%# GetTimeAgo(Convert.ToDateTime(Eval("CreationDate"))) %>
                                        </span>
                                    </div>
                                </div>
                            </ItemTemplate>
                            <EmptyDataTemplate>
                                <div class="text-center py-4">
                                    <i class="fas fa-comment fa-2x text-muted mb-3"></i>
                                    <p class="text-muted">Aucune réponse donnée pour le moment.</p>
                                </div>
                            </EmptyDataTemplate>
                        </asp:ListView>
                    </div>

                    <!-- Onglet Activité -->
                    <div class="tab-content" id="tab-activity">
                        <div class="activity-timeline">
                            <asp:ListView ID="lvUserActivity" runat="server">
                                <LayoutTemplate>
                                    <div id="itemPlaceholder" runat="server"></div>
                                </LayoutTemplate>
                                <ItemTemplate>
                                    <div class="timeline-item">
                                        <div class="timeline-icon activity-icon-<%# GetActivityType(Eval("ActionType").ToString()) %>">
                                            <i class="<%# GetActivityIcon(Eval("ActionType").ToString()) %>"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="timeline-action"><%# Eval("ActionDescription") %></div>
                                            <div class="timeline-details"><%# Eval("Details") %></div>
                                            <div class="timeline-time"><%# GetTimeAgo(Convert.ToDateTime(Eval("ActionDate"))) %></div>
                                        </div>
                                    </div>
                                </ItemTemplate>
                                <EmptyDataTemplate>
                                    <div class="text-center py-4">
                                        <i class="fas fa-history fa-2x text-muted mb-3"></i>
                                        <p class="text-muted">Aucune activité récente.</p>
                                    </div>
                                </EmptyDataTemplate>
                            </asp:ListView>
                        </div>
                    </div>

                    <!-- Onglet Réputation -->
                    <div class="tab-content" id="tab-reputation">
                        <asp:ListView ID="lvReputationHistory" runat="server">
                            <LayoutTemplate>
                                <div id="itemPlaceholder" runat="server"></div>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <div class="timeline-item">
                                    <div class="timeline-icon reputation-icon-<%# Convert.ToInt32(Eval("ReputationChange")) > 0 ? "positive" : "negative" %>">
                                        <%# Convert.ToInt32(Eval("ReputationChange")) > 0 ? "+" : "" %><%# Eval("ReputationChange") %>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-action"><%# Eval("Description") %></div>
                                        <div class="timeline-time"><%# GetTimeAgo(Convert.ToDateTime(Eval("CreationDate"))) %></div>
                                    </div>
                                </div>
                            </ItemTemplate>
                            <EmptyDataTemplate>
                                <div class="text-center py-4">
                                    <i class="fas fa-trophy fa-2x text-muted mb-3"></i>
                                    <p class="text-muted">Aucun changement de réputation.</p>
                                </div>
                            </EmptyDataTemplate>
                        </asp:ListView>
                    </div>
                </div>
            </div>

            <!-- Sidebar Droite -->
            <div class="col-md-4">
                <!-- Badges -->
                <div class="badges-section">
                    <h4><i class="fas fa-medal"></i> Badges</h4>
                    <asp:ListView ID="lvUserBadges" runat="server">
                        <LayoutTemplate>
                            <div id="itemPlaceholder" runat="server"></div>
                        </LayoutTemplate>
                        <ItemTemplate>
                            <div class="badge-item">
                                <div class="badge-icon <%# GetBadgeClass(Convert.ToInt32(Eval("Class"))) %>">
                                    <i class="fas fa-medal"></i>
                                </div>
                                <div class="badge-info">
                                    <div class="badge-name"><%# Eval("Name") %></div>
                                    <div class="badge-description"><%# Eval("Description") %></div>
                                    <div class="badge-date"><%# GetTimeAgo(Convert.ToDateTime(Eval("Date"))) %></div>
                                </div>
                            </div>
                        </ItemTemplate>
                        <EmptyDataTemplate>
                            <div class="text-center py-4">
                                <i class="fas fa-medal fa-2x text-muted mb-3"></i>
                                <p class="text-muted">Aucun badge obtenu pour le moment.</p>
                            </div>
                        </EmptyDataTemplate>
                    </asp:ListView>
                </div>

                <!-- Privilèges -->
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-key"></i> Privilèges</h6>
                    </div>
                    <div class="card-body">
                        <ul class="privileges-list">
                            <asp:ListView ID="lvUserPrivileges" runat="server">
                                <LayoutTemplate>
                                    <div id="itemPlaceholder" runat="server"></div>
                                </LayoutTemplate>
                                <ItemTemplate>
                                    <li class="privilege-item">
                                        <i class="fas fa-check privilege-icon"></i>
                                        <span class="privilege-text"><%# Eval("PrivilegeName") %></span>
                                        <span class="privilege-rep"><%# Eval("RequiredReputation") %></span>
                                    </li>
                                </ItemTemplate>
                            </asp:ListView>
                        </ul>
                    </div>
                </div>

                <!-- Tags Favoris -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-tags"></i> Tags les plus utilisés</h6>
                    </div>
                    <div class="card-body">
                        <asp:ListView ID="lvUserTags" runat="server">
                            <LayoutTemplate>
                                <div id="itemPlaceholder" runat="server"></div>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <a href="questions-tag.aspx?tag=<%# Eval("TagName") %>" class="tag mb-1">
                                    <%# Eval("TagName") %>
                                    <span class="badge badge-light"><%# Eval("Count") %></span>
                                </a>
                            </ItemTemplate>
                        </asp:ListView>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Gestion des onglets
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Désactiver tous les onglets
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // Activer l'onglet cliqué
                this.classList.add('active');
                document.getElementById('tab-' + this.dataset.tab).classList.add('active');
            });
        });

        // Graphique de réputation
        const ctx = document.getElementById('reputationChart').getContext('2d');
        const reputationChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
                datasets: [{
                    label: 'Réputation',
                    data: [1, 15, 45, 78, 120, 150],
                    borderColor: '#0366d6',
                    backgroundColor: 'rgba(3, 102, 214, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
</asp:Content>
