﻿using LinCom.Class;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LinCom.Imp
{
    internal interface IPolitiqueConfidentialite
    {
        void AfficherDetails(int idCondition, PolitiqueConfidentialite_Class conditionClass);
        int Ajouter(PolitiqueConfidentialite_Class conditionClass);
        void AfficherDetails(string code, PolitiqueConfidentialite_Class conditionClass);
        int Modifier(PolitiqueConfidentialite_Class conditionClass, int idCondition);
        int ID();
        int countID();
    }
}
