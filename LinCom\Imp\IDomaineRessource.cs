﻿using LinCom.Classe;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    internal interface IDomaineRessource
    {
        int Ajout(DomaineRessource_Class add);
        void search(GridView GV_apv, string code, long id, long idorg, long codere, int cd);
        void afficherDetails(long id, long idorg, long codere, int cd, DomaineRessource_Class pr);
        void afficherDetails(string code, DomaineRessource_Class pr);
        int edit(DomaineRessource_Class cl, long id, long idorg);
        int supprimer(long id, long idorg);
        void Chargement_GDV(GridView GV_apv, string code, long id, long idorg, long codere, int cd);
        void chargerDomainRessourc(DropDownList lst);
        int count();
    }
}
