﻿using LinCom.Classe;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace LinCom.Imp
{
    public class DomaineRessourceImp : IDomaineRessource
    {
        int msg;
        DomaineRessource p = new DomaineRessource();
        public int Ajout(DomaineRessource_Class add)
        {
            using (Connection con = new Connection())
            {
                p.RessourceId = add.RessourceId;
                p.DomaineInterventionId = add.DomaineInterventionId;
                p.OrganisationId = add.OrganisationId;
                p.DateCreation = add.DateCreation;
                p.statut = add.statut;
                p.MembreId = add.MembreId;

                try
                {
                    con.DomaineRessources.Add(p);

                    if (con.SaveChanges() == 1)
                    {
                        con.DomaineRessources.Add(p);
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }


            }
            return msg;
        }

        public void Chargement_GDV(GridView GV_apv, string code, long id, long idorg, long codere,int cd)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    var obj = (from ep in con.DomaineRessources
                               join r in con.Ressources on ep.RessourceId equals r.RessourceId
                               join d in con.DomaineInterventionOrganisations on ep.DomaineInterventionId equals d.DomaineInterventionOrganisationId
                               join dom in con.DomaineInterventions on d.DomaineInterventionId equals dom.DomaineInterventionId
                               join m in con.Membres on ep.MembreId equals m.MembreId
                               where  ep.OrganisationId==idorg
                               select new
                               {
                                   
                                   id = ep.DomaineResourceId,
                                   RessourceId = ep.RessourceId,
                                   DomaineInterventionId = ep.DomaineInterventionId,

                                   DateCreation = ep.DateCreation,
                                   statut = ep.statut,
                                   MembreId = ep.MembreId,
                                   PostTitre=r.Titre,
                                   PostDom=dom.Libelle,
                                   PostCateg=r.typeressources,


                               }).ToList();

                    GV_apv.DataSource = obj;
                    GV_apv.DataBind();

                }


            }
        }

        public void search(GridView GV_apv, string code, long id, long idorg, long codere, int cd)
        {
            using (Connection con = new Connection())
            {

                var obj = (from ep in con.DomaineRessources
                           join r in con.Ressources on ep.RessourceId equals r.RessourceId
                           join d in con.DomaineInterventionOrganisations on ep.DomaineInterventionId equals d.DomaineInterventionOrganisationId
                           join m in con.Membres on ep.MembreId equals m.MembreId
                           where (ep.RessourceId.ToString().Contains(code) && ep.MembreId.ToString().Contains(code)) && ep.OrganisationId==idorg
                           select new
                           {
                               DomaineResourceId = ep.DomaineResourceId,
                               RessourceId = ep.RessourceId,
                               DomaineInterventionId = ep.DomaineInterventionId,

                               DateCreation = ep.DateCreation,
                               statut = ep.statut,
                               MembreId = ep.MembreId,


                           }).ToList();

                GV_apv.DataSource = obj;
                GV_apv.DataBind();

            }
        }

        public void afficherDetails(long id,long idorg,long codere,int cd, DomaineRessource_Class pr)
        {
            using (Connection con = new Connection())
            {
                if (cd==0)
                {
                    p = con.DomaineRessources.Where(x => x.DomaineResourceId == id && x.OrganisationId==idorg).FirstOrDefault();

                    if (p != null)
                    {
                        pr.DomaineResourceId = p.DomaineResourceId;
                        pr.RessourceId = p.RessourceId;
                        pr.DomaineInterventionId = p.DomaineInterventionId;
                        pr.DateCreation = p.DateCreation;
                        pr.statut = p.statut;
                        pr.MembreId = p.MembreId;
                        pr.OrganisationId = p.OrganisationId;

                    }


                }

            }
        }

        public void afficherDetails(string code, DomaineRessource_Class pr)
        {
            // Implementation logic here
        }

        public int edit(DomaineRessource_Class cl, long id,long idorg)
        {
            using (Connection con = new Connection())
            {
                p = con.DomaineRessources.Where(x => x.DomaineResourceId == id && x.OrganisationId==idorg).FirstOrDefault();

                try
                {
                    p.RessourceId = cl.RessourceId;
                    p.DomaineInterventionId = cl.DomaineInterventionId;
                    p.statut = cl.statut;
                    p.MembreId = cl.MembreId;

                    if (con.SaveChanges() == 1)
                    {
                        con.DomaineRessources.Add(p);
                        con.Entry(p).State = EntityState.Modified;
                        return msg = 1;
                    }
                    else
                        return msg = 0;
                }
                catch (Exception e)
                {

                }
                return msg;
            }
        }

        public int supprimer(long id, long idorg)
        {
            using (Connection con = new Connection())
            {

                p = con.DomaineRessources.Where(x => x.DomaineResourceId == id && x.OrganisationId==id).First();

                if (con.Entry(p).State == EntityState.Detached)
                    con.DomaineRessources.Attach(p);

                con.DomaineRessources.Remove(p);
                con.SaveChanges();

                return msg = 1;
            }
        }

        public void chargerDomainRessourc(DropDownList lst)
        {
            // Implementation logic here
        }

        public int count()
        {
            int n = 0;
            using (Connection con = new Connection())
            {
                var b = (from l in con.DomaineRessources
                         select l).Count();
                n = b;
            }
            return n;
        }
    }
}