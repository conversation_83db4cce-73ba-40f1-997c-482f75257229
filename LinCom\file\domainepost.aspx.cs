﻿using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom.file
{
    public partial class domainepost : System.Web.UI.Page
    {

        private int info;
        int financementId;
        DomainePost_Class actpro = new DomainePost_Class();
        DomainePost_Class actproj = new DomainePost_Class();
        IDomainePost obj = new DomainePostImp();
        ICommonCode co = new CommonCode();
        IPoste objpos = new PosteImp();
        Post_Class pos = new Post_Class();

        IOrganisation objOrganisation = new OrganisationImp();
        Organisation_Class org = new Organisation_Class();
        MembresOrganisation_Class memorg = new MembresOrganisation_Class();
        MembresOrganisation_Class memorga = new MembresOrganisation_Class();
        IMembresOrganisation objmemorg = new MembresOrganisationImp();
        DomaineRessource_Class actco = new DomaineRessource_Class();
        IDomaineRessource objactco = new DomaineRessourceImp();
        DomaineInterventionOrganisation_Class domai = new DomaineInterventionOrganisation_Class();
        IDomaineInterventionOrganisation objdom = new DomaineInterventionOrganisationImp();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        CategoriePost_Class catpost = new CategoriePost_Class();
        ICategoryPost objcatpost = new CategoriePostImp();

        static string imge, imge1, pdfe, nameorg;
        static long ide, idorg;
        static int rolid;
        long index;
        static string nsco;

        protected void Page_Load(object sender, EventArgs e)
        {
            nsco = Request.QueryString["id"];
            financementId = Convert.ToInt32(nsco);
            if (!IsPostBack)
            {
                HttpCookie role = Request.Cookies["role"];
                HttpCookie usernm = Request.Cookies["usernm"];
                HttpCookie idperso = Request.Cookies["iduser"];

                if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
                {//admin
                    ide = Convert.ToInt64(idperso.Value);//idconnecte
                    rolid = Convert.ToInt32(role.Value);//roleconnecte

                }
                else Response.Redirect("~/login.aspx");

                objmemorg.AfficherDetails(Convert.ToInt64(ide), memorg, 1);
                idorg = Convert.ToInt64(memorg.OrganisationId);
                objOrganisation.AfficherDetails(Convert.ToInt64(idorg), org);
                nameorg = org.Nom;

                InitialiserMessages();
                objdom.chargerDomaineInterventionOrganisation(drpddomai,idorg);
                objcatpost.chargerCategoriePost(drpdcateg);

                // Vérifier si un ID est passé en paramètre pour l'édition
                if (nsco != null)
                {

                    btnEnregistrer.InnerText = "Modifier";
                    AfficherDetails();
                }
                else
                {
                    btnEnregistrer.InnerText = "Enregistrer";
                }
            }
        }

        private void InitialiserMessages()
        {
            div_msg_succes.Visible = false;
            div_msg_error.Visible = false;
        }


        protected void btn_ajouter_ServerClick(object sender, EventArgs e)
        {
            if (nsco != null)
            {
                ModifierDomaineService();
            }
            else
            {
                AjouterDomaineService();
            }
        }
      

        private void AjouterDomaineService()
        {
            try
            {
                if (drpdservice.SelectedValue == "-1" ||
                    drpddomai.SelectedValue == "-1" || drpdstatut.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }

                actproj.PostId = Convert.ToInt64(drpdservice.SelectedValue);
                actproj.DomaineInterventionOrganisationId =Convert.ToInt32( drpddomai.SelectedValue);
                actproj.DateCreation = DateTime.Now;
                actproj.statut = drpdstatut.SelectedValue;
                actproj.MembreId = ide;
                actproj.OrganisationId = idorg;
              
                info = obj.Ajouter(actproj);

                if (info == 1)
                {
                    div_msg_succes.Visible = true;
                    msg_succes.InnerText = "Le domaine d'intervention du Post a été enregistré avec succès";
                    ReinitialiserFormulaire();
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de l'enregistrement de l'activité";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }

        protected void drpdcateg_SelectedIndexChanged(object sender, EventArgs e)
        {
           
                objpos.chargerPost(drpdservice, -1, idorg, co.GenerateSlug(drpdcateg.SelectedItem.ToString()), "Selectionner le " + drpdcateg.SelectedItem.ToString(), 0);
                lblpost.InnerText = "Titre du " + drpdcateg.SelectedItem.ToString();
           
            }

        private void ModifierDomaineService()
        {
            try
            {
                if (drpdservice.SelectedValue == "-1" ||
                     drpddomai.SelectedValue == "-1" || drpdstatut.SelectedValue == "-1")
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Veuillez remplir tous les champs obligatoires";
                    return;
                }


                actproj.PostId = Convert.ToInt64(drpdservice.SelectedValue);
                actproj.DomaineInterventionOrganisationId = Convert.ToInt32(drpddomai.SelectedValue);
                actproj.statut = drpdstatut.SelectedValue;
                actproj.MembreId = ide;

                info = obj.Modifier(actproj, Convert.ToInt64(nsco), idorg, 0);

                if (info == 1)
                {
                    Response.Redirect("~/file/listdomainepost.aspx");
                }
                else
                {
                    div_msg_error.Visible = true;
                    msg_error.InnerText = "Erreur lors de la modification";
                }
            }
            catch (SqlException ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Erreur SQL: " + ex.Message;
            }
            catch (Exception ex)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Une erreur s'est produite: " + ex.Message;
            }
        }
        protected void btnEnregistrer_ServerClick(object sender, EventArgs e)
        {
            // Vérifier la validation côté serveur
            if (!Page.IsValid)
            {
                div_msg_error.Visible = true;
                msg_error.InnerText = "Veuillez corriger les erreurs avant de continuer.";
                return;
            }

            if (nsco == null)
            {
                AjouterDomaineService();
            }
            else
            {
                ModifierDomaineService();
            }
        }
        private void AfficherDetails()
        {
            if (Convert.ToInt64(nsco) > 0)
            {
                obj.AfficherDetails(actproj,Convert.ToInt64(nsco), idorg, -1, 0);//
               

                if (actproj.DomainePostId > 0)
                {
                    objdom.AfficherDetails(Convert.ToInt32(actproj.DomaineInterventionOrganisationId), domai, idorg, 0);
                    objactdom.AfficherDetails(Convert.ToInt32(domai.DomaineInterventionId), actdom);
                    objpos.AfficherDetails(Convert.ToInt64(actproj.PostId),idorg,0,pos);

                    drpdservice.SelectedValue = actproj.PostId.ToString();
                    drpddomai.SelectedValue = actproj.DomaineInterventionOrganisationId.ToString();
                    drpdstatut.SelectedValue = actproj.statut;

                    drpdcateg.SelectedValue = pos.CategoriePostId.ToString();

                    objpos.chargerPost(drpdservice, -1, idorg, co.GenerateSlug(drpdcateg.SelectedItem.ToString()), "Selectionner le " + drpdcateg.SelectedItem.ToString(), 0);
                    lblpost.InnerText = "Titre du " + drpdcateg.SelectedItem.ToString();
                    drpdservice.SelectedValue = actproj.PostId.ToString();


                }
            }
        }

        private void ReinitialiserFormulaire()
        {
            drpdservice.SelectedValue = "-1";
            drpddomai.SelectedValue = "-1";
            drpdstatut.SelectedValue = "-1";
           

        }
    }
}